package com.bestseller.services.orderrouting.event.handler;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.read.ListAppender;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsCreated.OrderPartsCreated;
import com.bestseller.services.orderrouting.event.AvailableInventoryEvent;
import com.bestseller.services.orderrouting.messaging.producer.OrderPartsCreatedProducer;
import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.model.OrderModel;
import com.bestseller.services.orderrouting.utils.generator.OrderModelGenerator;
import com.bestseller.services.orderrouting.utils.generator.OrderPartsCreatedGenerator;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.LoggerFactory;
import org.springframework.core.convert.converter.Converter;
import org.springframework.messaging.MessagingException;

import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class OrderPartsCreatedHandlerTest {
    private static final String FULFILLMENT_CENTER_EAST = "INGRAM_MICRO";
    private static final String SHIP_FROM_STORE = "SHIP_FROM_STORE";

    @Mock
    private OrderPartsCreatedProducer orderPartsCreatedProducer;

    @Mock
    private Converter<OrderModel, List<OrderPartsCreated>> converter;

    @Mock
    private ListAppender<ILoggingEvent> appender;

    @InjectMocks
    private OrderPartsCreatedHandler listener;

    @Captor
    private ArgumentCaptor<OrderPartsCreated> captor;

    private AvailableInventoryEvent event;
    private Logger logger;

    @Before
    public void setUp() {
        logger = (Logger) LoggerFactory.getLogger(listener.getClass());
        appender = new ListAppender<>();
        appender.start();

        OrderModel orderModel = OrderModelGenerator.createTestOrderModel();

        List<OrderLineQuantityModel> orderLines = OrderModelGenerator.createThreeTestOrderLines(orderModel);
        orderLines.get(0).setFulfillmentNode(FULFILLMENT_CENTER_EAST);
        orderLines.get(1).setFulfillmentNode(FULFILLMENT_CENTER_EAST);
        orderLines.get(2).setFulfillmentNode(SHIP_FROM_STORE);

        orderModel.setOrderLineQuantities(orderLines);

        event = new AvailableInventoryEvent(this, orderModel);

        OrderPartsCreated orderPartsCreated1 = OrderPartsCreatedGenerator.createMessage(orderModel.getOrderId(),
                Arrays.asList(orderLines.get(0).getEan(), orderLines.get(1).getEan()));

        OrderPartsCreated orderPartsCreated2 = OrderPartsCreatedGenerator.createMessage(orderModel.getOrderId(),
                Arrays.asList(orderLines.get(2).getEan()));

        when(converter.convert(orderModel)).thenReturn(Arrays.asList(orderPartsCreated1, orderPartsCreated2));
    }

    @After
    public void tearDown() {
        logger.detachAppender(appender);
    }

    @Test
    public void handleAvailableInventory_orderPartsConverted_orderPartsCreatedSend() {
        // act
        listener.handleAvailableInventory(event);

        // assert
        verify(orderPartsCreatedProducer, times(2)).produce(captor.capture());

        // assert
        assertThat(appender.list).isEmpty();
    }

    @Test
    public void handleAvailableInventory_exceptionOccurred_messagesAreLogged() {
        // arrange
        logger.addAppender(appender);

        doThrow(new MessagingException("")).when(orderPartsCreatedProducer).produce(any());

        // act
        listener.handleAvailableInventory(event);

        // assert
        assertThat(appender.list)
                .extracting(ILoggingEvent::getLevel)
                .containsExactly(Level.ERROR, Level.ERROR, Level.ERROR, Level.ERROR);
    }
}
