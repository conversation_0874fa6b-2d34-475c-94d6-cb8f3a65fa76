package com.bestseller.services.orderrouting.jobs;

import com.bestseller.services.orderrouting.metric.KafkaMessageOperation;
import com.bestseller.services.orderrouting.repository.OrderModelRepository;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.context.ApplicationEvent;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.Clock;
import java.time.Instant;

import static java.time.temporal.ChronoUnit.DAYS;
import static org.hamcrest.Matchers.equalTo;
import static org.junit.Assert.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class CleanUpOrderTaskTest {

    private static final double ORDER_NUMBER = 42;

    @Mock
    private OrderModelRepository orderModelRepository;

    @Spy
    private MeterRegistry meterRegistry = new SimpleMeterRegistry();

    @Mock
    private Clock utcClock;

    @InjectMocks
    private CleanUpOrderTask cleanUpOrderTask;

    private Instant currentTime = Instant.parse("2019-11-01T13:00:00Z");

    private long cleanupPeriodInDays;

    private Instant cleanUpTime = Instant.parse("2019-10-01T13:00:00Z");

    @Before
    public void setUp() {
        cleanupPeriodInDays = DAYS.between(cleanUpTime, currentTime);

        ReflectionTestUtils.setField(cleanUpOrderTask, "cleanupPeriodInDays", cleanupPeriodInDays);

        when(utcClock.instant()).thenReturn(currentTime);
    }

    @Test
    public void removeOldOrders_orderFoundForCleanUp_taskStarted() {
        // arrange
        var captor = ArgumentCaptor.forClass(ApplicationEvent.class);

        when(orderModelRepository.countCreatedDateBeforeAndAllOrderLineStatusesRoutedOrCancelled(cleanUpTime))
                .thenReturn((int) ORDER_NUMBER);

        // act
        cleanUpOrderTask.removeOldRecords();

        // assert
        verify(orderModelRepository).deleteCreatedDateBeforeAndAllOrderLineStatusesRoutedOrCancelled(cleanUpTime);

        verify(meterRegistry).counter(KafkaMessageOperation.ORDER_CLEANUP_COUNT.name());

        assertThat("Meter is incremented", meterRegistry.counter(KafkaMessageOperation.ORDER_CLEANUP_COUNT.name()).count(),
                equalTo(ORDER_NUMBER));

    }

    @Test
    public void removeOldOrders_noOrderFoundForCleanUp_taskStarted() {
        // arrange
        when(orderModelRepository.countCreatedDateBeforeAndAllOrderLineStatusesRoutedOrCancelled(cleanUpTime))
                .thenReturn(0);

        // act
        cleanUpOrderTask.removeOldRecords();

        // assert
        verifyNoInteractions(meterRegistry);
    }
}
