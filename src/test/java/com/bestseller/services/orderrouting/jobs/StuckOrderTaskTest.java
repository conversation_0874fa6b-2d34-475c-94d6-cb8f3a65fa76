package com.bestseller.services.orderrouting.jobs;

import com.bestseller.services.orderrouting.model.OrderModel;
import com.bestseller.services.orderrouting.repository.OrderModelRepository;
import com.bestseller.services.orderrouting.rules.facts.FactNames;
import com.bestseller.services.orderrouting.service.RulesService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Value;

import java.time.Clock;
import java.time.Duration;
import java.time.Instant;
import java.time.ZoneOffset;
import java.util.List;

import static com.bestseller.services.orderrouting.model.OrderLineQuantityStatus.PLACED;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class StuckOrderTaskTest {

    @Mock
    private OrderModelRepository orderModelRepository;

    @Mock
    private RulesService rulesService;

    private final Clock utcClock = Clock.fixed(Instant.parse("2024-11-13T14:00:00.000Z"), ZoneOffset.UTC);

    @Value("${order.stuck.job.from.duration}")
    private Duration fromDuration = Duration.ofHours(24);

    @Value("${order.stuck.job.to.duration}")
    private Duration toDuration = Duration.ofHours(1);

    private StuckOrderTask stuckOrderTask;

    @Before
    public void beforeEach() {
        stuckOrderTask = new StuckOrderTask(orderModelRepository, rulesService, utcClock, fromDuration, toDuration);
    }

    @Test
    public void requestItemAvailabilityAgain_givenOrderStuck_executeOrderPlacingRulesShouldBeCalledTwice() {
        // Mock current time
        Instant from = Instant.parse("2024-11-12T14:00:00Z");
        Instant to = Instant.parse("2024-11-13T13:00:00Z");

        // Mock repository to return some orders
        OrderModel order1 = OrderModel.builder()
            .orderId("order1")
            .build();
        OrderModel order2 = OrderModel.builder()
            .orderId("order2")
            .build();
        when(orderModelRepository.getOrdersInStatus(PLACED, from, to)).thenReturn(List.of(order1, order2));

        // Call the method to test
        stuckOrderTask.requestItemAvailabilityAgain();

        // Verify interactions
        verify(orderModelRepository).getOrdersInStatus(PLACED, from, to);
        verify(rulesService, times(2)).executeOrderPlacingRules(any());

        // Optionally, verify the content of `Facts` passed to `rulesService.executeOrderPlacingRules`
        verify(rulesService).executeOrderPlacingRules(argThat(facts ->
            facts.asMap().containsKey(FactNames.ORDER_MODEL_FACT)
                && facts.asMap().get(FactNames.ORDER_MODEL_FACT).equals(order1)
        ));
        verify(rulesService).executeOrderPlacingRules(argThat(facts ->
            facts.asMap().containsKey(FactNames.ORDER_MODEL_FACT)
                && facts.asMap().get(FactNames.ORDER_MODEL_FACT).equals(order2)
        ));
    }

    @Test
    public void requestItemAvailabilityAgain_givenNoOrderStuck_executeOrderPlacingRulesShouldNeverBeCalled() {
        // Mock current time
        Instant from = Instant.parse("2024-11-12T14:00:00Z");
        Instant to = Instant.parse("2024-11-13T13:00:00Z");

        // Mock repository to return an empty list (no stuck orders)
        when(orderModelRepository.getOrdersInStatus(PLACED, from, to)).thenReturn(List.of());

        // Call the method to test
        stuckOrderTask.requestItemAvailabilityAgain();

        // Verify interactions
        verify(orderModelRepository).getOrdersInStatus(PLACED, from, to);
        verifyNoInteractions(rulesService);  // No rules service call if no orders found
    }
}
