package com.bestseller.services.orderrouting.converter;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailabilityRequest;
import com.bestseller.services.orderrouting.model.OrderModel;
import com.bestseller.services.orderrouting.utils.generator.OrderModelGenerator;
import com.bestseller.services.orderrouting.utils.verifier.CollectionVerifier;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;

@RunWith(MockitoJUnitRunner.class)
public class ItemAvailabilityRequestConverterTest {

    @InjectMocks
    private ItemAvailabilityRequestConverter itemAvailabilityRequestConverter;

    private OrderModel orderModel;
    private List<String> orderLineEANs;

    @Before
    public void setUp() {
        orderLineEANs = new ArrayList<>();
        orderModel = OrderModelGenerator.createTestOrderModel();
        orderModel.setPayments(OrderModelGenerator.createTwoPayments(orderModel));
        orderModel.setOrderLineQuantities(OrderModelGenerator.createThreeTestOrderLines(orderModel));
        orderModel.getOrderLineQuantities().forEach(q -> orderLineEANs.add(q.getEan()));
    }

    @Test
    public void convert_orderModelGiven_itemAvailabilityRequestGenerated() {
        // arrange - done in setup

        // act
        ItemAvailabilityRequest itemAvailabilityRequest = itemAvailabilityRequestConverter.convert(orderModel);

        // assert
        assertEquals("EANs should match", new CollectionVerifier<>(orderLineEANs),
                new CollectionVerifier<>(itemAvailabilityRequest.getEans()));
        assertEquals("Order id should match", orderModel.getOrderId(), itemAvailabilityRequest.getCorrelationId());
    }
}
