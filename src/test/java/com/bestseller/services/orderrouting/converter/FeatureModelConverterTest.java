package com.bestseller.services.orderrouting.converter;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;
import org.togglz.core.repository.FeatureState;

import com.bestseller.services.orderrouting.feature.toggles.ORSFeatures;
import com.bestseller.services.orderrouting.model.FeatureModel;

@RunWith(MockitoJUnitRunner.class)
public class FeatureModelConverterTest {

    private static final String PARAMETER_KEY = "key";
    private static final String PARAMETER_VALUE = "value";
    private static final String ACTIVATION_STRATEGY_ID = "activation";

    @InjectMocks
    private FeatureModelConverter featureModelConverter;

    @Test
    public void convert_givenValidFeatureModel_expectValidFeatureState() {
        // arrange
        final FeatureModel featureModel = new FeatureModel();
        featureModel.setName(ORSFeatures.ROUTE_ORDERS.name());
        featureModel.setEnabled(true);
        featureModel.setStrategyId(ACTIVATION_STRATEGY_ID);

        // act
        final FeatureState state = featureModelConverter.convert(featureModel);

        // assert
        assertEquals("Should set the feature name to the model name", state.getFeature().name(), featureModel.getName());
        assertEquals("Should set the feature enabled state to the model enabled state", state.isEnabled(), featureModel.getEnabled());

        assertEquals("Should set the feature strategy ID to the model strategy ID", state.getStrategyId(), featureModel.getStrategyId());
    }

    @Test
    public void convert_givenFeatureModelWithoutParameters_expectValidFeatureStateWithoutParameters() {
        // arrange
        final FeatureModel featureModel = new FeatureModel();
        featureModel.setName(ORSFeatures.ROUTE_ORDERS.name());

        // act
        final FeatureState state = featureModelConverter.convert(featureModel);

        // assert
        assertTrue("Should set the parameter list to empty", state.getParameterMap().isEmpty());
    }

    @Test
    public void convert_givenFeatureModelWithoutStrategyId_expectValidFeatureStateWithoutStrategyId() {
        // arrange
        final FeatureModel featureModel = new FeatureModel();
        featureModel.setName(ORSFeatures.ROUTE_ORDERS.name());

        // act
        final FeatureState state = featureModelConverter.convert(featureModel);

        // assert
        assertEquals("Should have no strategy ID", state.getStrategyId(), featureModel.getStrategyId());
        assertNull("Should set the strategy ID to null", state.getStrategyId());
    }
}
