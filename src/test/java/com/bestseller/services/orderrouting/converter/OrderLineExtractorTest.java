package com.bestseller.services.orderrouting.converter;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartRejected.OrderPartRejected;
import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.model.OrderModel;
import com.bestseller.services.orderrouting.utils.generator.OrderModelGenerator;
import com.bestseller.services.orderrouting.utils.generator.OrderPartRejectedGenerator;
import org.apache.commons.lang.math.NumberUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;


/**
 * Unit tests for {@link OrderLineExtractor}.
 */
@RunWith(MockitoJUnitRunner.class)
public class OrderLineExtractorTest {
    private String firstEan;
    private OrderModel order;
    private OrderPartRejected orderPartRejected;

    @InjectMocks
    private OrderLineExtractor orderLineExtractor;

    @Before
    public void setUp() {
        order = OrderModelGenerator.createTestOrderModel();
        List<OrderLineQuantityModel> orderLineQuantities = OrderModelGenerator.createThreeTestOrderLines(order);

        order.setOrderLineQuantities(orderLineQuantities);

        orderPartRejected = OrderPartRejectedGenerator.createFullOrderPartRejectedMessage();
        firstEan = orderLineQuantities.get(1).getEan();
        orderPartRejected.getOrderLines().get(0).setEan(firstEan);
    }

    @Test
    public void extractOrderLinesFromMessage_orderRoutedToWarehouse_chainFactBroken() {
        // act
        List<OrderLineQuantityModel> orderLineQuantities = orderLineExtractor.extractOrderLinesFromMessage(order, orderPartRejected);

        // assert
        assertThat("Order line quantities size should be one", orderLineQuantities.size(), is(NumberUtils.INTEGER_ONE));

        OrderLineQuantityModel orderLineQuantityModel = orderLineQuantities.get(0);

        assertThat("EAN should match", orderLineQuantityModel.getEan(), is(firstEan));
    }
}
