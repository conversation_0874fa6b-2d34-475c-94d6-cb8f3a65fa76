package com.bestseller.services.orderrouting.converter;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.common.AdditionalInformation;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.Address;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.Payment;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderForFulfillment.OrderForFulfillment;
import com.bestseller.services.orderrouting.model.AdditionalOrderInformationModel;
import com.bestseller.services.orderrouting.model.AddressModel;
import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.model.OrderLineQuantityStatus;
import com.bestseller.services.orderrouting.model.OrderModel;
import com.bestseller.services.orderrouting.model.PaymentModel;
import com.bestseller.services.orderrouting.service.EntityType;
import com.bestseller.services.orderrouting.utils.generator.OrderModelGenerator;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import static com.bestseller.services.orderrouting.utils.generator.OrderForFulfillmentGenerator.createFullOrderForFulfillmentMessage;
import static org.hamcrest.Matchers.contains;
import static org.hamcrest.Matchers.empty;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.not;
import static org.hamcrest.Matchers.nullValue;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertThat;
import static org.junit.Assert.fail;

/**
 * Sets up an isolated Spring context in order to verify correctness of class annotations.
 */
@RunWith(SpringRunner.class)
@Import(OrderConverter.class)
@TestPropertySource(properties = "ORDER_TIME_TO_LIVE_SECONDS=100500")
public class OrderConverterTest {

    private static final String ISO_STORE_ID = "890909";

    @Autowired
    private OrderConverter orderConverter;

    private OrderModel orderModel;

    @Before
    public void setUp() {
        orderModel = OrderModelGenerator.createTestOrderModel();
        orderModel.setIsoStoreId(ISO_STORE_ID);
        orderModel.setOrderLineQuantities(OrderModelGenerator.createThreeTestOrderLines(orderModel));
    }

    @Test
    public void convert_givenValidMessage_expectValidModel() {
        // arrange
        final OrderForFulfillment message = createFullOrderForFulfillmentMessage();

        // act
        final OrderModel model = orderConverter.convert(message);

        // assert
        validateMapping(message, model);
    }

    @Test
    public void convert_givenNullZonedDateTime_expectNullInstant() {
        // arrange
        final OrderForFulfillment message = createFullOrderForFulfillmentMessage();
        message.setPlacedDate(null);
        message.getOrderDetails().setOrderCreationDate(null);

        // act
        final OrderModel model = orderConverter.convert(message);

        // assert
        assertNotNull("Model can't be null", model);
        assertNull("Placed date can't be null", model.getPlacedDate());
        assertNull("Order creation date can't be null", model.getOrderCreationDate());
    }

    @Test
    public void convert_givenValidMessageWithoutStoreID_expectValidModelWithoutStoreId() {
        // arrange
        OrderForFulfillment message = createFullOrderForFulfillmentMessage();
        message.getShippingInformation().getShippingAddress().setStoreId(null);
        message.getCustomerInformation().getBillingAddress().setStoreId(null);

        // act
        OrderModel model = orderConverter.convert(message);

        // assert
        validateMapping(message, model);
    }

    @Test
    public void convert_givenValidTestMessage_expectValidModel() {
        // arrange
        final OrderForFulfillment message = createFullOrderForFulfillmentMessage();
        message.setIsTest(true);

        // act
        final OrderModel model = orderConverter.convert(message);

        // assert
        assertFalse("Is advice is false", model.getIsOrderAdvice());
    }

    @Test
    public void convertRoutedLines_empty_expectedEmpty() {
        //arrange

        // act
        List<OrderLineQuantityModel> orderLineQuantityModels = orderConverter.convertRoutedLines(Collections.emptyList());

        //assert
        assertThat("Order lines should be empty", orderLineQuantityModels, empty());
    }

    @Test
    public void convertRoutedLines_valid_expectedSameValues() {
        //arrange
        BigDecimal price = BigDecimal.valueOf(Math.PI);
        Integer qty = Integer.MIN_VALUE;
        OrderLine orderLine = new OrderLine();
        orderLine.setDiscountValue(BigDecimal.TEN);
        orderLine.setRetailPrice(price);
        orderLine.setQuantity(qty);

        List<OrderLine> orderLines = Collections.singletonList(orderLine);


        //act
        List<OrderLineQuantityModel> orderLineQuantityModels = orderConverter.convertRoutedLines(orderLines);

        //assert
        assertThat("Order line quantity should have 1 item", orderLineQuantityModels, hasSize(1));
        assertThat("Order lines are not empty", orderLineQuantityModels, not(contains(nullValue())));

        OrderLineQuantityModel line = orderLineQuantityModels.get(0);
        assertThat("Discount value should match", line.getDiscountValue(), equalTo(BigDecimal.TEN));
        assertThat("Retail price should match", line.getRetailPrice(), equalTo(price));
        assertThat("Quantity should match", line.getQuantity(), equalTo(qty));
    }

    @Test
    public void deepCopyOrderModel_validOrder_expectedOrderCopy() {
        //arrange

        // act
        OrderModel copy = orderConverter.deepCopyOrderModel(orderModel);

        //assert
        assertThat("Copy order has same values", copy, equalTo(orderModel));
    }

    @Test
    public void deepCopyOrderLineQuantityModel_validOrderLine_expectedOrderLineCopy() {
        //arrange

        // act
        OrderLineQuantityModel copy = orderConverter.deepCopyOrderQuantityModel(orderModel.getOrderLineQuantities().get(0));

        //assert
        assertThat("Copy order has same values", copy, equalTo(orderModel.getOrderLineQuantities().get(0)));
    }

    private void validateMapping(OrderForFulfillment message, OrderModel model) {
        assertEquals("Order id should match", message.getOrderId(), model.getOrderId());
        assertEquals("Is test should match", message.getIsTest(), model.getIsTest());
        assertEquals("Placed date should match", message.getPlacedDate().toInstant(), model.getPlacedDate());
        assertEquals("Market place should match", message.getMarketPlace(), model.getMarketPlace());
        assertEquals("Store should match", message.getStore(), model.getStore());
        assertEquals("Channel should match", message.getChannel(), model.getChannel());
        assertEquals("Brand should match", message.getBrand(), model.getBrand());
        assertEquals("Action code should match", message.getActionCode(), model.getActionCode());
        assertEquals("Carrier should match", message.getOrderDetails().getCarrier(), model.getCarrier());
        assertEquals("Shipping fees should match", message.getOrderDetails().getShippingFees(),
                     model.getShippingFees());
        assertEquals("Shipping feed tax percentage should match",
                     message.getOrderDetails().getShippingFeesTaxPercentage(), model.getShippingFeesTaxPercentage());
        assertEquals("Shipping method should match", message.getOrderDetails().getShippingMethod(),
                     model.getShippingMethod());
        assertEquals("External order should match", message.getOrderDetails().getExternalOrderNumber(),
                     model.getExternalOrderNumber());
        assertEquals("Order creation date should match", message.getOrderDetails().getOrderCreationDate().toInstant(),
                     model.getOrderCreationDate());
        assertEquals("Order type should match", message.getOrderDetails().getOrderType(), model.getOrderType());
        assertEquals("Order value should match", message.getOrderDetails().getOrderValue(), model.getOrderValue());
        assertEquals("Carrier variant should match", message.getOrderDetails().getCarrierVariant(),
                     model.getCarrierVariant());
        assertEquals("Shipping fees cancelled should match", message.getOrderDetails().getShippingFeesCancelled(),
                     model.getShippingFeesCancelled());
        assertEquals("IsoStoreId should match", message.getOrderDetails().getIsoStoreId(),
            model.getIsoStoreId());

        assertEquals("Parcel locker should match", message.getShippingInformation().getParcelLocker(),
                model.getParcelLocker());

        validateAdditionalInformationSet(message.getShippingInformation().getAdditionalInformation(), model.getAdditionalOrderInformation());
        validateAddresses(message.getShippingInformation().getShippingAddress(), model.getShippingAddress());

        assertEquals("Email should match", message.getCustomerInformation().getEmail(), model.getCustomerEmail());
        assertEquals("External customer number should match",
                     message.getCustomerInformation().getExternalCustomerNumber(), model.getExternalCustomerNumber());
        assertEquals("Customer Id should match", message.getCustomerInformation().getCustomerId(),
                     model.getCustomerId());
        validateAddresses(message.getCustomerInformation().getBillingAddress(), model.getBillingAddress());

        validateOrderLines(message.getOrderLines(), model.getOrderLineQuantities());

        validatePayments(message.getPayments(), model.getPayments());
    }

    private void validateOrderLines(final List<OrderLine> orderLines,
                                    final List<OrderLineQuantityModel> orderLineQuantities) {
        assertEquals("Size should match", orderLines.size(), orderLineQuantities.size());
        for (OrderLine ol : orderLines) {
            OrderLineQuantityModel olqm = findOrderLineQuantity(ol.getLineNumber(), orderLineQuantities);
            validateOrderLine(ol, olqm);
        }
    }

    private OrderLineQuantityModel findOrderLineQuantity(final Integer lineNumber, final List<OrderLineQuantityModel> orderLineQuantities) {
        // find first line with this line number
        Optional<OrderLineQuantityModel> optional = orderLineQuantities.stream()
                .filter(x -> lineNumber.equals(x.getLineNumber()))
                .findFirst();

        // check whether optional has element you are looking for.
        if (optional.isPresent()) {
            // return found object.
            return optional.get();
        } else {
            // no such line number found - fail!
            fail("No such line number found");
            return null;
        }
    }

    private void validateOrderLine(final OrderLine orderline, OrderLineQuantityModel orderLineQuantity) {
        assertEquals("EAN should match", orderline.getEan(), orderLineQuantity.getEan());
        assertEquals("Product name should match", orderline.getProductName(), orderLineQuantity.getProductName());
        assertEquals("Line number should match", orderline.getLineNumber(), orderLineQuantity.getLineNumber());
        assertEquals("Quantity should match", orderline.getQuantity(), orderLineQuantity.getQuantity());
        assertEquals("Retail price should match", orderline.getRetailPrice(), orderLineQuantity.getRetailPrice());
        assertEquals("Discount value should match", orderline.getDiscountValue(), orderLineQuantity.getDiscountValue());
        assertEquals("Tax percentage should match", orderline.getTaxPercentage(), orderLineQuantity.getTaxPercentage());
        assertEquals("Is gift item should match", orderline.getIsGiftItem(), orderLineQuantity.getIsGiftItem());
        assertEquals("Partner reference should match", orderline.getPartnerReference(),
                     orderLineQuantity.getPartnerReference());
        assertEquals("Brand should match", orderline.getBrand(), orderLineQuantity.getBrand());

        // assert routing fields are not populated
        assertNull("Fulfillment node should be null", orderLineQuantity.getFulfillmentNode());
        assertNull("Order part number should be null", orderLineQuantity.getOrderPartNumber());

        // assert status is PLACED
        assertEquals("Status should be PLACED", OrderLineQuantityStatus.PLACED, orderLineQuantity.getStatus());
    }

    private void validateAddresses(final Address address, final AddressModel addressModel) {
        assertEquals("Address line 1 should match", address.getAddressLine1(), addressModel.getAddressLine1());
        assertEquals("Address line 2 should match", address.getAddressLine2(), addressModel.getAddressLine2());
        assertEquals("Address line 3 should match", address.getAddressLine3(), addressModel.getAddressLine3());
        assertEquals("City should match", address.getCity(), addressModel.getCity());
        assertEquals("Country should match", address.getCountry(), addressModel.getCountry());
        assertEquals("Zip code should match", address.getZipcode(), addressModel.getZipcode());
        assertEquals("House number should match", address.getHouseNumber(), addressModel.getHouseNumber());
        assertEquals("House number extended should match", address.getHouseNumberExtended(),
                     addressModel.getHouseNumberExtended());
        assertEquals("Phone number should match", address.getPhoneNumber(), addressModel.getPhoneNumber());
        assertEquals("First name should match", address.getFirstName(), addressModel.getFirstName());
        assertEquals("Last name should match", address.getLastName(), addressModel.getLastName());
        assertEquals("Store Id should match", address.getStoreId(), addressModel.getStoreId());
    }

    private void validatePayment(Payment payment, PaymentModel paymentModel) {
        assertEquals("Payment name should match", payment.getName(), paymentModel.getName());
        assertEquals("Payment sub method should match", payment.getSubMethod(), paymentModel.getSubMethod());
    }

    private void validatePayments(List<Payment> payments, List<PaymentModel> paymentModels) {
        assertEquals("Size should match", payments.size(), paymentModels.size());
        Iterator<Payment> paymentIterator = payments.iterator();
        Iterator<PaymentModel> paymentModelIterator = paymentModels.iterator();
        while (paymentIterator.hasNext()) {
            validatePayment(paymentIterator.next(), paymentModelIterator.next());
        }
    }

    private void validateAdditionalInformationSet(Set<AdditionalInformation> additionalInformationSet,
                                                  List<AdditionalOrderInformationModel> additionalOrderInformationModelSet) {
        assertEquals("Size should match", additionalInformationSet.size(), additionalOrderInformationModelSet.size());
        Iterator<AdditionalInformation> additionalInformationIterator = additionalInformationSet.iterator();
        Iterator<AdditionalOrderInformationModel> additionalInformationModelIterator = additionalOrderInformationModelSet.iterator();
        while (additionalInformationIterator.hasNext()) {
            validateAdditionalInformation(additionalInformationIterator.next(), additionalInformationModelIterator.next());
        }
    }

    private void validateAdditionalInformation(
        AdditionalInformation additionalInformation,
        AdditionalOrderInformationModel additionalOrderInformationModel) {
        assertEquals("Entity type should match", EntityType.SHIPPING_INFORMATION, additionalOrderInformationModel.getEntityType());
        assertEquals("Key should match", additionalInformation.getKey(), additionalOrderInformationModel.getKey());
        assertEquals("Value should match", additionalInformation.getValue(), additionalOrderInformationModel.getValue());
    }
}

