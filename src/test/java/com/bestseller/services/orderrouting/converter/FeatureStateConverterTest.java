package com.bestseller.services.orderrouting.converter;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;
import org.togglz.core.repository.FeatureState;

import com.bestseller.services.orderrouting.feature.toggles.ORSFeatures;
import com.bestseller.services.orderrouting.model.FeatureModel;

@RunWith(MockitoJUnitRunner.class)
public class FeatureStateConverterTest {

    private static final String PARAMETER_KEY = "key";
    private static final String PARAMETER_VALUE = "value";
    private static final String ACTIVATION_STRATEGY_ID = "activation";

    @InjectMocks
    private FeatureStateConverter featureStateConverter;

    @Test
    public void convert_givenValidFeatureState_expectValidModel() {
        // arrange
        final FeatureState featureState = new FeatureState(ORSFeatures.ROUTE_ORDERS, true).setParameter(PARAMETER_KEY, PARAMETER_VALUE)
                .setStrategyId(ACTIVATION_STRATEGY_ID);

        // act
        final FeatureModel model = featureStateConverter.convert(featureState);

        // assert
        assertEquals("Should set the model name to the feature name", model.getName(), featureState.getFeature().name());
        assertEquals("Should set the model enabled state to the feature enabled state", model.getEnabled(), featureState.isEnabled());
        assertEquals("Should set the model strategy ID to the feature strategy ID", model.getStrategyId(), featureState.getStrategyId());
    }

    @Test
    public void convert_givenFeatureStateWithoutStrategyId_expectValidModelWithoutStrategyId() {
        // arrange
        final FeatureState featureState = new FeatureState(ORSFeatures.ROUTE_ORDERS, true);

        // act
        final FeatureModel model = featureStateConverter.convert(featureState);

        // assert
        assertEquals("Should have no strategy ID", model.getStrategyId(), featureState.getStrategyId());
        assertNull("Should set the strategy ID to null", model.getStrategyId());
    }
}
