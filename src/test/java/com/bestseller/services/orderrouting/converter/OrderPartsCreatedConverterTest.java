package com.bestseller.services.orderrouting.converter;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.common.AdditionalInformation;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.Address;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.Payment;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsCreated.OrderPart;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsCreated.OrderPartsCreated;
import com.bestseller.services.orderrouting.model.AdditionalOrderInformationModel;
import com.bestseller.services.orderrouting.model.AddressModel;
import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.model.OrderLineQuantityStatus;
import com.bestseller.services.orderrouting.model.OrderModel;
import com.bestseller.services.orderrouting.model.PaymentModel;
import com.bestseller.services.orderrouting.service.EntityType;
import com.bestseller.services.orderrouting.utils.generator.OrderModelGenerator;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Iterator;
import java.util.List;
import java.util.Set;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.CoreMatchers.hasItem;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.hasProperty;
import static org.junit.Assert.assertEquals;

@RunWith(MockitoJUnitRunner.class)
public class OrderPartsCreatedConverterTest {
    private static final String SHIP_FROM_STORE = "SHIP_FROM_STORE";
    private static final String FULFILLMENT_CENTER_EAST = "INGRAM_MICRO";
    private static final String SIZE_SHOULD_BE_2 = "Size should be 2";

    private static final int ORDER_PART_NUMBER_1 = 1;
    private static final int ORDER_PART_NUMBER_2 = 2;
    private static final int ORDER_PART_NUMBER_3 = 3;

    private static final int ORDER_LINE_ONE_INDEX = 0;
    private static final int ORDER_LINE_TWO_INDEX = 1;
    private static final int ORDER_LINE_THREE_INDEX = 2;
    private static final int ORDER_LINE_FOUR_INDEX = 3;
    private static final String ISO_STORE_ID = "12687";

    @InjectMocks
    private OrderPartsCreatedConverter converter;

    private OrderModel orderModel;
    private OrderLineQuantityModel orderLine1;
    private OrderLineQuantityModel orderLine2;
    private OrderLineQuantityModel orderLine3;
    private OrderLineQuantityModel orderLine4;

    @Before
    public void setUp() {
        orderModel = OrderModelGenerator.createTestOrderModel();
        orderModel.setIsoStoreId(ISO_STORE_ID);
        List<OrderLineQuantityModel> orderLines = OrderModelGenerator.createThreeTestOrderLines(orderModel);
        orderLines.add(OrderModelGenerator.createOneTestOrderLine(orderModel));

        orderModel.setPayments(OrderModelGenerator.createTwoPayments(orderModel));
        orderModel.setOrderLineQuantities(orderLines);
        orderModel.getOrderLineQuantities().forEach(q -> q.setStatus(OrderLineQuantityStatus.ROUTING));

        orderLine1 = orderModel.getOrderLineQuantities().get(ORDER_LINE_ONE_INDEX);
        orderLine1.setFulfillmentNode(SHIP_FROM_STORE);
        orderLine1.setOrderPartNumber(ORDER_PART_NUMBER_1);
        orderLine2 = orderModel.getOrderLineQuantities().get(ORDER_LINE_TWO_INDEX);
        orderLine2.setFulfillmentNode(SHIP_FROM_STORE);
        orderLine2.setOrderPartNumber(ORDER_PART_NUMBER_2);
        orderLine3 = orderModel.getOrderLineQuantities().get(ORDER_LINE_THREE_INDEX);
        orderLine3.setFulfillmentNode(SHIP_FROM_STORE);
        orderLine3.setOrderPartNumber(ORDER_PART_NUMBER_2);


        orderLine4 = orderModel.getOrderLineQuantities().get(ORDER_LINE_FOUR_INDEX);
        orderLine4.setFulfillmentNode(FULFILLMENT_CENTER_EAST);
        orderLine4.setOrderPartNumber(ORDER_PART_NUMBER_3);
    }

    @Test
    public void convert_noRoutingLinesGiven_noOrderPartMessages() {
        // arrange
        orderModel.getOrderLineQuantities().forEach(o -> o.setStatus(OrderLineQuantityStatus.ROUTED));

        // act
        List<OrderPartsCreated> orderParts = converter.convert(orderModel);

        // assert
        assertEquals("Size should be zero", 0, orderParts.size());
    }

    @Test
    public void convert_routingLinesGiven_orderPartMessagesCreated() {
        // act
        List<OrderPartsCreated> messages = converter.convert(orderModel);

        // assert
        assertEquals(SIZE_SHOULD_BE_2, 2, messages.size());

        assertThat(messages, hasItem(hasProperty("fulfillmentNode", equalTo(SHIP_FROM_STORE))));

        OrderPartsCreated message = getMessageFromList(messages, SHIP_FROM_STORE);
        assertMessage(message, 2);
        assertOrderPart(message.getOrderParts().get(0), ORDER_PART_NUMBER_1, orderLine1);
        assertOrderPart(message.getOrderParts().get(1), ORDER_PART_NUMBER_2, orderLine2, orderLine3);


        assertThat(messages, hasItem(hasProperty("fulfillmentNode", equalTo(FULFILLMENT_CENTER_EAST))));

        message = getMessageFromList(messages, FULFILLMENT_CENTER_EAST);
        assertMessage(message, 1);
        assertOrderPart(message.getOrderParts().get(0), ORDER_PART_NUMBER_3, orderLine4);
    }

    private OrderPartsCreated getMessageFromList(List<OrderPartsCreated> messages, String fulfillmentNode) {
        return messages.stream()
                .filter(m -> m.getFulfillmentNode().equals(fulfillmentNode))
                .findFirst()
                .get();
    }

    @SuppressWarnings("PMD.NcssCount")
    private void assertMessage(OrderPartsCreated message, int expectedOrderParts) {
        assertEquals("Order id should match",
                orderModel.getOrderId(), message.getOrderId());
        assertEquals("Total order parts should match",
                expectedOrderParts, message.getOrderParts().size());
        assertEquals("Marketplace should match",
                orderModel.getMarketPlace(), message.getMarketPlace());
        assertEquals("Store should match",
                orderModel.getStore(), message.getStore());
        assertEquals("Channel should match",
                orderModel.getChannel(), message.getChannel());
        assertEquals("Brand should match",
                orderModel.getBrand(), message.getBrand());
        assertEquals("Action code should match",
                orderModel.getActionCode(), message.getActionCode());
        assertEquals("Carrier should match",
                orderModel.getCarrier(), message.getOrderDetails().getCarrier());
        assertEquals("Carrier variant should match",
                orderModel.getCarrierVariant(), message.getOrderDetails().getCarrierVariant());
        assertEquals("Customer email should match",
                orderModel.getCustomerEmail(), message.getCustomerInformation().getEmail());
        assertEquals("Customer id should match",
                orderModel.getCustomerId(), message.getCustomerInformation().getCustomerId());
        assertEquals("Customer locale should match",
                     orderModel.getCustomerLocale(), message.getCustomerInformation().getCustomerLocale());
        assertEquals("External customer number",
                orderModel.getExternalCustomerNumber(), message.getCustomerInformation().getExternalCustomerNumber());
        assertEquals("External order number should match",
                orderModel.getExternalOrderNumber(), message.getOrderDetails().getExternalOrderNumber());
        assertEquals("IsTest should match",
                orderModel.getIsTest(), message.getIsTest());
        assertEquals("Creation date should match",
                orderModel.getOrderCreationDate(), message.getOrderDetails().getOrderCreationDate().toInstant());
        assertEquals("Order type should match",
                orderModel.getOrderType(), message.getOrderDetails().getOrderType());
        assertEquals("Order value should match",
                orderModel.getOrderValue(), message.getOrderDetails().getOrderValue());
        assertEquals("Placed date should match",
                orderModel.getPlacedDate(), message.getPlacedDate().toInstant());
        assertEquals("Shipping fees should match",
                orderModel.getShippingFees(), message.getOrderDetails().getShippingFees());
        assertEquals("Shipping fees cancelled should match",
                orderModel.getShippingFeesCancelled(), message.getOrderDetails().getShippingFeesCancelled());
        assertEquals("Shipping fees tax percentage should match",
                orderModel.getShippingFeesTaxPercentage(), message.getOrderDetails().getShippingFeesTaxPercentage());
        assertEquals("isoStoreId should match",
            orderModel.getIsoStoreId(), message.getOrderDetails().getIsoStoreId());

        assertAddress(orderModel.getBillingAddress(), message.getCustomerInformation().getBillingAddress(),
                "billing");
        assertAddress(orderModel.getShippingAddress(), message.getShippingInformation().getShippingAddress(),
                "shipping");

        assertEquals("Parcel locker should match",
                orderModel.getParcelLocker(), message.getShippingInformation().getParcelLocker());

        assertAdditionalInformationSet(orderModel.getAdditionalOrderInformation(), message.getShippingInformation().getAdditionalInformation());

        assertEquals("Payments size should match", orderModel.getPayments().size(), message.getPayments().size());
        Iterator<PaymentModel> paymentModelIterator = orderModel.getPayments().iterator();
        Iterator<Payment> paymentIterator = message.getPayments().iterator();
        int i = 1;
        while (paymentModelIterator.hasNext()) {
            PaymentModel expected = paymentModelIterator.next();
            Payment actual = paymentIterator.next();
            assertEquals("Payment type should match for payment line " + i, expected.getName(), actual.getName());
            assertEquals("Payment method should match for payment line " + i, expected.getSubMethod(), actual.getSubMethod());
            i++;
        }
    }

    private void assertOrderPart(OrderPart orderPart, Integer expectedOrderPartNumber, OrderLineQuantityModel... lines) {
        assertEquals("Order part number should match", expectedOrderPartNumber, orderPart.getOrderPartNumber());

        assertEquals("The total amount of order lines do not match for this order part.", lines.length, orderPart.getOrderLines().size());

        assertOrderLines(lines, orderPart.getOrderLines());
    }

    private void assertOrderLines(OrderLineQuantityModel[] lines, List<OrderLine> actual) {
        assertEquals("Lines size should match", lines.length, actual.size());

        for (int i = 0; i < lines.length; i++) {
            OrderLine orderLine = actual.get(i);
            assertEquals("EAN should match for line " + i,
                    lines[i].getEan(), orderLine.getEan());
            assertEquals("Product name should match for line " + i,
                    lines[i].getProductName(), orderLine.getProductName());
            assertEquals("Line number should match for line " + i,
                    lines[i].getLineNumber(), orderLine.getLineNumber());
            assertEquals("Quantity should match for line " + i,
                    lines[i].getQuantity(), orderLine.getQuantity());
            assertEquals("Retail price should match for line " + i,
                    lines[i].getRetailPrice(), orderLine.getRetailPrice());
            assertEquals("Discount value should match for line " + i,
                    lines[i].getDiscountValue(), orderLine.getDiscountValue());
            assertEquals("Tax percentage should match for line " + i,
                    lines[i].getTaxPercentage(), orderLine.getTaxPercentage());
            assertEquals("Is gift item should match for line " + i,
                    lines[i].getIsGiftItem(), orderLine.getIsGiftItem());
            assertEquals("Partner reference should match for line " + i,
                    lines[i].getPartnerReference(), orderLine.getPartnerReference());
        }
    }

    private void assertAddress(AddressModel expected, Address actual, String addressType) {
        assertEquals("Address line 1 should match for address type " + addressType,
                expected.getAddressLine1(), actual.getAddressLine1());
        assertEquals("Address line 2 should match for address type " + addressType,
                expected.getAddressLine2(), actual.getAddressLine2());
        assertEquals("Address line 3 should match for address type " + addressType,
                expected.getAddressLine3(), actual.getAddressLine3());
        assertEquals("City should match for address type " + addressType,
                expected.getCity(), actual.getCity());
        assertEquals("State should match for address type " + addressType,
                expected.getState(), actual.getState());
        assertEquals("Country should match for address type " + addressType,
                expected.getCountry(), actual.getCountry());
        assertEquals("First name should match for address type " + addressType,
                expected.getFirstName(), actual.getFirstName());
        assertEquals("Last name should match for address type " + addressType,
                expected.getLastName(), actual.getLastName());
        assertEquals("House number should match for address type " + addressType,
                expected.getHouseNumber(), actual.getHouseNumber());
        assertEquals("House number extended should match for address type " + addressType,
                expected.getHouseNumberExtended(), actual.getHouseNumberExtended());
        assertEquals("Phone should match for address type " + addressType,
                expected.getPhoneNumber(), actual.getPhoneNumber());
        assertEquals("Zip should match for type " + addressType,
                expected.getZipcode(), actual.getZipcode());
    }

    private void assertAdditionalInformationSet(List<AdditionalOrderInformationModel> additionalInformationList,
                                                Set<AdditionalInformation> additionalInformationModelSet) {
        assertEquals("Size should match", additionalInformationList.size(), additionalInformationModelSet.size());
        Iterator<AdditionalOrderInformationModel> additionalInformationIterator = additionalInformationList.iterator();
        Iterator<AdditionalInformation> additionalInformationModelIterator = additionalInformationModelSet.iterator();
        while (additionalInformationIterator.hasNext()) {
            assertAdditionalInformation(additionalInformationModelIterator.next(), additionalInformationIterator.next());
        }
    }

    private void assertAdditionalInformation(
        AdditionalInformation additionalInformation,
        AdditionalOrderInformationModel additionalOrderInformationModel) {
        assertEquals("Entity type should match", EntityType.SHIPPING_INFORMATION, additionalOrderInformationModel.getEntityType());
        assertEquals("Key should match", additionalInformation.getKey(), additionalOrderInformationModel.getKey());
        assertEquals("Value should match", additionalInformation.getValue(), additionalOrderInformationModel.getValue());
    }
}
