package com.bestseller.services.orderrouting.converter;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartRejected.OrderPartRejected;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartsCancelled.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartsCancelled.OrderPartsCancelled;
import com.bestseller.services.orderrouting.utils.generator.OrderPartRejectedGenerator;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;

@RunWith(MockitoJUnitRunner.class)
public class OrderPartsCancelledConverterTest {
    private OrderPartRejected orderPartRejected;

    @InjectMocks
    private OrderPartsCancelledConverter orderPartsCancelledConverter;

    @Before
    public void setUp() {
        orderPartRejected = OrderPartRejectedGenerator.createFullOrderPartRejectedMessage();
    }

    @Test
    public void convert_orderPartRejectedGiven_orderPartsCancelledMessageCreated() {
        // act
        OrderPartsCancelled orderPartsCancelled = orderPartsCancelledConverter.convert(orderPartRejected);

        // assert
        assertThat("Order id should be the same", orderPartsCancelled.getOrderId(), is(orderPartRejected.getOrderId()));
        assertThat("Warehouse should be the same", orderPartsCancelled.getWarehouse(), is(orderPartRejected.getWarehouse()));
        assertThat("Is test should be the same", orderPartsCancelled.getIsTest(), is(orderPartRejected.getIsTest()));
        assertThat("Cancellation date should be the same", orderPartsCancelled.getCancellationDate(), is(orderPartRejected.getCancellationDate()));
        assertThat("Amount of order lines are the same", orderPartsCancelled.getOrderLines().size(), is(orderPartRejected.getOrderLines().size()));

        for (int i = 0; i < orderPartsCancelled.getOrderLines().size(); i++) {
            OrderLine orderLineCancelled = orderPartsCancelled.getOrderLines().get(i);
            com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartRejected.OrderLine orderLineRejected =
                    orderPartRejected.getOrderLines().get(i);

            assertThat("EAN should be the same", orderLineCancelled.getEan(), is(orderLineRejected.getEan()));
            assertThat("Line number should be the same", orderLineCancelled.getLineNumber(), is(orderLineRejected.getLineNumber()));
            assertThat("Quantity should be the same", orderLineCancelled.getQuantity(), is(orderLineRejected.getQuantity()));
            assertThat("Cancel reason should be the same", orderLineCancelled.getCancelReason(), is(orderLineRejected.getCancelReason()));
        }
    }
}
