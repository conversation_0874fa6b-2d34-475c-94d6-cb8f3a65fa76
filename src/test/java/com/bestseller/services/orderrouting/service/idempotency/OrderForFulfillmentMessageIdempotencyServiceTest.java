package com.bestseller.services.orderrouting.service.idempotency;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderForFulfillment.OrderForFulfillment;
import com.bestseller.services.orderrouting.metric.KafkaMessageOperation;
import com.bestseller.services.orderrouting.model.OrderModel;
import com.bestseller.services.orderrouting.repository.OrderModelRepository;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Optional;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;

/**
 * Test class for OrderForFulfillmentMessageIdempotencyServiceImpl.
 */
@RunWith(MockitoJUnitRunner.class)
public class OrderForFulfillmentMessageIdempotencyServiceTest {

    @Mock
    private OrderModelRepository orderModelRepository;

    @Spy
    private MeterRegistry meterRegistry = new SimpleMeterRegistry();

    @Mock
    private OrderForFulfillment orderForFulfillment;

    @Mock
    private AbstractMessageIdempotencyService<?> messageIdempotencyService;

    @InjectMocks
    private OrderForFulfillmentMessageIdempotencyService orderMessageIdempotencyService;

    @Captor
    private ArgumentCaptor<Tags> tagsCaptor;

    private OrderModel orderModel;

    @Before
    public void setUp() {
        orderModel = new OrderModel();

        Mockito.when(orderForFulfillment.getOrderId()).thenReturn("orderId");
        Mockito.when(orderModelRepository.findById(orderForFulfillment.getOrderId())).thenReturn(Optional.of(orderModel));
    }

    @Test
    public void checkDuplicateMessage_duplicateMessage_returnsTrue() {
        //arrange: done in setup

        //act
        boolean isDuplicate = orderMessageIdempotencyService.checkDuplicateMessage(orderForFulfillment);

        //assert
        assertTrue("Should be duplicate", isDuplicate);
    }

    @Test
    public void checkDuplicateMessage_messageIsOrderAdvice_returnsFalse() {
        //arrange
        orderModel.setIsOrderAdvice(true);

        //act
        boolean isDuplicate = orderMessageIdempotencyService.checkDuplicateMessage(orderForFulfillment);

        //assert
        assertFalse("Should not be duplicate", isDuplicate);
    }

    @Test
    public void checkDuplicateMessage_nonDuplicateMessage_returnsFalse() {
        //arrange
        Mockito.when(orderModelRepository.findById(orderForFulfillment.getOrderId())).thenReturn(Optional.empty());

        //act
        boolean isDuplicate = orderMessageIdempotencyService.checkDuplicateMessage(orderForFulfillment);

        //assert
        assertFalse("Should not be duplicate", isDuplicate);
    }

    @SuppressWarnings("unchecked")
    @Test
    public void checkDuplicateMessage_duplicateMessage_metricNotifiedWithDuplicateMessageEvent() {
        //arrange: done in setup

        //act
        orderMessageIdempotencyService.checkDuplicateMessage(orderForFulfillment);

        //assert
        verify(meterRegistry).counter(eq(KafkaMessageOperation.DUPLICATE_ORDER_FULFILLMENT_MESSAGE.name()),
                tagsCaptor.capture());

        var tags = tagsCaptor.getValue();

        assertThat("Metric is incremented", meterRegistry.counter(KafkaMessageOperation.DUPLICATE_ORDER_FULFILLMENT_MESSAGE.name(), tags).count(),
                equalTo((double) 1));
    }

    @SuppressWarnings("unchecked")
    @Test
    public void checkDuplicateMessage_notDuplicateMessage_metricsNotNotified() {
        //arrange
        Mockito.when(orderModelRepository.findById(orderForFulfillment.getOrderId())).thenReturn(Optional.empty());

        //act
        orderMessageIdempotencyService.checkDuplicateMessage(orderForFulfillment);

        //assert
        verifyNoInteractions(meterRegistry);
    }
}
