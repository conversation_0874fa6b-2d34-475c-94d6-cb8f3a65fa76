package com.bestseller.services.orderrouting.service;

import org.jeasy.rules.api.Facts;
import org.jeasy.rules.api.Rules;
import org.jeasy.rules.api.RulesEngine;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;

/**
 * Unit tests for {@link RulesService}.
 */
@RunWith(MockitoJUnitRunner.class)
public class RulesServiceTest {

    @Mock
    private Rules orderPlacingRules;
    @Mock
    private Rules orderRoutingRules;
    @Mock
    private Rules orderCancellingRules;
    @Mock
    private RulesEngine defaultRulesEngine;

    private RulesService rulesService;

    private Facts facts;

    @Before
    public void setUp() {
        rulesService = new RulesService(orderPlacingRules, orderRoutingRules, orderCancellingRules, defaultRulesEngine);
        facts = new Facts();
    }

    @Test
    public void executeOrderRoutingRulesEngine_factsGiven_fireRuleEngine() {
        // arrange - done in setup

        // act
        rulesService.executeOrderPlacingRules(facts);

        // assert
        verify(defaultRulesEngine).fire(eq(orderPlacingRules), eq(facts));
    }

    @Test
    public void executeOrderRoutingRules_factsGiven_fireRuleEngine() {
        // arrange - done in setup

        // act
        rulesService.executeOrderRoutingRules(facts);

        // assert
        verify(defaultRulesEngine).fire(eq(orderRoutingRules), eq(facts));
    }

    @Test
    public void executeOrderCancellingRules_factsGiven_fireRuleEngine() {
        // arrange - done in setup

        // act
        rulesService.executeOrderPartRejectedRules(facts);

        // assert
        verify(defaultRulesEngine).fire(orderCancellingRules, facts);
    }
}
