package com.bestseller.services.orderrouting.service.idempotency;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartRejected.OrderPartRejected;
import com.bestseller.services.orderrouting.metric.KafkaMessageOperation;
import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.model.OrderLineQuantityStatus;
import com.bestseller.services.orderrouting.model.OrderModel;
import com.bestseller.services.orderrouting.service.CancellationReason;
import com.bestseller.services.orderrouting.service.OrderService;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;

import static com.bestseller.services.orderrouting.constants.FulfillmentNodeConstants.SHIP_FROM_STORE_NL;
import static com.bestseller.services.orderrouting.utils.generator.OrderModelGenerator.createOneTestOrderLine;
import static com.bestseller.services.orderrouting.utils.generator.OrderModelGenerator.createTestOrderModel;
import static com.bestseller.services.orderrouting.utils.generator.OrderPartRejectedGenerator.createFullOrderPartRejectedMessage;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;

/**
 * Test class for {@link OrderPartRejectedIdempotencyService}.
 */
@RunWith(MockitoJUnitRunner.class)
public class OrderPartRejectedIdempotencyServiceTest {
    private static final String EAN_NOT_EXISTING_IN_CACHE = "*********";

    @Spy
    private MeterRegistry meterRegistry = new SimpleMeterRegistry();

    @Mock
    private OrderService orderService;

    @InjectMocks
    private OrderPartRejectedIdempotencyService orderPartRejectedIdempotencyService;

    @Captor
    private ArgumentCaptor<Tags> tagsCaptor;

    private OrderPartRejected orderPartRejected;
    private OrderLineQuantityModel orderLineQuantityModel;
    private OrderModel orderModel;

    @Before
    public void setUp() {
        // Create order model.
        orderModel = createTestOrderModel();
        // Create one ROUTED order line.
        orderLineQuantityModel = createOneTestOrderLine(orderModel);
        orderLineQuantityModel.setStatus(OrderLineQuantityStatus.ROUTED);

        // Add order line to order model.
        orderModel.setOrderLineQuantities(Collections.singletonList(orderLineQuantityModel));
        // Create order parts cancelled message.
        orderPartRejected = createFullOrderPartRejectedMessage(orderModel.getOrderId(), SHIP_FROM_STORE_NL,
                                                               orderLineQuantityModel.getEan());
        // Mock order service to return test order model.
        doReturn(orderModel).when(orderService).findOrderModel(anyString());
    }

    @Test
    public void checkDuplicateMessage_nullOrder_returnTrue() {
        // arrange
        doReturn(null).when(orderService).findOrderModel(anyString());

        // act
        boolean result = orderPartRejectedIdempotencyService.checkDuplicateMessage(orderPartRejected);

        // assert
        assertTrue("Message should be evaluated as duplicate.", result);
    }

    @Test
    public void checkDuplicateMessage_nullOrder_notifyMonitoringService() {
        // arrange
        doReturn(null).when(orderService).findOrderModel(anyString());

        // act
        orderPartRejectedIdempotencyService.checkDuplicateMessage(orderPartRejected);

        // assert
        verify(meterRegistry).counter(eq(KafkaMessageOperation.DUPLICATE_ORDER_PART_REJECTED_MESSAGE.name()),
                tagsCaptor.capture());

        var tags = tagsCaptor.getValue();

        assertThat("Metric is incremented", meterRegistry.counter(KafkaMessageOperation.DUPLICATE_ORDER_PART_REJECTED_MESSAGE.name(), tags).count(),
                equalTo((double) 1));
    }

    @Test
    public void checkDuplicateMessage_nonDuplicateMessage_returnFalse() {
        // act
        boolean result = orderPartRejectedIdempotencyService.checkDuplicateMessage(orderPartRejected);

        // assert
        assertFalse("Message should not be evaluated as duplicate.", result);
    }

    @SuppressWarnings("unchecked")
    @Test
    public void checkDuplicateMessage_nonDuplicateMessage_doNotNotifyMonitoringService() {
        // act
        orderPartRejectedIdempotencyService.checkDuplicateMessage(orderPartRejected);

        // assert
        verifyNoInteractions(meterRegistry);
    }

    @Test
    public void checkDuplicateMessage_duplicateMessageWithNonTestOrder_returnTrue() {
        // arrange
        orderLineQuantityModel.setStatus(OrderLineQuantityStatus.CANCELLED);

        // act
        boolean result = orderPartRejectedIdempotencyService.checkDuplicateMessage(orderPartRejected);

        // assert
        assertTrue("Message should be evaluated as duplicate.", result);
    }

    @Test
    public void checkDuplicateMessage_orderLineIsMissing_returnTrue() {
        // arrange
        orderPartRejected = createFullOrderPartRejectedMessage(orderModel.getOrderId(),
                                                               orderLineQuantityModel.getEan(), EAN_NOT_EXISTING_IN_CACHE);

        // act
        boolean result = orderPartRejectedIdempotencyService.checkDuplicateMessage(orderPartRejected);

        // assert
        assertTrue("Message should be evaluated as duplicate.", result);
    }

    @Test
    public void checkDuplicateMessage_duplicateMessageWithTestOrder_returnFalse() {
        // arrange
        orderModel.setIsTest(true);
        orderLineQuantityModel.setStatus(OrderLineQuantityStatus.CANCELLED);

        // act
        boolean result = orderPartRejectedIdempotencyService.checkDuplicateMessage(orderPartRejected);

        // assert
        assertFalse("Message should be evaluated as new.", result);
    }

    @Test
    public void checkDuplicateMessage_duplicateRoutedMessageWithTestOrder_returnFalse() {
        // arrange
        orderModel.setIsTest(true);
        orderLineQuantityModel.setStatus(OrderLineQuantityStatus.ROUTED);
        orderLineQuantityModel.setCancelReason(CancellationReason.STORE_REJECTION);

        // act
        boolean result = orderPartRejectedIdempotencyService.checkDuplicateMessage(orderPartRejected);

        // assert
        assertFalse("Message should be evaluated as new.", result);
    }

    @SuppressWarnings("unchecked")
    @Test
    public void checkDuplicateMessage_duplicateMessage_notifyMonitoringService() {
        // arrange
        orderLineQuantityModel.setStatus(OrderLineQuantityStatus.CANCELLED);

        // act
        orderPartRejectedIdempotencyService.checkDuplicateMessage(orderPartRejected);

        // assert
        verify(meterRegistry).counter(eq(KafkaMessageOperation.DUPLICATE_ORDER_PART_REJECTED_MESSAGE.name()),
                tagsCaptor.capture());

        var tags = tagsCaptor.getValue();

        assertThat("Metric is incremented", meterRegistry.counter(KafkaMessageOperation.DUPLICATE_ORDER_PART_REJECTED_MESSAGE.name(), tags).count(),
                equalTo((double) 1));
    }

    @Test
    public void checkDuplicateMessage_messageForNullOrder_returnFalse() {
        // act
        boolean result = orderPartRejectedIdempotencyService.checkDuplicateMessage(orderPartRejected);

        // assert
        assertFalse("Message should not be evaluated as duplicate.", result);
    }

    @SuppressWarnings("unchecked")
    @Test
    public void checkDuplicateMessage_messageForNullOrder_doNotNotifyMonitoringService() {
        // act
        orderPartRejectedIdempotencyService.checkDuplicateMessage(orderPartRejected);

        // assert
        verifyNoInteractions(meterRegistry);
    }

    @Test
    public void checkDuplicateMessage_messageWithStateOfRoutedWithNoCancelReason_returnFalse() {
        // arrange
        orderLineQuantityModel.setStatus(OrderLineQuantityStatus.ROUTED);

        // act
        boolean result = orderPartRejectedIdempotencyService.checkDuplicateMessage(orderPartRejected);

        // assert
        assertFalse("Message should evaluate to False. Marked as new message", result);

    }

    @Test
    public void checkDuplicateMessage_messageWithStateOfPlacedWithCancelReason_returnFalse() {
        // Checking if Store Rejection has been set with a previous state of placed
        orderLineQuantityModel.setStatus(OrderLineQuantityStatus.PLACED);
        orderLineQuantityModel.setCancelReason(CancellationReason.STORE_REJECTION);
        // act
        boolean result = orderPartRejectedIdempotencyService.checkDuplicateMessage(orderPartRejected);

        // assert
        assertFalse("Message should evaluate to False. Marked as new message", result);

    }

    @Test
    public void checkDuplicateMessage_messageWithStateOfRoutedWithStoreRejectionReason_returnTrue() {
        // arrange
        orderLineQuantityModel.setStatus(OrderLineQuantityStatus.ROUTED);
        orderLineQuantityModel.setCancelReason(CancellationReason.STORE_REJECTION);
        orderPartRejected.getOrderLines().get(0).setCancelReason(CancellationReason.STORE_REJECTION.getReason());

        // act
        boolean result = orderPartRejectedIdempotencyService.checkDuplicateMessage(orderPartRejected);

        // assert
        assertTrue("Message should evaluate to True. Marked as duplicate message", result);

    }
}
