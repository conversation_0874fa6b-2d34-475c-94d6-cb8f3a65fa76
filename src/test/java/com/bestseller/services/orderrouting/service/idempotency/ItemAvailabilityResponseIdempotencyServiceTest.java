package com.bestseller.services.orderrouting.service.idempotency;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.Item;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailability;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailabilityResponse;
import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.model.OrderLineQuantityStatus;
import com.bestseller.services.orderrouting.model.OrderModel;
import com.bestseller.services.orderrouting.repository.OrderModelRepository;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Optional;
import java.util.UUID;

import static com.bestseller.services.orderrouting.utils.generator.ItemAvailabilityGenerator.FULL_AVAILABILITY;
import static com.bestseller.services.orderrouting.utils.generator.ItemAvailabilityGenerator.WAREHOUSE_1;
import static com.bestseller.services.orderrouting.utils.generator.ItemAvailabilityGenerator.createItem;
import static com.bestseller.services.orderrouting.utils.generator.ItemAvailabilityGenerator.createItemAvailability;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ItemAvailabilityResponseIdempotencyServiceTest {

    private static final int ORDER_LINE_1_QUANTITY = 5;
    private static final int ORDER_LINE_2_QUANTITY = 7;
    private static final int ORDER_LINE_NUMBER_1_NUMBER = 1;
    private static final int ORDER_LINE_NUMBER_2_NUMBER = 2;
    private static final String EAN_1 = "**********";
    private static final String EAN_2 = "**********";

    @Spy
    private MeterRegistry meterRegistry = new SimpleMeterRegistry();

    @Mock
    private OrderModelRepository orderModelRepository;

    @InjectMocks
    private ItemAvailabilityResponseIdempotencyService itemAvailabilityResponseIdempotencyService;

    private OrderModel orderModel;
    private OrderLineQuantityModel orderLine1;
    private OrderLineQuantityModel orderLine2;
    private ItemAvailabilityResponse testMessage;

    @Before
    public void setUp() {
        orderLine1 = new OrderLineQuantityModel();
        orderLine1.setEan(EAN_1);
        orderLine1.setQuantity(ORDER_LINE_1_QUANTITY);
        orderLine1.setStatus(OrderLineQuantityStatus.PLACED);
        orderLine1.setLineNumber(ORDER_LINE_NUMBER_1_NUMBER);

        orderLine2 = new OrderLineQuantityModel();
        orderLine2.setEan(EAN_2);
        orderLine2.setQuantity(ORDER_LINE_2_QUANTITY);
        orderLine2.setStatus(OrderLineQuantityStatus.PLACED);
        orderLine2.setLineNumber(ORDER_LINE_NUMBER_2_NUMBER);

        orderModel = new OrderModel();
        orderModel.setOrderLineQuantities(new ArrayList<>());
        orderModel.getOrderLineQuantities().add(orderLine1);
        orderModel.getOrderLineQuantities().add(orderLine2);

        final ItemAvailability testItemAvailability = createItemAvailability(WAREHOUSE_1, FULL_AVAILABILITY);
        final Item testItem1 = createItem(EAN_1, testItemAvailability);
        final Item testItem2 = createItem(EAN_2, testItemAvailability);
        testMessage = new ItemAvailabilityResponse()
                .withCorrelationId(UUID.randomUUID().toString())
                .withItems(Arrays.asList(testItem1, testItem2));
    }

    @Test
    public void checkDuplicateMessage_routedAndCancelledOrderLinesGiven_returnsTrue() {
        // arrange
        when(orderModelRepository.findById(testMessage.getCorrelationId())).thenReturn(Optional.of(orderModel));
        orderLine1.setStatus(OrderLineQuantityStatus.ROUTED);
        orderLine2.setStatus(OrderLineQuantityStatus.CANCELLED);
        boolean result;

        // act
        result = itemAvailabilityResponseIdempotencyService.checkDuplicateMessage(testMessage);

        // assert
        assertTrue("Result should be true", result);
    }

    @Test
    public void checkDuplicateMessage_placedAndWaitingForStockOrderLinesGiven_returnsFalse() {
        // arrange
        when(orderModelRepository.findById(testMessage.getCorrelationId())).thenReturn(Optional.of(orderModel));
        orderLine1.setStatus(OrderLineQuantityStatus.PLACED);
        orderLine2.setStatus(OrderLineQuantityStatus.WAITING_FOR_STOCK);
        boolean result;

        // act
        result = itemAvailabilityResponseIdempotencyService.checkDuplicateMessage(testMessage);

        // assert
        assertFalse("Result should be false", result);
    }

    @Test
    public void checkDuplicateMessage_orderNotFound_returnsFalse() {

        // arrange
        when(orderModelRepository.findById(testMessage.getCorrelationId())).thenReturn(Optional.empty());
        boolean result;

        // act
        result = itemAvailabilityResponseIdempotencyService.checkDuplicateMessage(testMessage);

        // assert
        assertFalse("Result should be false", result);
    }
}
