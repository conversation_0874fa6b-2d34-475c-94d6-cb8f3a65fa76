package com.bestseller.services.orderrouting.service;

import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.model.OrderLineQuantityStatus;
import com.bestseller.services.orderrouting.model.OrderModel;
import com.bestseller.services.orderrouting.repository.OrderLineQuantityModelRepository;
import com.bestseller.services.orderrouting.repository.OrderModelRepository;
import com.bestseller.services.orderrouting.utils.TestingFeatureManager;
import com.bestseller.services.orderrouting.utils.generator.OrderModelGenerator;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.togglz.core.context.StaticFeatureManagerProvider;

import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

import static org.hamcrest.CoreMatchers.allOf;
import static org.hamcrest.CoreMatchers.sameInstance;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.equalToIgnoringCase;
import static org.hamcrest.Matchers.everyItem;
import static org.hamcrest.Matchers.hasProperty;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Performs validations on the data in the {@link OrderService} message.
 */
@RunWith(MockitoJUnitRunner.class)
public class OrderServiceTest {

    private static final int FIRST_ORDER_PART_NUMBER = 1;

    @Rule
    public ExpectedException thrown = ExpectedException.none();

    @Mock
    private OrderModelRepository orderModelRepository;

    @Mock
    private OrderLineQuantityModelRepository orderLineQuantityModelRepository;

    private OrderService orderService;

    private OrderModel order;

    @BeforeClass
    public static final void init() {
        TransactionSynchronizationManager.clear();
    }

    @Before
    public void setUp() {
        StaticFeatureManagerProvider.setFeatureManager(new TestingFeatureManager());

        orderService = new OrderService(orderModelRepository, orderLineQuantityModelRepository);
        order = OrderModelGenerator.createTestOrderModel();
        order.setOrderLineQuantities(OrderModelGenerator.createThreeTestOrderLines(order));
    }

    @Test
    public void findOrderModel_givenOrderId_callsRepositoryFind() {
        // arrange
        OrderModel orderModel = mock(OrderModel.class);
        String orderId = UUID.randomUUID().toString();
        when(orderModelRepository.findById(orderId)).thenReturn(Optional.of(orderModel));

        // act
        OrderModel result = orderService.findOrderModel(orderId);

        // assert
        verify(orderModelRepository).findById(orderId);
        assertEquals("Order model should match", orderModel, result);
    }

    @Test
    public void saveOrderModel_orderModelGiven_callsRepositoriesSave() {
        // arrange - done in before

        // act
        orderService.saveOrderModel(order);

        // assert
        verify(orderModelRepository).save(eq(order));
    }

    @Test
    public void saveOrderLines_orderLinesGiven_callsRepositoriesSave() {
        // arrange - done in before

        // act
        orderService.saveOrderLines(order.getOrderLineQuantities());

        // assert
        verify(orderLineQuantityModelRepository).saveAll(eq(order.getOrderLineQuantities()));
    }

    @Test
    public void deleteOrderModel_orderModelGiven_deletesLineQuantities() {
        // arrange
        OrderModel orderModel = new OrderModel();
        orderModel.setOrderLineQuantities(new ArrayList<>());

        // act
        orderService.deleteOrderModel(orderModel);

        // assert
        verify(orderModelRepository).delete(orderModel);
    }

    @Test
    public void updateOrderLinesStatus_validOrderLinesAndStatus_saveUpdateOrderLines() {
        // arrange
        final OrderLineQuantityStatus status = OrderLineQuantityStatus.RESUBMIT;
        final List<OrderLineQuantityModel> orderLineQuantities = order.getOrderLineQuantities();
        final List<OrderLineQuantityModel> updatedLineQuantities = new ArrayList<>(orderLineQuantities);
        updatedLineQuantities.forEach(ol -> ol.setStatus(status));

        // act
        orderService.updateOrderLinesStatus(orderLineQuantities, status);

        // assert
        verify(orderLineQuantityModelRepository).saveAll(eq(updatedLineQuantities));
    }

    @Test
    public void findAllOrderModels_positiveScenario_delegatesToTheRepository() {
        // arrange
        Iterable<OrderModel> anIterable = new ArrayDeque<>();
        when(orderModelRepository.findAll())
                .thenReturn(anIterable);

        // act
        final Iterable<OrderModel> result = orderService.findAllOrderModels();

        // assert
        assertThat("Result match instance of iterable", result, sameInstance(anIterable));
    }

    @Test
    public void assignOrderPartFulfillmentNodes_singlePart_sameWarehouseAndPartNumberSet() {
        // arrange
        String warehouse = "HOUSE";
        Map<String, List<List<OrderLineQuantityModel>>> partAssignment = Map.of(warehouse, List.of(order.getOrderLineQuantities()));

        // act
        orderService.assignOrderPartFulfillmentNodes(order, partAssignment);

        // assert
        List<OrderLineQuantityModel> orderLineQuantityModels = order.getOrderLineQuantities();
        assertThat(orderLineQuantityModels, everyItem(hasProperty("fulfillmentNode", equalToIgnoringCase(warehouse))));
        assertThat(orderLineQuantityModels, everyItem(hasProperty("orderPartNumber", equalTo(FIRST_ORDER_PART_NUMBER))));
        assertThat(orderLineQuantityModels, everyItem(hasProperty("totalOrderParts", equalTo(1))));
    }

    @Test
    public void assignOrderPartFulfillmentNodes_twoWarehouses_warehousesAndPartNumbersSet() {
        // arrange
        final int orderSize = 4;
        String warehouse1 = "HOUSE_1";
        String warehouse2 = "HOUSE_2";
        var orderLines = OrderModelGenerator.createNOrderLines(order.getOrderId(), orderSize);
        List<OrderLineQuantityModel> part1 = orderLines.subList(0, orderLines.size() / 2);
        List<OrderLineQuantityModel> part2 = orderLines.subList(orderLines.size() / 2, orderLines.size());
        order.setOrderLineQuantities(orderLines);

        Map<String, List<List<OrderLineQuantityModel>>> partAssignment = Map.of(
                warehouse1, List.of(part1),
                warehouse2, List.of(part2));

        int partNumber1 = 1;
        int partNumber2 = 2;
        // adjust for Map order
        if (part2.equals(partAssignment.values().iterator().next().iterator().next())) {
            partNumber1 = 2;
            partNumber2 = 1;
        }

        // act
        orderService.assignOrderPartFulfillmentNodes(order, partAssignment);

        // assert
        assertThat("Wrong lines in part #1", part1, everyItem(allOf(
                hasProperty("fulfillmentNode", equalToIgnoringCase(warehouse1)),
                hasProperty("orderPartNumber", equalTo(partNumber1)))));
        assertThat("Wrong lines in part #1", part2, everyItem(allOf(
                hasProperty("fulfillmentNode", equalToIgnoringCase(warehouse2)),
                hasProperty("orderPartNumber", equalTo(partNumber2)))));
        assertThat("Wrong total order parts", orderLines, everyItem(hasProperty("totalOrderParts", equalTo(2))));
    }

    @Test
    public void assignOrderPartFulfillmentNodes_twoPartsSameWarehouse_warehouseAndPartNumbersSet() {
        // arrange
        final int orderSize = 4;
        String warehouse = "HOUSE";
        var orderLines = OrderModelGenerator.createNOrderLines(order.getOrderId(), orderSize);
        List<OrderLineQuantityModel> part1 = orderLines.subList(0, orderLines.size() / 2);
        List<OrderLineQuantityModel> part2 = orderLines.subList(orderLines.size() / 2, orderLines.size());
        order.setOrderLineQuantities(orderLines);

        Map<String, List<List<OrderLineQuantityModel>>> partAssignment = Map.of(warehouse, List.of(part1, part2));

        // act
        orderService.assignOrderPartFulfillmentNodes(order, partAssignment);

        // assert
        assertThat("Wrong 1st part number", part1, everyItem(hasProperty("orderPartNumber", equalTo(1))));
        assertThat("Wrong 2nd part number", part2, everyItem(hasProperty("orderPartNumber", equalTo(2))));
        assertThat("Wrong warehouse", orderLines, everyItem(hasProperty("fulfillmentNode", equalTo(warehouse))));
        assertThat("Wrong total order parts", orderLines, everyItem(hasProperty("totalOrderParts", equalTo(2))));
    }
}
