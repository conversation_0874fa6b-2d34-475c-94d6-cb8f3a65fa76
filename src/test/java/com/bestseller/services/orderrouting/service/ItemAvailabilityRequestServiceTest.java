package com.bestseller.services.orderrouting.service;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailabilityRequest;
import com.bestseller.services.orderrouting.converter.ItemAvailabilityRequestConverter;
import com.bestseller.services.orderrouting.model.OrderModel;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Optional;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.CoreMatchers.notNullValue;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.mockito.Mockito.when;

/**
 * Performs validations on the data in the {@link ItemAvailabilityRequestService} service.
 */
@RunWith(MockitoJUnitRunner.class)
public class ItemAvailabilityRequestServiceTest {

    private static final String ORDER_ID = "********";

    @Mock
    private OrderService orderService;

    private ItemAvailabilityRequestService itemAvailabilityRequestService;

    @Before
    public void setUp() {
        itemAvailabilityRequestService = new ItemAvailabilityRequestService(orderService, new ItemAvailabilityRequestConverter());
    }

    @Test
    public void findAndConvert_givenValidOrderId_returnsItemAvailabilityRequest() {
        // arrange
        OrderModel orderModel = new OrderModel();
        orderModel.setOrderId(ORDER_ID);
        when(orderService.findOrderModel(ORDER_ID)).thenReturn(orderModel);

        // act
        ItemAvailabilityRequest itemAvailabilityRequest = itemAvailabilityRequestService.findAndConvert(ORDER_ID).get();

        // assert
        assertThat("Existing order should return valid request", is(notNullValue()));
        assertThat("Correlation id matches order id", itemAvailabilityRequest.getCorrelationId(),
                   is(equalTo(ORDER_ID)));
    }

    @Test
    public void findAndConvert_givenInvalidOrderId_returnsNoValue() {
        // arrange
        when(orderService.findOrderModel(ORDER_ID)).thenReturn(null);

        // act
        Optional<ItemAvailabilityRequest> itemAvailabilityRequest = itemAvailabilityRequestService.findAndConvert(ORDER_ID);

        // assert
        assertThat("Non existing order should return null request", itemAvailabilityRequest, equalTo(Optional.empty()));
    }

}
