package com.bestseller.services.orderrouting.strategy;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailability;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailability.Type;
import com.bestseller.services.orderrouting.model.AddressModel;
import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.model.OrderModel;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailability.Type.STORE;
import static com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailability.Type.WAREHOUSE;
import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;

@RunWith(MockitoJUnitRunner.class)
public class StorePriorityFulfillmentTest {

    private static final int CERTAIN_AMOUNT = 10;
    private static final String NODE = "NO";
    private static final String EAN = "*********";
    private static final String COUNTRY = "ZZ";
    private static final String ANAGRAM_MINI = "ANAGRAM MINI";
    private static final String NO = "NO";
    private static final String EAN2 = "222222222";

    @InjectMocks
    private StorePriorityFulfillment storePriorityFulfillment;

    @Before
    public void setUp() {
        storePriorityFulfillment = new StorePriorityFulfillment();
    }

    @Test
    public void allocateOrderParts_justEnoughStoreAvailability_storesArePrioritized() {
        // arrange
        OrderLineQuantityModel line = createOrderLine(EAN);
        OrderModel order = createOrderWithQuantities(line);
        Map<String, List<ItemAvailability>> eanAvailability = Map.of(EAN, List.of(
                createItemAvailability(WAREHOUSE, ANAGRAM_MINI, NO, Integer.MAX_VALUE),
                createItemAvailability(STORE, NODE, NO, CERTAIN_AMOUNT)));

        // act
        Map<String, List<List<OrderLineQuantityModel>>> parts = storePriorityFulfillment.allocateOrderParts(order, eanAvailability);

        // assert
        assertThat("Single part should go stores", parts, equalTo(Map.of(NODE, List.of(List.of(line)))));
    }

    @Test
    public void allocateOrderParts_lowStoreAvailability_returnEmptyMap() {
        // arrange
        OrderLineQuantityModel line = createOrderLine(EAN);
        OrderModel order = createOrderWithQuantities(line);
        Map<String, List<ItemAvailability>> eanAvailability = Map.of(EAN, List.of(
                createItemAvailability(STORE, NODE, NO, CERTAIN_AMOUNT - 1),
                createItemAvailability(WAREHOUSE, ANAGRAM_MINI, NO, 0)));

        // act
        Map<String, List<List<OrderLineQuantityModel>>> parts = storePriorityFulfillment.allocateOrderParts(order, eanAvailability);

        // assert
        assertThat("order parts map should be empty", parts,
                equalTo(Map.of()));
    }

    @Test
    public void allocateOrderParts_foreignStoreAvailability_returnEmptyMap() {
        // arrange
        OrderLineQuantityModel line = createOrderLine(EAN);
        OrderModel order = createOrderWithQuantities(line);
        Map<String, List<ItemAvailability>> eanAvailability = Map.of(EAN, List.of(
                createItemAvailability(STORE, NODE, COUNTRY, Integer.MAX_VALUE),
                createItemAvailability(WAREHOUSE, ANAGRAM_MINI, NO, 0)));

        // act
        Map<String, List<List<OrderLineQuantityModel>>> parts = storePriorityFulfillment.allocateOrderParts(order, eanAvailability);

        // assert
        assertThat("order parts map should be empty", parts,
                equalTo(Map.of()));
    }

    @Test
    public void allocateOrderParts_splitFriendlyAvailability_noSplitting() {
        // arrange
        OrderLineQuantityModel line1 = createOrderLine(EAN);
        OrderLineQuantityModel line2 = createOrderLine(EAN2);
        OrderModel order = createOrderWithQuantities(line1, line2);
        Map<String, List<ItemAvailability>> eanAvailability = Map.of(
                EAN, List.of(
                        createItemAvailability(STORE, NODE, NO, Integer.MAX_VALUE),
                        createItemAvailability(WAREHOUSE, ANAGRAM_MINI, NO, 0)),
                EAN2, List.of(
                        createItemAvailability(STORE, NODE, NO, 0),
                        createItemAvailability(WAREHOUSE, ANAGRAM_MINI, NO, Integer.MAX_VALUE)));

        // act
        Map<String, List<List<OrderLineQuantityModel>>> parts = storePriorityFulfillment.allocateOrderParts(order, eanAvailability);

        // assert
        assertThat("order parts map should be empty in splitting case", parts, equalTo(Map.of()));
    }

    private ItemAvailability createItemAvailability(Type type, String node, String country, int quantity) {
        return new ItemAvailability(quantity, country, type, node);
    }

    private OrderModel createOrderWithQuantities(OrderLineQuantityModel... quantities) {
        OrderModel order = new OrderModel();

        order.setShippingAddress(new AddressModel());
        order.getShippingAddress().setCountry(NO);

        order.setOrderLineQuantities(Arrays.asList(quantities));

        return order;
    }

    private OrderLineQuantityModel createOrderLine(String ean) {
        OrderLineQuantityModel line = new OrderLineQuantityModel();
        line.setEan(ean);
        line.setQuantity(CERTAIN_AMOUNT);
        return line;
    }

}
