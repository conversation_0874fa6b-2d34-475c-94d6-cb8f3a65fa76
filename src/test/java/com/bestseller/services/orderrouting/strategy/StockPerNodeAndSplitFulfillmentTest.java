package com.bestseller.services.orderrouting.strategy;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailability;
import com.bestseller.services.orderrouting.configuration.Warehouses;
import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.model.OrderModel;
import com.bestseller.services.orderrouting.utils.TestingFeatureManager;
import com.bestseller.services.orderrouting.utils.generator.OrderModelGenerator;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.togglz.core.context.StaticFeatureManagerProvider;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.bestseller.services.orderrouting.utils.generator.ItemAvailabilityGenerator.createItemAvailability;
import static java.util.stream.Collectors.toMap;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.allOf;
import static org.hamcrest.Matchers.contains;
import static org.hamcrest.Matchers.containsInAnyOrder;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.everyItem;
import static org.hamcrest.Matchers.hasEntry;
import static org.hamcrest.Matchers.hasProperty;
import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.lessThanOrEqualTo;

@RunWith(MockitoJUnitRunner.class)
public class StockPerNodeAndSplitFulfillmentTest {

    private static final String WOLFRAM_NECRO_FULFILLMENT_NODE = "WOLFRAM_NECRO_FULFILLMENT_NODE";
    private static final String SFS_NL_FULFILLMENT_NODE = "SHIP_FROM_STORE_NL";
    private static final int BIG_ORDER_LINES = 17;
    private static final int SFS_BIG_ORDER_LINES = 7;

    private static final int MAX_SFS_PART_SIZE = 6;

    private StockPerNodeFulfillmentFactory strategyFactory;

    private StockPerNodeAndSplitFulfillment strategy;

    private OrderModel order;
    private List<OrderLineQuantityModel> orderLines;
    private Map<String, List<ItemAvailability>> eanAvailability;

    @Before
    public void init() {
        StaticFeatureManagerProvider.setFeatureManager(new TestingFeatureManager());

        final Warehouses warehouses = new Warehouses();
        warehouses.setFulfillmentCenterEast(WOLFRAM_NECRO_FULFILLMENT_NODE);
        warehouses.setFulfillmentCenterWest("HOUSE_WEST");

        strategyFactory = new StockPerNodeFulfillmentFactory(warehouses);

        strategy = strategyFactory.stockPerNodeAndSplitFulfillment();
        strategy.setMaxSfsPartSize(MAX_SFS_PART_SIZE);

        order = OrderModelGenerator.createTestOrderModel();
        orderLines = OrderModelGenerator.createNOrderLines(UUID.randomUUID().toString(), BIG_ORDER_LINES);
        order.setOrderLineQuantities(orderLines);

        eanAvailability = orderLines.stream()
                .map(OrderLineQuantityModel::getEan)
                .collect(toMap(Function.identity(), ean ->
                        List.of(createItemAvailability(WOLFRAM_NECRO_FULFILLMENT_NODE, Integer.MAX_VALUE))));
    }

    @Test
    public void allocateOrderParts_oneEanAvailableStore_storePartCreatedWarehousePartNotSplit() {
        // arrange
        String nlEan = eanAvailability.keySet().iterator().next();
        eanAvailability.get(nlEan).get(0).setWarehouse(SFS_NL_FULFILLMENT_NODE);

        // act
        Map<String, List<List<OrderLineQuantityModel>>> parts = strategy.allocateOrderParts(order, eanAvailability);

        // assert
        assertThat("Wrong NL part", parts,
                hasEntry(equalTo(SFS_NL_FULFILLMENT_NODE), contains(contains(hasProperty("ean", equalTo(nlEan))))));
        assertThat("Too many warehouse parts", parts,
                hasEntry(equalTo(WOLFRAM_NECRO_FULFILLMENT_NODE), hasSize(1)));
        assertThat("Missing lines", lines(parts), containsInAnyOrder(orderLines.toArray()));
    }

    private static Set<OrderLineQuantityModel> lines(Map<String, List<List<OrderLineQuantityModel>>> parts) {
        return parts.values()
                .stream()
                .flatMap(Collection::stream)
                .flatMap(Collection::stream)
                .collect(Collectors.toSet());
    }

    @Test
    public void allocateOrderParts_manyEansAvailableStore_multipleSmallerPartsGoStore() {
        // arrange
        eanAvailability.values()
                .stream()
                .limit(SFS_BIG_ORDER_LINES)
                .forEach(item -> {
                        item.get(0).setWarehouse(SFS_NL_FULFILLMENT_NODE);
                        item.get(0).setType(ItemAvailability.Type.STORE);
                });

        // act
        Map<String, List<List<OrderLineQuantityModel>>> parts = strategy.allocateOrderParts(order, eanAvailability);

        // assert
        assertThat("Missing lines", lines(parts), containsInAnyOrder(orderLines.toArray()));
        assertThat("Store part too big", parts,
                hasEntry(equalTo(SFS_NL_FULFILLMENT_NODE), everyItem(hasSize(lessThanOrEqualTo(MAX_SFS_PART_SIZE)))));
        assertThat("Store part should split", parts, hasEntry(equalTo(SFS_NL_FULFILLMENT_NODE), hasSize(2)));
        assertThat("Non-single warehouse", parts, hasEntry(equalTo(WOLFRAM_NECRO_FULFILLMENT_NODE), hasSize(1)));
    }

    @Test
    public void allocateOrderParts_fewEansGoStore_noSplit() {
        // arrange
        orderLines = orderLines.subList(0, MAX_SFS_PART_SIZE);
        order.setOrderLineQuantities(orderLines);

        eanAvailability.values()
                .stream()
                .flatMap(Collection::stream)
                .forEach(itemAvailability -> itemAvailability.setWarehouse(SFS_NL_FULFILLMENT_NODE));

        // act
        Map<String, List<List<OrderLineQuantityModel>>> parts = strategy.allocateOrderParts(order, eanAvailability);

        // assert
        assertThat("Missing lines", lines(parts), containsInAnyOrder(orderLines.toArray()));
        assertThat("Missing single store part", parts, allOf(
                hasEntry(equalTo(SFS_NL_FULFILLMENT_NODE), contains(containsInAnyOrder(orderLines.toArray())))));
    }

    @Test
    public void allocateOrderParts_noAvailability_singleOrderPartGoesWarehouse() {
        // arrange
        eanAvailability.clear();

        // act
        Map<String, List<List<OrderLineQuantityModel>>> parts = strategy.allocateOrderParts(order, eanAvailability);

        // assert
        assertThat("Missing single warehouse part", parts, allOf(
                hasEntry(equalTo(WOLFRAM_NECRO_FULFILLMENT_NODE), contains(containsInAnyOrder(orderLines.toArray())))));
    }

}
