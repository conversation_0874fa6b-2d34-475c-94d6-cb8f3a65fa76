package com.bestseller.services.orderrouting.strategy;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailability;
import com.bestseller.services.orderrouting.configuration.Warehouses;
import com.bestseller.services.orderrouting.feature.toggles.ORSFeatures;
import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.model.OrderModel;
import com.bestseller.services.orderrouting.utils.TestingFeatureManager;
import com.bestseller.services.orderrouting.utils.generator.ItemAvailabilityGenerator;
import com.bestseller.services.orderrouting.utils.generator.OrderModelGenerator;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.togglz.core.context.StaticFeatureManagerProvider;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;

import static com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailability.Type.STORE;
import static com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailability.Type.WAREHOUSE;
import static com.bestseller.services.orderrouting.utils.generator.ItemAvailabilityGenerator.STORE_1;
import static com.bestseller.services.orderrouting.utils.generator.ItemAvailabilityGenerator.WAREHOUSE_1;
import static com.bestseller.services.orderrouting.utils.generator.ItemAvailabilityGenerator.WAREHOUSE_2;
import static com.bestseller.services.orderrouting.utils.generator.ItemAvailabilityGenerator.createItemAvailability;
import static java.util.stream.Collectors.toMap;
import static org.hamcrest.CoreMatchers.allOf;
import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.contains;
import static org.hamcrest.Matchers.containsInAnyOrder;
import static org.hamcrest.Matchers.hasEntry;
import static org.hamcrest.Matchers.hasSize;

@RunWith(MockitoJUnitRunner.class)
public class StockPerNodeFulfillmentTest {

    private static final String FULFILLMENT_CENTER_EAST = "HOUSE_EAST";
    private static final String FULFILLMENT_CENTER_WEST = "HOUSE_WEST";
    private static final String HOLOGRAM_AGGRO_FULFILLMENT_NODE = "HOLOGRAM_AGGRO_FULFILLMENT_NODE";
    private static final String WAREHOUSE_WEST = "INGRAM_MICRO_NL";
    private static final int FULL_STOCK = Integer.MAX_VALUE;
    private static final int ZERO_STOCK = 0;
    private static final int TEN_STOCK = 10;
    private static final int ORDER_LINE_QUANTITY = 10;
    private static final int SATISFYING_QUANTITY = ORDER_LINE_QUANTITY;
    private static final int HIGHER_SATISFYING_QUANTITY = SATISFYING_QUANTITY + 5;
    private static final String STORE_NETHERLANDS = "SHIP_FROM_STORE_NL";
    private static final String STORE_NORWAY = "SHIP_FROM_STORE_NO";
    private static final int MORE_THAN_ENOUGH = Integer.MAX_VALUE;

    private StockPerNodeFulfillmentFactory strategyFactory;

    private StockPerNodeFulfillment strategy;

    private OrderModel order;
    private List<OrderLineQuantityModel> orderLines;
    private Map<String, List<ItemAvailability>> eanAvailability;
    private OrderLineQuantityModel orderLine1;

    @Before
    public void setUp() {
        final Warehouses warehouses = new Warehouses();
        warehouses.setFulfillmentCenterEast(FULFILLMENT_CENTER_EAST);
        warehouses.setFulfillmentCenterWest(FULFILLMENT_CENTER_WEST);

        strategyFactory = new StockPerNodeFulfillmentFactory(warehouses);
        strategy = strategyFactory.warehouseFulfillment();

        order = OrderModelGenerator.createTestOrderModel();
        orderLines = OrderModelGenerator.createThreeTestOrderLines(order);
        order.setOrderLineQuantities(orderLines);
        orderLine1 = orderLines.iterator().next();

        eanAvailability = orderLines.stream()
                .map(OrderLineQuantityModel::getEan)
                .collect(toMap(Function.identity(), ean ->
                        List.of(createItemAvailability(FULFILLMENT_CENTER_EAST, Integer.MAX_VALUE))));

        StaticFeatureManagerProvider.setFeatureManager(new TestingFeatureManager());
    }

    @After
    public void tearDown() {
        ORSFeatures.PRIORITIZE_FCW_OVER_FCE.setActive(false);
    }

    @Test
    public void allocateOrderParts_enoughAvailability_nodeAssigned() {
        // arrange

        // act
        Map<String, List<List<OrderLineQuantityModel>>> parts = strategy.allocateOrderParts(order, eanAvailability);

        // assert
        assertThat(parts, hasEntry(equalTo(FULFILLMENT_CENTER_EAST), contains(containsInAnyOrder(orderLines.toArray()))));
    }

    @Test
    public void allocateOrderParts_enoughAvailabilityInTwoWarehouses_orderSplit() {
        // arrange
        OrderLineQuantityModel forthLine = OrderModelGenerator.createOneTestOrderLine(order);
        orderLines.add(forthLine);

        int expectedSizeForFirstWarehouse = eanAvailability.size();
        eanAvailability.put(forthLine.getEan(),
                List.of(ItemAvailabilityGenerator.createItemAvailability(HOLOGRAM_AGGRO_FULFILLMENT_NODE, Integer.MAX_VALUE)));

        int expectedSizeForSecondWarehouse = eanAvailability.size() - expectedSizeForFirstWarehouse;

        // act
        Map<String, List<List<OrderLineQuantityModel>>> parts = strategy.allocateOrderParts(order, eanAvailability);

        // assert
        assertThat(parts, allOf(
                hasEntry(equalTo(FULFILLMENT_CENTER_EAST), contains(hasSize(expectedSizeForFirstWarehouse))),
                hasEntry(equalTo(HOLOGRAM_AGGRO_FULFILLMENT_NODE), contains(hasSize(expectedSizeForSecondWarehouse)))
        ));
    }

    @Test
    public void allocateOrderParts_enoughAvailabilityInTwoWarehousesFcwEnabled_orderSplit() {
        // arrange
        ORSFeatures.PRIORITIZE_FCW_OVER_FCE.setActive(true);
        eanAvailability.values().forEach(availabilities ->
                availabilities.forEach(availability ->
                        availability.setType(WAREHOUSE)));

        // act - doesn't change
        allocateOrderParts_enoughAvailabilityInTwoWarehouses_orderSplit();

        // assert - same as fcw disabled
    }

    @Test
    public void allocateOrderParts_notEnoughAvailabilityForOneItem_nodeAssigned() {
        // arrange
        OrderLineQuantityModel orderLineQuantityModel = OrderModelGenerator.createOneTestOrderLine(order);
        orderLines.add(orderLineQuantityModel);
        eanAvailability.put(orderLineQuantityModel.getEan(),
                List.of(ItemAvailabilityGenerator.createItemAvailability(HOLOGRAM_AGGRO_FULFILLMENT_NODE, 0)));

        // act
        Map<String, List<List<OrderLineQuantityModel>>> parts = strategy.allocateOrderParts(order, eanAvailability);

        // assert
        assertThat("Wrong # of lines routed", parts,
                hasEntry(equalTo(FULFILLMENT_CENTER_EAST), contains(hasSize(eanAvailability.size()))));
    }

    @Test
    public void allocateOrderParts_notEnoughAvailability_nodeAssigned() {
        // arrange
        OrderLineQuantityModel orderLineQuantityModel = OrderModelGenerator.createOneTestOrderLine(order);
        orderLines.add(orderLineQuantityModel);
        eanAvailability = Map.of();

        // act
        Map<String, List<List<OrderLineQuantityModel>>> parts = strategy.allocateOrderParts(order, eanAvailability);

        // assert
        assertThat("Wrong # of lines routed", parts,
                hasEntry(equalTo(FULFILLMENT_CENTER_EAST), contains(hasSize(orderLines.size()))));
    }

    @Test
    public void allocateOrderParts_enoughForOnlyOne_nodesAssigned() {
        // arrange
        int expectedSizeForHermesWarehouse = orderLines.size();
        OrderLineQuantityModel extraLine = OrderModelGenerator.createOneTestOrderLine(order);
        orderLines.add(extraLine);
        eanAvailability = this.eanAvailability.keySet()
                .stream()
                .collect(toMap(Function.identity(), ean -> List.of(createItemAvailability(FULFILLMENT_CENTER_EAST, ZERO_STOCK))));
        eanAvailability.put(extraLine.getEan(),
                List.of(ItemAvailabilityGenerator.createItemAvailability(HOLOGRAM_AGGRO_FULFILLMENT_NODE, FULL_STOCK)));
        int expectedSizeForIMWarehouse = orderLines.size() - expectedSizeForHermesWarehouse;

        // act
        Map<String, List<List<OrderLineQuantityModel>>> parts = strategy.allocateOrderParts(order, this.eanAvailability);

        // assert
        assertThat("Wrong # of lines routed to Hermes", parts, hasEntry(equalTo(FULFILLMENT_CENTER_EAST),
                contains(hasSize(expectedSizeForHermesWarehouse))));
        assertThat("Wrong # of lines routed to HA", parts, hasEntry(equalTo(HOLOGRAM_AGGRO_FULFILLMENT_NODE),
                contains(hasSize(expectedSizeForIMWarehouse))));
    }

    @Test
    public void allocateOrderParts_enoughAvailableInTwoWarehousesWithFcwDisabled_assignToNodeWithMostAvailability() {
        // arrange
        ORSFeatures.PRIORITIZE_FCW_OVER_FCE.setActive(false);

        orderLine1.setQuantity(ORDER_LINE_QUANTITY);

        eanAvailability = Map.of(orderLine1.getEan(), List.of(
                createItemAvailability(WAREHOUSE_1, SATISFYING_QUANTITY),
                createItemAvailability(WAREHOUSE_2, HIGHER_SATISFYING_QUANTITY)));

        // act
        Map<String, List<List<OrderLineQuantityModel>>> parts = strategy.allocateOrderParts(order, eanAvailability);

        // assert
        assertThat(parts, hasEntry(equalTo(WAREHOUSE_2), contains(hasSize(1))));
    }

    @Test
    public void allocateOrderParts_enoughAvailableInTwoWarehousesWithFcwEnabled_assignToFulfillmentCenterWest() {
        // arrange
        ORSFeatures.PRIORITIZE_FCW_OVER_FCE.setActive(true);

        orderLine1.setQuantity(ORDER_LINE_QUANTITY);
        order.setOrderLineQuantities(List.of(orderLine1));

        eanAvailability = Map.of(orderLine1.getEan(), List.of(
                createItemAvailability(WAREHOUSE_1, SATISFYING_QUANTITY),
                createItemAvailability(WAREHOUSE_2, HIGHER_SATISFYING_QUANTITY)));

        // act
        Map<String, List<List<OrderLineQuantityModel>>> parts = strategy.allocateOrderParts(order, eanAvailability);

        // assert
        assertThat(parts, hasEntry(equalTo(WAREHOUSE_2), contains(hasSize(1))));
    }

    @Test
    public void allocateOrderParts_enoughAvailableInTwoWarehousesButForTheBecauseOfSecondIsBackorder_assignToNodeWithMostAvailability() {
        // arrange
        orderLine1.setQuantity(ORDER_LINE_QUANTITY);

        eanAvailability = Map.of(orderLine1.getEan(), List.of(
                    createItemAvailability(WAREHOUSE_1, SATISFYING_QUANTITY),
                    createItemAvailability(WAREHOUSE_2, ZERO_STOCK)));

        // act
        Map<String, List<List<OrderLineQuantityModel>>> parts = strategy.allocateOrderParts(order, eanAvailability);

        // assert
        assertThat(parts, hasEntry(equalTo(WAREHOUSE_1), contains(hasSize(1))));
    }

    @Test
    public void allocateOrderParts_notEnoughTotalQuantityInTwoWarehousesWithFcwDisabled_assignToNodeWithMostAvailability() {
        // arrange
        final int availableQuantityWarehouse1 = 8;
        final int availableQuantityWarehouse2 = 9;

        orderLine1.setQuantity(ORDER_LINE_QUANTITY);

        eanAvailability = Map.of(orderLine1.getEan(), List.of(
                    createItemAvailability(WAREHOUSE_1, availableQuantityWarehouse1),
                    createItemAvailability(WAREHOUSE_2, availableQuantityWarehouse2)));

        // act
        Map<String, List<List<OrderLineQuantityModel>>> parts = strategy.allocateOrderParts(order, eanAvailability);

        // assert
        assertThat("Unwanted split", parts, hasEntry(equalTo(WAREHOUSE_2), contains(hasSize(1))));
    }

    @Test
    public void allocateOrderParts_notEnoughTotalQuantityInTwoWarehousesWithFcwEnabled_assignToNodeWithMostAvailability() {
        // arrange
        ORSFeatures.PRIORITIZE_FCW_OVER_FCE.setActive(true);

        final int availableQuantityWarehouse1 = 8;
        final int availableQuantityWarehouse2 = 9;
        order.setOrderLineQuantities(List.of(orderLine1));

        orderLine1.setQuantity(ORDER_LINE_QUANTITY);

        eanAvailability = Map.of(orderLine1.getEan(), List.of(
                    createItemAvailability(WAREHOUSE_1, availableQuantityWarehouse1),
                    createItemAvailability(WAREHOUSE_2, availableQuantityWarehouse2)));

        // act
        Map<String, List<List<OrderLineQuantityModel>>> parts = strategy.allocateOrderParts(order, eanAvailability);

        // assert
        assertThat(parts, hasEntry(equalTo(WAREHOUSE_2), contains(hasSize(1))));
    }

    @Test
    public void allocateOrderParts_fcwCanFulfillSomeLinesFcwEnabled_unfulfillableLinesAreAlsoSentToFcw() {
        // arrange
        ORSFeatures.PRIORITIZE_FCW_OVER_FCE.setActive(true);

        orderLine1.setQuantity(ORDER_LINE_QUANTITY);

        eanAvailability = Map.of(orderLine1.getEan(), List.of(
                    createItemAvailability(FULFILLMENT_CENTER_WEST, orderLine1.getQuantity())));

        // act
        Map<String, List<List<OrderLineQuantityModel>>> parts = strategy.allocateOrderParts(order, eanAvailability);

        // assert
        assertThat(parts, hasEntry(equalTo(FULFILLMENT_CENTER_WEST), contains(containsInAnyOrder(orderLines.toArray()))));
    }

    @Test
    public void findNodeForLine_notEnoughWarehouseInventoryAndSosEligible_returnStore() {
        // arrange
        strategy = strategyFactory.shipFromStoreWarehousePriorityFulfillment();
        order.getShippingAddress().setCountry("NL");

        eanAvailability = Map.of(orderLine1.getEan(), List.of(
                // no availability in warehouse
                new ItemAvailability()
                        .withWarehouse(WAREHOUSE_1)
                        .withType(WAREHOUSE)
                        .withAvailableQuantity(0),
                // enough availability in stores
                new ItemAvailability()
                        .withWarehouse(STORE_NETHERLANDS)
                        .withType(STORE)
                        .withCountry("NL")
                        .withAvailableQuantity(MORE_THAN_ENOUGH)
        ));

        // act
        Optional<String> node = strategy.findNodeForLine(orderLine1, order, eanAvailability);

        // assert
        assertThat("Node should be store netherlands", node, equalTo(Optional.of(STORE_NETHERLANDS)));
    }

    @Test
    public void findNodeForLine_notEnoughWarehouseInventoryAndSosIneligible_returnStore() {
        // arrange
        order.getBillingAddress().setCountry("NL");
        eanAvailability = Map.of(orderLine1.getEan(), List.of(

                // no availability in warehouse
                new ItemAvailability()
                        .withWarehouse(WAREHOUSE_1)
                        .withType(WAREHOUSE)
                        .withAvailableQuantity(0),
                // enough availability in stores
                new ItemAvailability()
                        .withWarehouse(STORE_NETHERLANDS)
                        .withType(STORE)
                        .withAvailableQuantity(MORE_THAN_ENOUGH)
        ));

        // act
        Optional<String> node = strategy.findNodeForLine(orderLine1, order, eanAvailability);

        // assert
        assertThat("Node wrongly assigned", node, equalTo(Optional.empty()));
    }

    @Test
    public void findNodeForLine_enoughWarehouseInventoryAndSosEligible_returnStore() {
        // arrange
        order.getBillingAddress().setCountry("NL");
        eanAvailability = Map.of(orderLine1.getEan(), List.of(
                new ItemAvailability()
                        .withWarehouse(WAREHOUSE_1)
                        .withType(WAREHOUSE)
                        .withAvailableQuantity(1),
                new ItemAvailability()
                        .withWarehouse(STORE_NETHERLANDS)
                        .withType(STORE)
                        .withAvailableQuantity(MORE_THAN_ENOUGH)
        ));

        // act
        Optional<String> node = strategy.findNodeForLine(orderLine1, order, eanAvailability);

        // assert
        assertThat("Node should be warehouse 1", node, equalTo(Optional.of(WAREHOUSE_1)));
    }

    @Test
    public void findNodeForLine_enoughWarehouseInventoryAndSosIneligible_returnStore() {
        // arrange
        order.getBillingAddress().setCountry("NL");
        eanAvailability = Map.of(orderLine1.getEan(), List.of(
                new ItemAvailability()
                        .withWarehouse(WAREHOUSE_1)
                        .withType(WAREHOUSE)
                        .withAvailableQuantity(1),
                new ItemAvailability()
                        .withWarehouse(STORE_NETHERLANDS)
                        .withType(STORE)
                        .withAvailableQuantity(MORE_THAN_ENOUGH)
        ));

        // act
        Optional<String> node = strategy.findNodeForLine(orderLine1, order, eanAvailability);

        // assert
        assertThat("Node should be warehouse 1", node, equalTo(Optional.of(WAREHOUSE_1)));
    }

    @Test
    public void findNodeForLine_notEnoughStoreAndWarehouseInventoryAndSosEligible_returnStore() {
        // arrange
        strategy = strategyFactory.shipFromStoreWarehousePriorityFulfillment();
        order.getBillingAddress().setCountry("NL");
        eanAvailability = Map.of(orderLine1.getEan(), List.of(
                // no availability in warehouse
                new ItemAvailability()
                        .withWarehouse(WAREHOUSE_1)
                        .withType(WAREHOUSE)
                        .withAvailableQuantity(0),
                // no availability in stores
                new ItemAvailability()
                        .withWarehouse(STORE_NETHERLANDS)
                        .withType(STORE)
                        .withAvailableQuantity(0)
        ));

        // act
        Optional<String> node = strategy.findNodeForLine(orderLine1, order, eanAvailability);

        // assert
        assertThat("Node wrongly assigned", node, equalTo(Optional.empty()));
    }

    @Test
    public void findNodeForLine_enoughStoreInventoryAndSosEligible_returnStore() {
        // arrange
        strategy = strategyFactory.shipFromStoreWarehousePriorityFulfillment();
        order.getShippingAddress().setCountry("NL");
        eanAvailability = Map.of(orderLine1.getEan(), List.of(
                new ItemAvailability()
                        .withWarehouse("UndefinedWarehouse")
                        .withType(null)
                        .withAvailableQuantity(MORE_THAN_ENOUGH),
                new ItemAvailability()
                        .withWarehouse(STORE_NETHERLANDS)
                        .withType(STORE)
                        .withCountry("NL")
                        .withAvailableQuantity(TEN_STOCK)
        ));

        // act
        Optional<String> node = strategy.findNodeForLine(orderLine1, order, eanAvailability);

        // assert
        assertThat("Node should be store netherlands", node, equalTo(Optional.of(STORE_NETHERLANDS)));
    }

    @Test
    public void findNodeForLine_availableInStoresInOtherCountry_routeToFallbackWarehouse() {
        // arrange
        // order is generally eligible for SFS
        strategy = strategyFactory.shipFromStoreWarehousePriorityFulfillment();
        eanAvailability = Map.of(orderLine1.getEan(), List.of(
                // no availability in warehouse
                new ItemAvailability()
                        .withWarehouse(WAREHOUSE_1)
                        .withType(WAREHOUSE)
                        .withAvailableQuantity(0),
                // available in store in a different country
                new ItemAvailability()
                        .withWarehouse(STORE_1)
                        .withType(STORE)
                        .withCountry("NO")
                        .withAvailableQuantity(MORE_THAN_ENOUGH)
        ));

        // act
        Optional<String> node = strategy.findNodeForLine(orderLine1, order, eanAvailability);

        // assert
        assertThat("Node wrongly assigned", node, equalTo(Optional.empty()));
    }

    @Test
    public void findNodeForLine_enoughStoreInventoryAndSfsEligible_returnStore() {
        // arrange
        // order is generally eligible for SFS
        strategy = strategyFactory.shipFromStoreStorePriorityFulfillment();
        eanAvailability = generateWarehouseAndStoreItemsWithQuantity(MORE_THAN_ENOUGH);

        // act
        Optional<String> node = strategy.findNodeForLine(orderLine1, order, eanAvailability);

        // assert
        assertThat("Node should be store norway", node, equalTo(Optional.of(STORE_NORWAY)));
    }

    @Test
    public void findNodeForLine_notEnoughStoreStockAndEnoughWarehouseStockAndSfsEligible_returnWarehouse() {
        // arrange
        // order is generally eligible for SFS
        strategy = strategyFactory.shipFromStoreStorePriorityFulfillment();
        eanAvailability = generateWarehouseAndStoreItemsWithQuantity(0);

        // act
        Optional<String> node = strategy.findNodeForLine(orderLine1, order, eanAvailability);

        // assert
        assertThat("Node should be warehouse 1", node, equalTo(Optional.of(WAREHOUSE_1)));
    }

    @Test
    public void allocateOrderParts_oneLinesGoesWest_allLinesGoWest() {
        // arrange
        ORSFeatures.PRIORITIZE_FCW_OVER_FCE.setActive(true);
        var oneEanAvailableWest = Map.of(orderLine1.getEan(), List.of(createItemAvailability(WAREHOUSE_WEST, Integer.MAX_VALUE)));

        // act
        Map<String, List<List<OrderLineQuantityModel>>> parts = strategy.allocateOrderParts(order, oneEanAvailableWest);

        // assert
        assertThat("Some line didn't go West", parts, hasEntry(equalTo(WAREHOUSE_WEST), contains(containsInAnyOrder(orderLines.toArray()))));
    }

    @Test
    public void allocateOrderParts_noStockAnywhere_allLinesGoWest() {
        // arrange
        ORSFeatures.PRIORITIZE_FCW_OVER_FCE.setActive(true);
        var noStockAnywhere = Map.<String, List<ItemAvailability>>of();

        // act
        Map<String, List<List<OrderLineQuantityModel>>> parts = strategy.allocateOrderParts(order, noStockAnywhere);

        // assert
        assertThat("Lines didn't go West", parts, hasEntry(equalTo(FULFILLMENT_CENTER_WEST),
                contains(containsInAnyOrder(orderLines.toArray()))));
    }

    @Test
    public void allocateOrderParts_oneLineGoesNonWest_allLinesGoNonWest() {
        // arrange
        ORSFeatures.PRIORITIZE_FCW_OVER_FCE.setActive(true);
        var nonWest = "FULFILLMENT_CENTER_NORTH";
        var oneEanAvailableNonWest = Map.of(orderLine1.getEan(), List.of(createItemAvailability(nonWest, Integer.MAX_VALUE)));

        // act
        Map<String, List<List<OrderLineQuantityModel>>> parts = strategy.allocateOrderParts(order, oneEanAvailableNonWest);

        // assert
        assertThat("Lines didn't go non-West", parts, hasEntry(equalTo(nonWest), contains(containsInAnyOrder(orderLines.toArray()))));
    }

    private Map<String, List<ItemAvailability>> generateWarehouseAndStoreItemsWithQuantity(final int storeQuantity) {
        return Map.of(orderLine1.getEan(), List.of(
                // availability in warehouse
                new ItemAvailability().withWarehouse(WAREHOUSE_1)
                        .withType(WAREHOUSE).withCountry(
                                OrderModelGenerator.ShippingAddressProperties.SHIPPING_ADDRESS_COUNTRY)
                        .withAvailableQuantity(MORE_THAN_ENOUGH),
                // available in store in same country
                new ItemAvailability().withWarehouse(STORE_NORWAY).withType(STORE)
                        .withCountry(OrderModelGenerator.ShippingAddressProperties.SHIPPING_ADDRESS_COUNTRY)
                        .withAvailableQuantity(storeQuantity)));
    }
}
