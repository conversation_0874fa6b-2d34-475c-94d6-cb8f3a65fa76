package com.bestseller.services.orderrouting.messaging.producer;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailabilityRequest;
import com.bestseller.services.orderrouting.configuration.messaging.ItemAvailabilityChannels;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;

import java.util.Arrays;
import java.util.List;

import static com.bestseller.services.orderrouting.metric.CounterOperation.ITEM_AVAILABILITY_REQUEST_PRODUCED;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ItemAvailabilityRequestProducerTest {
    private static final int TOTAL_EANS = 3;

    private static final String ORDER_ID = "ORDER_ID";
    private static final String EAN_1 = "1234";
    private static final String EAN_2 = "12345";
    private static final String EAN_3 = "123456";

    @Mock
    private ItemAvailabilityChannels itemAvailabilityChannelsConfig;

    @Mock
    private MessageChannel messageChannel;

    @Captor
    private ArgumentCaptor<Message<ItemAvailabilityRequest>> captor;

    @Spy
    private MeterRegistry meterRegistry = new SimpleMeterRegistry();

    @InjectMocks
    private ItemAvailabilityRequestProducer itemAvailabilityRequestProducer;

    @Test
    public void produce_normalInputIsGiven_isSendingToKafkaTopic() {
        // arrange
        final ItemAvailabilityRequest itemAvailabilityRequest = new ItemAvailabilityRequest()
                .withCorrelationId(ORDER_ID).withEans(Arrays.asList(EAN_1, EAN_2, EAN_3));

        when(itemAvailabilityChannelsConfig.itemAvailabilityRequest()).thenReturn(messageChannel);

        // act
        itemAvailabilityRequestProducer.produce(itemAvailabilityRequest);

        // assert
        verify(itemAvailabilityChannelsConfig.itemAvailabilityRequest()).send(any());
        assertThat("The counter should match",
                meterRegistry.counter(ITEM_AVAILABILITY_REQUEST_PRODUCED).count(),
                equalTo(1.0));
    }

    @Test
    public void produce_normalInputIsGiven_correlationIdIsOrderId() {
        // arrange
        final ItemAvailabilityRequest itemAvailabilityRequest = new ItemAvailabilityRequest()
                .withCorrelationId(ORDER_ID).withEans(Arrays.asList(EAN_1, EAN_2, EAN_3));

        when(itemAvailabilityChannelsConfig.itemAvailabilityRequest()).thenReturn(messageChannel);

        // act
        itemAvailabilityRequestProducer.produce(itemAvailabilityRequest);
        verify(itemAvailabilityChannelsConfig.itemAvailabilityRequest()).send(captor.capture());

        ItemAvailabilityRequest message = captor.getValue().getPayload();

        // assert
        Assert.assertEquals("Correlation id is not the same as the order id", ORDER_ID, message.getCorrelationId());
        assertThat("The counter should match",
                meterRegistry.counter(ITEM_AVAILABILITY_REQUEST_PRODUCED).count(),
                equalTo(1.0));
    }

    @Test
    public void produce_duplicateEansInOrder_duplicatesAreFilteredOut() {
        // arrange
        List<String> eans = Arrays.asList(EAN_1, EAN_2, EAN_3);
        final ItemAvailabilityRequest itemAvailabilityRequest = new ItemAvailabilityRequest()
                .withCorrelationId(ORDER_ID).withEans(eans);

        when(itemAvailabilityChannelsConfig.itemAvailabilityRequest()).thenReturn(messageChannel);

        // act
        itemAvailabilityRequestProducer.produce(itemAvailabilityRequest);
        verify(itemAvailabilityChannelsConfig.itemAvailabilityRequest()).send(captor.capture());

        ItemAvailabilityRequest message = captor.getValue().getPayload();

        // assert
        Assert.assertEquals("Total found", TOTAL_EANS, message.getEans().size());
        Assert.assertTrue("Message", message.getEans().containsAll(eans));
        assertThat("The counter should match",
                meterRegistry.counter(ITEM_AVAILABILITY_REQUEST_PRODUCED).count(),
                equalTo(1.0));
    }
}
