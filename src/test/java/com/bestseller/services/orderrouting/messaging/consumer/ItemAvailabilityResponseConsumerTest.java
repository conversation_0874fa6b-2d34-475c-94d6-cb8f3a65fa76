package com.bestseller.services.orderrouting.messaging.consumer;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.Item;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailability;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailabilityResponse;
import com.bestseller.services.orderrouting.configuration.SfsConfig;
import com.bestseller.services.orderrouting.feature.toggles.ORSFeatures;
import com.bestseller.services.orderrouting.messaging.validation.ItemAvailabilityResponseValidator;
import com.bestseller.services.orderrouting.messaging.validation.ValidationException;
import com.bestseller.services.orderrouting.metric.TimedOperation;
import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.model.OrderLineQuantityStatus;
import com.bestseller.services.orderrouting.model.OrderModel;
import com.bestseller.services.orderrouting.repository.OrderModelRepository;
import com.bestseller.services.orderrouting.service.OrderService;
import com.bestseller.services.orderrouting.service.RulesService;
import com.bestseller.services.orderrouting.service.idempotency.ItemAvailabilityResponseIdempotencyService;
import com.bestseller.services.orderrouting.utils.TestingFeatureManager;
import com.bestseller.services.orderrouting.utils.verifier.FactsVerifier;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.jeasy.rules.api.Facts;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.dao.CannotAcquireLockException;
import org.togglz.core.context.StaticFeatureManagerProvider;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

import static com.bestseller.services.orderrouting.rules.facts.FactNames.ITEM_AVAILABILITY_RESPONSE_FACT;
import static com.bestseller.services.orderrouting.rules.facts.FactNames.ORDER_MODEL_FACT;
import static org.junit.Assert.assertTrue;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ItemAvailabilityResponseConsumerTest {
    private static final long TOLERANCE_MS = 1000L;
    // period defined in milliseconds
    private static final long PERIOD_MS = 5 * 1000;

    @Rule
    public ExpectedException thrown = ExpectedException.none();

    @Mock
    private ItemAvailabilityResponseValidator validator;

    @Spy
    private MeterRegistry meterRegistry = new SimpleMeterRegistry();

    @Mock
    private OrderService orderService;

    @Mock
    private ItemAvailabilityResponseIdempotencyService idempotencyService;

    @Mock
    private OrderModelRepository orderModelRepository;

    @Mock
    private RulesService rulesService;

    @Mock
    private SfsConfig sfsConfig;

    @InjectMocks
    private ItemAvailabilityResponseConsumer consumer;

    private OrderModel orderModel;
    private OrderLineQuantityModel orderLine1;
    private OrderLineQuantityModel orderLine2;
    private Facts facts;
    private ItemAvailabilityResponse response;

    @Before
    public void setUp() {
        orderLine1 = new OrderLineQuantityModel();
        orderLine1.setStatus(OrderLineQuantityStatus.ROUTING);

        orderLine2 = new OrderLineQuantityModel();
        orderLine2.setStatus(OrderLineQuantityStatus.ROUTING);

        orderModel = new OrderModel();
        orderModel.setOrderLineQuantities(new ArrayList<>());
        orderModel.getOrderLineQuantities().add(orderLine1);
        orderModel.getOrderLineQuantities().add(orderLine2);
        orderModel.setCreatedDate(Instant.ofEpochMilli(System.currentTimeMillis() - PERIOD_MS));

        response = new ItemAvailabilityResponse().withCorrelationId(UUID.randomUUID().toString());

        facts = new Facts();
        facts.put(ORDER_MODEL_FACT, orderModel);
        facts.put(ITEM_AVAILABILITY_RESPONSE_FACT, response);

        when(orderService.findOrderModel(response.getCorrelationId())).thenReturn(orderModel);
        when(orderModelRepository.existsById(response.getCorrelationId())).thenReturn(true);

        StaticFeatureManagerProvider.setFeatureManager(new TestingFeatureManager());
    }

    @Test
    public void receiveItemAvailabilityResponse_validationThrowsException_flowInterrupted() throws Exception {
        // arrange
        doThrow(ValidationException.class).when(validator).validate(response);

        // assert
        thrown.expect(ValidationException.class);

        // act
        consumer.receiveItemAvailabilityResponse(response);
    }

    @Test
    public void receiveItemAvailabilityResponse_transientExceptionThrown_loggedAndRethrown() throws Exception {
        // arrange
        doThrow(CannotAcquireLockException.class).when(validator).validate(response);

        // act
        assertThrows(CannotAcquireLockException.class, () ->
                consumer.receiveItemAvailabilityResponse(response));
    }

    @Test
    public void receiveItemAvailabilityResponse_allLinesRouted_eventPublished() throws Exception {
        // arrange - done in setup

        // act
        consumer.receiveItemAvailabilityResponse(response);

        // assert
        verify(validator).validate(response);
        verify(orderService).findOrderModel(response.getCorrelationId());
        verify(idempotencyService).checkDuplicateMessage(response);
        verify(orderService).saveOrderLines(orderModel.getOrderLineQuantities());
        verify(rulesService).executeOrderRoutingRules(eq(new FactsVerifier(facts)));
    }

    @Test
    public void receiveItemAvailabilityResponse_linesStillPlaced_eventNotPublished() throws Exception {
        // arrange
        orderModel.getOrderLineQuantities().forEach(o -> o.setStatus(OrderLineQuantityStatus.PLACED));

        // act
        consumer.receiveItemAvailabilityResponse(response);

        // assert
        verify(validator).validate(response);
        verify(orderService).findOrderModel(response.getCorrelationId());
        verify(idempotencyService).checkDuplicateMessage(response);
        verify(orderService).saveOrderLines(orderModel.getOrderLineQuantities());
    }

    @Test
    public void monitorInventoryIn_inboundMessageReceived_measuresTimesForResponse() throws Exception {
        // arrange

        // act
        consumer.receiveItemAvailabilityResponse(response);

        // assert
        verify(meterRegistry).timer(TimedOperation.INVENTORY_RESPONSE);

        var timer = meterRegistry.timer(TimedOperation.INVENTORY_RESPONSE).totalTime(TimeUnit.MILLISECONDS);

        long interval = (long) (timer - PERIOD_MS);

        // using fuzzy match to give a 100ms tolerance in the execution time of this unit test
        assertTrue("Should properly report the inventory response time", interval >= 0 && interval < TOLERANCE_MS);
    }

    @Test
    public void receiveItemAvailabilityResponse_allLinesCancelled_eventPublished() throws Exception {
        // arrange
        orderLine1.setStatus(OrderLineQuantityStatus.CANCELLED);
        orderLine2.setStatus(OrderLineQuantityStatus.CANCELLED);

        // act
        consumer.receiveItemAvailabilityResponse(response);

        // assert
        verify(validator).validate(response);
        verify(orderService).findOrderModel(response.getCorrelationId());
        verify(idempotencyService).checkDuplicateMessage(response);
        verify(orderService).saveOrderLines(orderModel.getOrderLineQuantities());
        verify(rulesService).executeOrderRoutingRules(eq(new FactsVerifier(facts)));
    }

    @Test
    public void receiveItemAvailabilityResponse_routingAndCancelledLinesGiven_twoEventsPublished() throws Exception {
        // arrange
        orderLine1.setStatus(OrderLineQuantityStatus.ROUTING);
        orderLine2.setStatus(OrderLineQuantityStatus.CANCELLED);

        // act
        consumer.receiveItemAvailabilityResponse(response);

        // assert
        verify(validator).validate(response);
        verify(orderService).findOrderModel(response.getCorrelationId());
        verify(idempotencyService).checkDuplicateMessage(response);
        verify(orderService).saveOrderLines(orderModel.getOrderLineQuantities());
    }

    @Test
    public void receiveItemAvailabilityResponse_duplicateMessageExists_messageNotProcessed() throws Exception {
        // arrange
        when(idempotencyService.checkDuplicateMessage(response)).thenReturn(true);

        // act
        consumer.receiveItemAvailabilityResponse(response);

        // assert
        verify(validator).validate(response);
        verify(idempotencyService).checkDuplicateMessage(response);
        verify(orderService, never()).findOrderModel(response.getCorrelationId());
        verify(orderService, never()).saveOrderLines(orderModel.getOrderLineQuantities());
        verifyNoInteractions(meterRegistry);
    }

    @Test
    public void receiveItemAvailabilityResponse_crossDockOrder_warehouseResponseCreated() throws Exception {
        // arrange
        orderModel.setIsOrderAdvice(true);

        // act
        consumer.receiveItemAvailabilityResponse(response);

        // assert
        verify(validator).validate(response);
        verify(orderService).findOrderModel(response.getCorrelationId());
        verify(idempotencyService).checkDuplicateMessage(response);
    }

    @Test
    public void receiveItemAvailabilityResponse_orderNotExist_messageIgnored() throws Exception {
        // arrange
        when(orderModelRepository.existsById(response.getCorrelationId())).thenReturn(false);

        // act
        consumer.receiveItemAvailabilityResponse(response);

        // assert
        verify(validator, never()).validate(response);
        verifyNoInteractions(meterRegistry);
    }

    @Test
    public void receiveItemAvailabilityResponse_messageNotContainWarehousesWithZeroAvailability_messageEnriched() throws Exception {
        // arrange
        final int numberOfFulfillmentCenters = 3;
        var itemAvailabilityResponse = new ItemAvailabilityResponse()
                .withCorrelationId(UUID.randomUUID().toString())
                .withItems(
                        List.of(
                                new Item()
                                        .withEan("1")
                                        .withItemAvailability(
                                                List.of(
                                                        new ItemAvailability()
                                                                .withType(ItemAvailability.Type.STORE)
                                                                .withWarehouse("SHIP_FROM_STORE_NO")
                                                                .withAvailableQuantity(1)
                                                                .withCountry("NO")
                                                )
                                        ),
                                new Item()
                                        .withEan("2")
                                        .withItemAvailability(
                                                List.of(
                                                        new ItemAvailability()
                                                                .withType(ItemAvailability.Type.WAREHOUSE)
                                                                .withWarehouse("INGRAM_MICRO")
                                                                .withAvailableQuantity(1)
                                                                .withCountry("PL")
                                                )
                                        )
                        )
                );
        when(sfsConfig.getAllCountries()).thenReturn(Set.of("NO"));

        // act
        consumer.enrichTheMessageIfNecessary(itemAvailabilityResponse);

        // assert
        assert itemAvailabilityResponse.getItems().get(0).getItemAvailability().size() == numberOfFulfillmentCenters;
        assert itemAvailabilityResponse.getItems().get(1).getItemAvailability().size() == numberOfFulfillmentCenters;
        assert itemAvailabilityResponse.getItems().get(0).getItemAvailability().stream()
                .anyMatch(itemAvailability ->
                        itemAvailability.getAvailableQuantity() == 0
                        && itemAvailability.getWarehouse().equals("INGRAM_MICRO")
                );
        assert itemAvailabilityResponse.getItems().get(0).getItemAvailability().stream()
                .anyMatch(itemAvailability ->
                        itemAvailability.getAvailableQuantity() == 0
                                && itemAvailability.getWarehouse().equals("INGRAM_MICRO_NL")
                );
        assert itemAvailabilityResponse.getItems().get(1).getItemAvailability().stream()
                .anyMatch(itemAvailability ->
                        itemAvailability.getAvailableQuantity() == 0
                                && itemAvailability.getWarehouse().equals("SHIP_FROM_STORE_NO")
                );
        assert itemAvailabilityResponse.getItems().get(1).getItemAvailability().stream()
                .anyMatch(itemAvailability ->
                        itemAvailability.getAvailableQuantity() == 0
                                && itemAvailability.getWarehouse().equals("INGRAM_MICRO_NL")
                );
    }

    @Test
    public void receiveItemAvailabilityResponse_ignoreFcwEnabled_fcwStockSetToZeroAndItemsSorted() {
        // arrange
        ORSFeatures.IGNORE_FCW_AVAILABILITY.setActive(true);
        var itemAvailabilityResponse = new ItemAvailabilityResponse()
                .withCorrelationId(UUID.randomUUID().toString())
                .withItems(
                        List.of(
                                new Item()
                                        .withEan("2")
                                        .withItemAvailability(
                                                List.of(
                                                    new ItemAvailability()
                                                        .withType(ItemAvailability.Type.WAREHOUSE)
                                                        .withWarehouse("INGRAM_MICRO_NL")
                                                        .withAvailableQuantity(1)
                                                        .withCountry("NL"),
                                                    new ItemAvailability()
                                                        .withType(ItemAvailability.Type.WAREHOUSE)
                                                        .withWarehouse("INGRAM_MICRO")
                                                        .withAvailableQuantity(1)
                                                        .withCountry("PL")
                                                )
                                        )
                        )
                );

        // act
        consumer.setFcwAvailabilityToZeroAndSortNodesIfNecessary(itemAvailabilityResponse);

        // assert
        assert itemAvailabilityResponse.getItems().get(0).getItemAvailability().stream()
                .anyMatch(itemAvailability ->
                        itemAvailability.getAvailableQuantity() == 0
                                && itemAvailability.getWarehouse().equals("INGRAM_MICRO_NL")
                );

        assert itemAvailabilityResponse.getItems().get(0).getItemAvailability().get(0)
            .getWarehouse().equals("INGRAM_MICRO_NL");
    }

    @Test
    public void receiveItemAvailabilityResponse_ignoreFcwDisabled_noChanges() {
        // arrange
        ORSFeatures.IGNORE_FCW_AVAILABILITY.setActive(false);
        var itemAvailabilityResponse = new ItemAvailabilityResponse()
                .withCorrelationId(UUID.randomUUID().toString())
                .withItems(
                        List.of(
                                new Item()
                                        .withEan("2")
                                        .withItemAvailability(
                                                List.of(
                                                    new ItemAvailability()
                                                        .withType(ItemAvailability.Type.WAREHOUSE)
                                                        .withWarehouse("INGRAM_MICRO_NL")
                                                        .withAvailableQuantity(1)
                                                        .withCountry("NL"),
                                                    new ItemAvailability()
                                                        .withType(ItemAvailability.Type.WAREHOUSE)
                                                        .withWarehouse("INGRAM_MICRO")
                                                        .withAvailableQuantity(1)
                                                        .withCountry("PL")
                                                )
                                        )
                        )
                );

        // act
        consumer.setFcwAvailabilityToZeroAndSortNodesIfNecessary(itemAvailabilityResponse);

        // assert
        assert itemAvailabilityResponse.getItems().get(0).getItemAvailability().stream()
                .noneMatch(itemAvailability ->
                        itemAvailability.getAvailableQuantity() == 0
                                && itemAvailability.getWarehouse().equals("INGRAM_MICRO_NL")
                );

        assert itemAvailabilityResponse.getItems().get(0).getItemAvailability().get(0)
            .getWarehouse().equals("INGRAM_MICRO_NL");
    }
}
