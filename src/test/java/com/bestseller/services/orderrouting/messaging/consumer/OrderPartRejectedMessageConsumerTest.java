package com.bestseller.services.orderrouting.messaging.consumer;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.read.ListAppender;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartRejected.OrderPartRejected;
import com.bestseller.services.orderrouting.converter.OrderConverter;
import com.bestseller.services.orderrouting.messaging.validation.OrderPartRejectedMessageValidator;
import com.bestseller.services.orderrouting.messaging.validation.ValidationException;
import com.bestseller.services.orderrouting.metric.CounterOperation;
import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.model.OrderLineQuantityStatus;
import com.bestseller.services.orderrouting.model.OrderModel;
import com.bestseller.services.orderrouting.rules.facts.FactNames;
import com.bestseller.services.orderrouting.service.CancellationReason;
import com.bestseller.services.orderrouting.service.OrderService;
import com.bestseller.services.orderrouting.service.RulesService;
import com.bestseller.services.orderrouting.service.idempotency.MessageIdempotencyService;
import com.bestseller.services.orderrouting.util.OrderValueCalculator;
import com.bestseller.services.orderrouting.utils.generator.OrderModelGenerator;
import com.bestseller.services.orderrouting.utils.generator.OrderPartRejectedGenerator;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tag;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.apache.commons.lang.math.NumberUtils;
import org.assertj.core.api.Assertions;
import org.jeasy.rules.api.Facts;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.List;

import static com.bestseller.services.orderrouting.service.CancellationReason.ITEM_NOT_AVAILABLE;
import static com.bestseller.services.orderrouting.utils.generator.OrderModelGenerator.OrderLineProperties.LINE_1_EAN;
import static com.bestseller.services.orderrouting.utils.generator.OrderModelGenerator.OrderLineProperties.LINE_2_EAN;
import static com.bestseller.services.orderrouting.utils.generator.OrderModelGenerator.OrderLineProperties.LINE_3_EAN;
import static java.lang.String.format;
import static org.assertj.core.groups.Tuple.tuple;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.hasItem;
import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class OrderPartRejectedMessageConsumerTest {

    private static final String WAREHOUSE = "SHIP_FROM_STORE_NL";

    @Rule
    public ExpectedException thrown = ExpectedException.none();

    @Mock
    private RulesService rulesService;

    @Mock
    private OrderService orderService;

    @Mock
    private OrderPartRejectedMessageValidator validator;

    @Mock
    private MessageIdempotencyService<OrderPartRejected> orderPartRejectedIdempotency;

    private Logger logger;

    private ListAppender<ILoggingEvent> appender;

    @Spy
    private MeterRegistry meterRegistry = new SimpleMeterRegistry();

    @Mock
    private OrderValueCalculator orderValueCalculator;

    @Mock
    private OrderConverter orderConverter;

    @Captor
    private ArgumentCaptor<Facts> factsCaptor;

    @Captor
    private ArgumentCaptor<List<Tag>> tagsCaptor;

    @Captor
    private ArgumentCaptor<List<OrderLineQuantityModel>> orderLinesCaptor;

    @InjectMocks
    private OrderPartRejectedMessageConsumer consumer;

    private OrderPartRejected orderPartRejected;
    private OrderModel order;
    private List<OrderLineQuantityModel> orderLines;

    @Before
    public void setUp() {
        appender = new ListAppender<>();
        logger = (Logger) LoggerFactory.getLogger(consumer.getClass());
        logger.addAppender(appender);
        appender.start();

        order = OrderModelGenerator.createTestOrderModel();

        orderLines = OrderModelGenerator.createThreeTestOrderLines(order);
        orderLines.forEach(olqm -> olqm.setStatus(OrderLineQuantityStatus.ROUTED));
        orderLines.forEach(olqm -> olqm.setOrderPartNumber(NumberUtils.INTEGER_ONE));

        order.setOrderLineQuantities(orderLines);

        orderPartRejected = OrderPartRejectedGenerator.createFullOrderPartRejectedMessage(order.getOrderId(), WAREHOUSE,
                                                                                          LINE_1_EAN, LINE_2_EAN, LINE_3_EAN);

        when(orderConverter.deepCopyOrderModel(any())).thenReturn(order);
        order.getOrderLineQuantities().forEach(orderLine -> when(orderConverter.deepCopyOrderQuantityModel(orderLine)).thenReturn(orderLine));
    }

    @Test
    public void tearDown() {
        logger.detachAppender(appender);
    }

    @Test
    public void receiveOrderPartRejected_partialQty_parametersAreRecorded() throws ValidationException {
        // arrange
        BigDecimal orderValue = BigDecimal.valueOf(Math.E);
        orderPartRejected.getOrderLines().forEach(ol -> {
            ol.setCancelReason(ITEM_NOT_AVAILABLE.getReason());
            ol.setQuantity(1);
        });

        when(orderService.findOrderModel(orderPartRejected.getOrderId())).thenReturn(order);
        when(orderValueCalculator.totalOrderCost(any())).thenReturn(orderValue);

        // act
        consumer.receiveOrderPartRejected(orderPartRejected);

        // assert
        verify(orderConverter).deepCopyOrderModel(order);
        order.getOrderLineQuantities().forEach(orderLineQuantityModel -> verify(orderConverter).deepCopyOrderQuantityModel(orderLineQuantityModel));
        verify(orderValueCalculator).totalOrderCost(orderLinesCaptor.capture());
        List<OrderLineQuantityModel> orderLineQuantityModels = orderLinesCaptor.getValue();

        assertThat("Order parts cancelled size should match", orderLineQuantityModels,
                   hasSize(orderPartRejected.getOrderLines().size()));
        OrderLineQuantityModel actualLine = orderLineQuantityModels.get(0);
        OrderLineQuantityModel orderLine = order.getOrderLineQuantities().get(0);

        assertThat("Quantity should be 1", actualLine.getQuantity(), equalTo(1));
        assertThat("Retail price should match", actualLine.getRetailPrice(), equalTo(orderLine.getRetailPrice()));
        assertThat("Discount value should match", actualLine.getDiscountValue(), equalTo(orderLine.getDiscountValue()));

        verify(meterRegistry).counter(eq(CounterOperation.ORDER_PART_REJECTED_CONSUMED), tagsCaptor.capture());
        var oprTags = tagsCaptor.getAllValues().get(0);
        assertThat("Total order lines should match", oprTags,
                hasItem(Tag.of("totalOrderLines", String.valueOf(orderLines.size()))));
        assertThat("Cancellation reason should match", oprTags, hasItem(Tag.of("cancellationReason",
                CancellationReason.ITEM_NOT_AVAILABLE.getReason())));
        assertThat("Origin warehouse should match", oprTags, hasItem(Tag.of("originWarehouse", WAREHOUSE)));

        verify(meterRegistry).counter(eq(CounterOperation.TOTAL_ORDER_CANCELLED_VALUE), tagsCaptor.capture());
        var totalCancelledValueTags = tagsCaptor.getAllValues().get(1);
        assertThat("Warehouse should match", totalCancelledValueTags, hasItem(Tag.of("warehouse", WAREHOUSE)));
        assertThat("Total cancelled value is reported",
                meterRegistry.counter(CounterOperation.TOTAL_ORDER_CANCELLED_VALUE, totalCancelledValueTags).count(),
                equalTo(orderValue.doubleValue()));
    }

    @Test(expected = ValidationException.class)
    public void receiveOrderPartRejected_invalidMessage_errorIsThrown() throws ValidationException {
        // arrange
        doThrow(new ValidationException("Message not valid")).when(validator).validate(orderPartRejected);

        // act
        consumer.receiveOrderPartRejected(orderPartRejected);
        verify(orderConverter, never()).deepCopyOrderModel(order);
        order.getOrderLineQuantities().forEach(orderLineQuantityModel -> verify(orderConverter, never())
                .deepCopyOrderQuantityModel(orderLineQuantityModel));
        // assert (exception is thrown so these should not be called)
        verify(rulesService, never()).executeOrderPartRejectedRules(any(Facts.class));
        verify(orderService, never()).saveOrderModel(any(OrderModel.class));
    }

    @Test
    public void receiveOrderPartRejected_duplicateMessage_messageIsNotProcessedFurther() throws ValidationException {
        // arrange
        when(orderPartRejectedIdempotency.checkDuplicateMessage(orderPartRejected)).thenReturn(true);

        // act
        consumer.receiveOrderPartRejected(orderPartRejected);

        // assert
        verify(orderConverter, never()).deepCopyOrderModel(order);
        order.getOrderLineQuantities().forEach(orderLineQuantityModel -> verify(orderConverter, never())
                .deepCopyOrderQuantityModel(orderLineQuantityModel));
        verify(rulesService, never()).executeOrderPartRejectedRules(any(Facts.class));
        verify(orderService, never()).saveOrderModel(any(OrderModel.class));
    }

    @Test
    public void receiveOrderPartRejected_correctMessage_messageIsPresentedToRuleEngine() throws ValidationException {
        // arrange
        when(orderService.findOrderModel(orderPartRejected.getOrderId())).thenReturn(order);
        when(orderValueCalculator.totalOrderCost(any())).thenReturn(BigDecimal.TEN);

        // act
        consumer.receiveOrderPartRejected(orderPartRejected);
        verify(orderConverter).deepCopyOrderModel(order);
        order.getOrderLineQuantities().forEach(orderLineQuantityModel -> verify(orderConverter).deepCopyOrderQuantityModel(orderLineQuantityModel));
        verify(rulesService).executeOrderPartRejectedRules(factsCaptor.capture());

        Facts facts = factsCaptor.getValue();

        // assert
        assertThat("Order model should match", facts.get(FactNames.ORDER_MODEL_FACT), is(order));
        assertThat("Order parts cancelled should match", facts.get(FactNames.ORDER_PART_REJECTED_FACT),
                   is(orderPartRejected));
    }

    @Test
    public void receiveOrderPartRejected_orderProcessingFails_orderIsRevertedInStorage() throws ValidationException {
        // arrange
        when(orderService.findOrderModel(orderPartRejected.getOrderId())).thenReturn(order);
        when(orderValueCalculator.totalOrderCost(any())).thenReturn(BigDecimal.TEN);
        doThrow(new TestException("Message processing failed")).when(rulesService).executeOrderPartRejectedRules(any(Facts.class));

        // act
        assertThrows(TestException.class, () -> consumer.receiveOrderPartRejected(orderPartRejected));

        // assert
        verify(orderConverter).deepCopyOrderModel(order);
        order.getOrderLineQuantities().forEach(orderLineQuantityModel -> verify(orderConverter)
                .deepCopyOrderQuantityModel(orderLineQuantityModel));

        String message = format("Thrown error! Reverting order %s to original state.", order.getOrderId());
        Assertions.assertThat(appender.list)
                .extracting(ILoggingEvent::getLevel, ILoggingEvent::getFormattedMessage)
                .containsExactly(tuple(Level.ERROR, message));
        verify(orderService).saveOrderModel(order);
    }

    private class TestException extends RuntimeException {
        /* default */ TestException(String message) {
            super(message);
        }
    }
}
