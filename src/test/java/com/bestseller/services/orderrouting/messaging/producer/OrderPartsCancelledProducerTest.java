package com.bestseller.services.orderrouting.messaging.producer;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartsCancelled.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartsCancelled.OrderPartsCancelled;
import com.bestseller.services.orderrouting.configuration.messaging.OrderCancellingChannels;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.messaging.MessageChannel;

import java.time.ZonedDateTime;
import java.util.Collections;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class OrderPartsCancelledProducerTest {

    private static final String WAREHOUSE = "Node";

    private static final String EAN = "EAN";

    private static final ZonedDateTime DATE = ZonedDateTime.parse("2018-03-30T10:00:00Z");

    @Mock
    private OrderCancellingChannels orderCancellingChannels;

    @Mock
    private MessageChannel messageChannel;

    @InjectMocks
    private OrderPartsCancelledProducer orderPartsCancelledProducer;

    private OrderPartsCancelled orderPartsCancelled;

    @Before
    public void setUp() {
        orderPartsCancelled = createMinValidOrderPartsCancelledMessage();
    }

    @Test
    public void produce_normalInputIsGiven_isSendingToKafkaTopic() {
        // arrange - partially done in setup
        when(orderCancellingChannels.orderPartsCancelled()).thenReturn(messageChannel);

        // act
        orderPartsCancelledProducer.produce(orderPartsCancelled);

        // assert
        verify(messageChannel).send(ArgumentMatchers.any());
    }

    private OrderPartsCancelled createMinValidOrderPartsCancelledMessage() {
        OrderLine orderLine = new OrderLine().withLineNumber(1).withQuantity(1).withEan(EAN);
        return new OrderPartsCancelled()
                .withOrderId("1")
                .withWarehouse(WAREHOUSE)
                .withCancellationDate(DATE)
                .withOrderLines(Collections.singletonList(orderLine));
    }
}
