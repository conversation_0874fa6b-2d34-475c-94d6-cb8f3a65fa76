package com.bestseller.services.orderrouting.messaging.producer;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsRouted.OrderPartsRouted;
import com.bestseller.services.orderrouting.configuration.messaging.OrderChannels;
import com.bestseller.services.orderrouting.converter.OrderConverter;
import com.bestseller.services.orderrouting.metric.CounterOperation;
import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.util.OrderValueCalculator;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tag;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.apache.commons.lang.math.NumberUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.messaging.MessageChannel;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.hasItem;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class OrderPartsRoutedProducerTest {
    private static final String WAREHOUSE_NAME = "WAREHOUSE_1";
    private static final double TOTAL_ORDER_LINES = 2;

    @Mock
    private MessageChannel messageChannel;

    @Mock
    private OrderChannels orderChannels;

    @Spy
    private MeterRegistry meterRegistry = new SimpleMeterRegistry();

    @Mock
    private OrderValueCalculator orderValueCalculator;

    @Mock
    private OrderConverter orderConverter;

    @Mock
    private List<OrderLineQuantityModel> convertedQuantities;

    @InjectMocks
    private OrderPartsRoutedProducer orderPartsRoutedProducer;

    @Captor
    private ArgumentCaptor<List<Tag>> tagsCaptor;

    private OrderPartsRouted orderPartsRouted;

    @Before
    public void setUp() {
        orderPartsRouted = new OrderPartsRouted();
        orderPartsRouted.setFulfillmentNode(WAREHOUSE_NAME);

        List<OrderLine> orderLines = Arrays.asList(new OrderLine().withEan("EAN_1"), new OrderLine().withEan("EAN_2"));
        orderPartsRouted.setOrderLines(orderLines);
    }

    @Test
    public void produce_validInput_sendToKafkaTopic() {
        // arrange
        when(orderChannels.orderPartsRouted()).thenReturn(messageChannel);
        BigDecimal orderValue = BigDecimal.valueOf(Math.sqrt(2));
        when(orderValueCalculator.totalOrderCost(any())).thenReturn(orderValue);
        when(orderConverter.convertRoutedLines(any())).thenReturn(convertedQuantities);

        // act
        orderPartsRoutedProducer.produce(orderPartsRouted);

        // assert
        verify(orderConverter).convertRoutedLines(orderPartsRouted.getOrderLines());
        verify(orderValueCalculator).totalOrderCost(convertedQuantities);
        verify(messageChannel, times(NumberUtils.INTEGER_ONE)).send(any());

        verify(meterRegistry).counter(eq(CounterOperation.ORDER_PARTS_ROUTED_PRODUCED), tagsCaptor.capture());
        var oprTags = tagsCaptor.getAllValues().get(0);
        assertThat("OPR counter is increased",
                meterRegistry.counter(CounterOperation.ORDER_PARTS_ROUTED_PRODUCED, oprTags).count(), equalTo((double) 1));
        assertThat("Warehouse is reported", oprTags, hasItem(Tag.of("warehouse", WAREHOUSE_NAME)));

        verify(meterRegistry).counter(eq(CounterOperation.ORDER_PARTS_ROUTED_LINES), tagsCaptor.capture());
        var oprLinesTags = tagsCaptor.getAllValues().get(1);
        assertThat("OPR Lines counter is increased",
                meterRegistry.counter(CounterOperation.ORDER_PARTS_ROUTED_LINES, oprLinesTags).count(), equalTo(TOTAL_ORDER_LINES));
        assertThat("Warehouse is reported", oprLinesTags, hasItem(Tag.of("warehouse", WAREHOUSE_NAME)));

        verify(meterRegistry).counter(eq(CounterOperation.ORDER_PARTS_ROUTED_VALUE), tagsCaptor.capture());
        var oprValueTags = tagsCaptor.getAllValues().get(2);
        assertThat("OPR Lines counter is increased",
                meterRegistry.counter(CounterOperation.ORDER_PARTS_ROUTED_VALUE, oprValueTags).count(), equalTo(orderValue.doubleValue()));
        assertThat("Warehouse is reported", oprValueTags, hasItem(Tag.of("warehouse", WAREHOUSE_NAME)));
    }
}
