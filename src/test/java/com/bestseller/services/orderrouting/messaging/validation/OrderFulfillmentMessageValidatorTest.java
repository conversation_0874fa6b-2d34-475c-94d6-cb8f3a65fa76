package com.bestseller.services.orderrouting.messaging.validation;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderForFulfillment.OrderForFulfillment;
import com.bestseller.services.orderrouting.utils.ExpectedExceptionUtils;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import static com.bestseller.services.orderrouting.messaging.validation.ValidationException.ONE_OF_THE_FIELDS_MUST_BE_SET_TEMPLATE;
import static com.bestseller.services.orderrouting.utils.generator.OrderForFulfillmentGenerator.createFullOrderForFulfillmentMessage;

/**
 * Tests for {@link OrderFulfillmentMessageValidator} class.
 */
@RunWith(MockitoJUnitRunner.class)
public class OrderFulfillmentMessageValidatorTest {
    private static final String BILLING_COUNTRY = "billingCountry";
    private static final String SHIPPING_COUNTRY = "shippingCountry";

    @Rule
    public ExpectedException thrown = ExpectedException.none();

    @InjectMocks
    private OrderFulfillmentMessageValidator orderFulfillmentMessageValidator;

    @Test
    public void validate_bothBillingShippingCountryNull_exceptionThrown() throws ValidationException {
        // arrange
        OrderForFulfillment message = createFullOrderForFulfillmentMessage();
        message.getCustomerInformation().getBillingAddress().setCountry(null);
        message.getShippingInformation().getShippingAddress().setCountry(null);

        ExpectedExceptionUtils.expect(thrown, ValidationException.class, ONE_OF_THE_FIELDS_MUST_BE_SET_TEMPLATE,
                                      BILLING_COUNTRY, SHIPPING_COUNTRY);

        // act
        orderFulfillmentMessageValidator.validate(message);

        // assert - expected exception
    }

    @Test
    public void validate_shippingCountryNull_noExceptionsThrown() throws ValidationException {
        // arrange
        OrderForFulfillment message = createFullOrderForFulfillmentMessage();
        message.getShippingInformation().getShippingAddress().setCountry(null);

        // act
        orderFulfillmentMessageValidator.validate(message);

        // assert - no exceptions thrown
    }

    @Test
    public void validate_billingCountryNull_noExceptionsThrown() throws ValidationException {
        // arrange
        OrderForFulfillment message = createFullOrderForFulfillmentMessage();
        message.getCustomerInformation().getBillingAddress().setCountry(null);

        // act
        orderFulfillmentMessageValidator.validate(message);

        // assert - no exceptions thrown
    }

    @Test
    public void validate_validFullMessage_noExceptionsThrown() throws ValidationException {
        // arrange
        OrderForFulfillment message = createFullOrderForFulfillmentMessage();

        // act
        orderFulfillmentMessageValidator.validate(message);

        // assert: no exceptions thrown
    }

    @Test
    public void validate_retailPriceNull_exceptionThrown() throws ValidationException {
        //arrange
        OrderForFulfillment message = createFullOrderForFulfillmentMessage();
        message.getOrderLines().get(0).setRetailPrice(null);

        thrown.expect(ValidationException.class);
        thrown.expectMessage("Price");
        thrown.expectMessage(message.getOrderId());

        //act
        orderFulfillmentMessageValidator.validate(message);
    }

    @Test
    public void validate_discountValueNull_exceptionThrown() throws ValidationException {
        //arrange
        OrderForFulfillment message = createFullOrderForFulfillmentMessage();
        message.getOrderLines().get(0).setDiscountValue(null);

        thrown.expect(ValidationException.class);
        thrown.expectMessage("discount");
        thrown.expectMessage(message.getOrderId());

        //act
        orderFulfillmentMessageValidator.validate(message);
    }
}
