package com.bestseller.services.orderrouting.messaging.exceptions.handler;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessagingException;
import org.springframework.messaging.handler.annotation.support.MethodArgumentNotValidException;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class KafkaValidationErrorHandlerTest {

    private KafkaValidationErrorHandler errorHandler;
    private Message<Object> mockMessage;

    @Before
    public void setUp() {
        errorHandler = new KafkaValidationErrorHandler();
        mockMessage = mock(Message.class);
    }

    @Test
    public void handleError_WithMethodArgumentNotValidException_ShouldLogValidationError() {
        MethodArgumentNotValidException validationException = mock(MethodArgumentNotValidException.class);
        when(validationException.getMessage()).thenReturn("Validation failed");
        when(mockMessage.getPayload()).thenReturn(validationException);
        errorHandler.handleError(mockMessage);
        verify(mockMessage).getPayload();
        verify(validationException).getCause();
    }

    @Test
    public void handleError_WithRuntimeExceptionAndThrowable_ShouldLogUnhandledError() {
        RuntimeException genericException = new RuntimeException("Something went wrong");
        when(mockMessage.getPayload()).thenReturn(genericException);
        errorHandler.handleError(mockMessage);
        verify(mockMessage).getPayload();
    }

    @Test
    public void handleError_WithNonThrowablePayload_ShouldLogUnknownType() {
        String nonThrowable = "not an exception";
        when(mockMessage.getPayload()).thenReturn(nonThrowable);
        errorHandler.handleError(mockMessage);
        verify(mockMessage).getPayload();
    }

    @Test
    public void handleError_WithMessagingExceptionWithCause_ShouldHandleCause() {
        MethodArgumentNotValidException cause = mock(MethodArgumentNotValidException.class);
        when(cause.getMessage()).thenReturn("Validation failed");
        MessagingException messagingException = mock(MessagingException.class);
        when(messagingException.getCause()).thenReturn(cause);
        when(mockMessage.getPayload()).thenReturn(messagingException);
        errorHandler.handleError(mockMessage);
        verify(mockMessage).getPayload();
        verify(messagingException).getCause();
    }

    @Test
    public void handleError_WithMessagingExceptionWithoutCause_ShouldHandleMessagingException() {
        MessagingException messagingException = mock(MessagingException.class);
        when(messagingException.getCause()).thenReturn(null);
        when(mockMessage.getPayload()).thenReturn(messagingException);
        errorHandler.handleError(mockMessage);
        verify(mockMessage).getPayload();
        verify(messagingException, times(2)).getCause();
    }
}
