package com.bestseller.services.orderrouting.messaging.consumer;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderForFulfillment.OrderForFulfillment;
import com.bestseller.services.orderrouting.feature.toggles.ORSFeatures;
import com.bestseller.services.orderrouting.messaging.exceptions.ProcessingException;
import com.bestseller.services.orderrouting.messaging.validation.ValidationException;
import com.bestseller.services.orderrouting.messaging.validation.Validator;
import com.bestseller.services.orderrouting.model.OrderModel;
import com.bestseller.services.orderrouting.service.OrderService;
import com.bestseller.services.orderrouting.service.RulesService;
import com.bestseller.services.orderrouting.service.idempotency.OrderForFulfillmentMessageIdempotencyService;
import com.bestseller.services.orderrouting.utils.TestingFeatureManager;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.apache.commons.lang.math.NumberUtils;
import org.jeasy.rules.api.Facts;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.core.convert.converter.Converter;
import org.springframework.dao.CannotAcquireLockException;
import org.springframework.messaging.MessagingException;
import org.togglz.core.context.StaticFeatureManagerProvider;

import static com.bestseller.services.orderrouting.metric.CounterOperation.ORDER_FOR_FULFILLMENT_CONSUMED;
import static com.bestseller.services.orderrouting.rules.facts.FactNames.ORDER_FOR_FULFILLMENT_FACT;
import static com.bestseller.services.orderrouting.rules.facts.FactNames.ORDER_MODEL_FACT;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.junit.Assert.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class OrderForFulfillmentMessageConsumerTest {
    private static final TestingFeatureManager TESTING_FEATURE_MANAGER = new TestingFeatureManager();

    @Rule
    public ExpectedException thrown = ExpectedException.none();

    @InjectMocks
    private OrderForFulfillmentMessageConsumer orderForFulfillmentMessageConsumer;

    @Mock
    private OrderForFulfillment orderForFulfillment;

    @Mock
    private OrderService orderService;

    @Mock
    private Validator<OrderForFulfillment> orderForFulfillmentValidator;

    @Mock
    private OrderForFulfillmentMessageIdempotencyService messageIdempotencyService;

    @Mock
    private Converter<OrderForFulfillment, OrderModel> orderConverter;

    @Mock
    private RulesService rulesService;

    @Mock
    private OrderModel orderModel;

    @Spy
    private MeterRegistry meterRegistry = new SimpleMeterRegistry();

    @Before
    public void setUp() {
        StaticFeatureManagerProvider.setFeatureManager(TESTING_FEATURE_MANAGER);
    }

    @Test
    public void receiveOrderForFulfillment_normalOrderIsGiven_orderPlacingRulesExecuted()
            throws ValidationException, ProcessingException {
        // arrange
        when(messageIdempotencyService.checkDuplicateMessage(any(OrderForFulfillment.class))).thenReturn(false);
        when(orderConverter.convert(orderForFulfillment)).thenReturn(orderModel);
        ArgumentCaptor<Facts> argumentCaptor = ArgumentCaptor.forClass(Facts.class);

        // act
        orderForFulfillmentMessageConsumer.receiveOrderForFulfillment(orderForFulfillment);

        // assert
        verify(rulesService).executeOrderPlacingRules(argumentCaptor.capture());
        OrderModel insertedOrderModel = verify(orderService, times(NumberUtils.INTEGER_ONE)).saveOrderModel(orderModel);
        assertEquals(insertedOrderModel, argumentCaptor.getValue().get(ORDER_MODEL_FACT));
        assertEquals(orderForFulfillment, argumentCaptor.getValue().get(ORDER_FOR_FULFILLMENT_FACT));
        assertThat("The counter should match",
                meterRegistry.counter(ORDER_FOR_FULFILLMENT_CONSUMED).count(),
                equalTo(1.0));
    }

    @Test
    public void receiveOrderForFulfillment_exceptionThrownByRules_logExceptionAndRethrow()
            throws ValidationException, ProcessingException {
        // arrange
        when(messageIdempotencyService.checkDuplicateMessage(any(OrderForFulfillment.class))).thenReturn(false);
        MessagingException messagingException = new MessagingException("");
        doThrow(messagingException).when(rulesService).executeOrderPlacingRules(any());
        // Prepare the expected exception.
        thrown.expect(ProcessingException.class);

        // act
        orderForFulfillmentMessageConsumer.receiveOrderForFulfillment(orderForFulfillment);

        // assert
        verifyNoInteractions(meterRegistry);
    }

    @Test
    public void receiveOrderForFulfillment_transientException_logExceptionAndRethrow()
            throws ValidationException, ProcessingException {
        // arrange
        when(messageIdempotencyService.checkDuplicateMessage(any(OrderForFulfillment.class))).thenReturn(false);
        doThrow(CannotAcquireLockException.class).when(rulesService).executeOrderPlacingRules(any());

        // act
        assertThrows(CannotAcquireLockException.class, () ->
                orderForFulfillmentMessageConsumer.receiveOrderForFulfillment(orderForFulfillment));
        verifyNoInteractions(meterRegistry);
    }

    @Test
    public void receiveOrderForFulfillment_duplicateOrderForFulfillmentMessage_messageNotProcessed()
            throws ValidationException, ProcessingException {
        //arrange
        ORSFeatures.ORDER_FOR_FULFILLMENT_IDEMPOTENCY_CHECK_REQUIRED.setActive(true);
        when(messageIdempotencyService.checkDuplicateMessage(any(OrderForFulfillment.class))).thenReturn(true);

        // act
        orderForFulfillmentMessageConsumer.receiveOrderForFulfillment(orderForFulfillment);

        // assert
        verify(orderService, never()).saveOrderModel(any(OrderModel.class));
        verify(rulesService, never()).executeOrderPlacingRules(any());
        verifyNoInteractions(meterRegistry);
    }

    @Test
    public void receiveOrderForFulfillment_duplicateOrderForFulfillmentMessage_messageProcessed()
            throws ValidationException, ProcessingException {
        //arrange
        ORSFeatures.ORDER_FOR_FULFILLMENT_IDEMPOTENCY_CHECK_REQUIRED.setActive(false);
        when(messageIdempotencyService.checkDuplicateMessage(any(OrderForFulfillment.class))).thenReturn(true);

        // act
        orderForFulfillmentMessageConsumer.receiveOrderForFulfillment(orderForFulfillment);

        // assert
        verify(orderService, never()).saveOrderModel(any(OrderModel.class));
        verify(rulesService).executeOrderPlacingRules(any());
        assertThat("The counter should match",
                meterRegistry.counter(ORDER_FOR_FULFILLMENT_CONSUMED).count(),
                equalTo(1.0));
    }
}
