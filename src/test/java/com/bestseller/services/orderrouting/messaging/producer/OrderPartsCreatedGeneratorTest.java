package com.bestseller.services.orderrouting.messaging.producer;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsCreated.OrderPart;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsCreated.OrderPartsCreated;
import com.bestseller.services.orderrouting.configuration.messaging.OrderChannels;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.messaging.MessageChannel;

import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class OrderPartsCreatedGeneratorTest {
    private static final String WAREHOUSE_NAME = "WAREHOUSE_1";

    @Mock
    private MessageChannel messageChannel;

    @Mock
    private OrderChannels orderChannels;

    @InjectMocks
    private OrderPartsCreatedProducer orderPartsCreatedProducer;

    private OrderPartsCreated orderPartsCreated;

    @Before
    public void setUp() {
        orderPartsCreated = new OrderPartsCreated();
        orderPartsCreated.setFulfillmentNode(WAREHOUSE_NAME);

        List<OrderLine> orderLines = Arrays.asList(new OrderLine().withEan("EAN_1"), new OrderLine().withEan("EAN_2"));

        OrderPart orderPart = new OrderPart();
        orderPart.setOrderPartNumber(1);
        orderPart.setOrderLines(orderLines);

        orderPartsCreated.setOrderParts(Arrays.asList(orderPart));
    }

    @Test
    public void produce_validInput_sendToKafkaTopic() {
        // arrange
        when(orderChannels.orderPartsCreated()).thenReturn(messageChannel);

        // act
        orderPartsCreatedProducer.produce(orderPartsCreated);

        // assert
        verify(messageChannel).send(any());
    }
}
