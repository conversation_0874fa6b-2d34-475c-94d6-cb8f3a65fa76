package com.bestseller.services.orderrouting.messaging.validation;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartRejected.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartRejected.OrderPartRejected;
import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.model.OrderLineQuantityStatus;
import com.bestseller.services.orderrouting.model.OrderModel;
import com.bestseller.services.orderrouting.repository.OrderModelRepository;
import com.bestseller.services.orderrouting.service.CancellationReason;
import com.bestseller.services.orderrouting.utils.generator.OrderModelGenerator;
import com.bestseller.services.orderrouting.utils.generator.OrderPartRejectedGenerator;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static com.bestseller.services.orderrouting.constants.FulfillmentNodeConstants.SHIP_FROM_STORE_NL;
import static com.bestseller.services.orderrouting.messaging.validation.ValidationException.NON_POSITIVE_FIELD_TEMPLATE;
import static com.bestseller.services.orderrouting.messaging.validation.ValidationException.NULL_OR_EMPTY_FIELD_TEMPLATE;
import static com.bestseller.services.orderrouting.model.OrderLineQuantityStatus.CANCELLED;
import static com.bestseller.services.orderrouting.model.OrderLineQuantityStatus.ROUTED;
import static com.bestseller.services.orderrouting.utils.ExpectedExceptionUtils.expect;
import static com.bestseller.services.orderrouting.utils.generator.OrderPartRejectedGenerator.createFullOrderLine;
import static org.hamcrest.Matchers.allOf;
import static org.hamcrest.Matchers.containsString;
import static org.mockito.Mockito.when;

/**
 * Tests for {@link OrderPartRejectedMessageValidator} class.
 */
@RunWith(MockitoJUnitRunner.class)
public class OrderPartRejectedMessageValidatorTest {
    private static final String UNKNOWN_CANCELLATION_REASON = "UNKNOWN_CANCELLATION_REASON";

    @Rule
    public ExpectedException thrown = ExpectedException.none();

    @Mock
    private OrderModelRepository orderModelRepository;

    @InjectMocks
    private OrderPartRejectedMessageValidator orderPartRejectedMessageValidator;

    private OrderPartRejected testMessage;
    private OrderModel orderModel;

    @Before
    public void setUp() {
        orderModel = OrderModelGenerator.createTestOrderModel();

        List<OrderLineQuantityModel> orderLines = OrderModelGenerator.createThreeTestOrderLines(orderModel);
        orderLines.forEach(orderLineQuantityModel -> orderLineQuantityModel.setStatus(ROUTED));
        orderModel.setOrderLineQuantities(orderLines);

        String orderId = orderModel.getOrderId();
        testMessage = OrderPartRejectedGenerator.createFullOrderPartRejectedMessage(orderId, SHIP_FROM_STORE_NL,
                Arrays.asList(orderLines.get(0).getEan(), orderLines.get(2).getEan()));
        testMessage.setOrderId(orderId);

        when(orderModelRepository.findById(orderId)).thenReturn(Optional.of(orderModel));
    }

    @Test
    public void validate_validFullMessage_noExceptionsThrown() throws ValidationException {
        // arrange (done in setup)


        // act
        orderPartRejectedMessageValidator.validate(testMessage);

        // assert - no exceptions thrown
    }

    @Test
    public void validate_unknownCancellationReasons_exceptionsThrown() throws ValidationException {
        testMessage.getOrderLines().forEach(orderLine -> orderLine.setCancelReason(UNKNOWN_CANCELLATION_REASON));

        thrown.expect(ValidationException.class);
        thrown.expectMessage(UNKNOWN_CANCELLATION_REASON);
        thrown.expectMessage("Unknown");

        // act
        orderPartRejectedMessageValidator.validate(testMessage);

        // assert - exception thrown
    }

    @Test
    public void validate_mixedCancellationReasons_exceptionsThrown() throws ValidationException {
        // arrange
        testMessage.getOrderLines().get(0).setCancelReason(CancellationReason.STORE_REJECTION.getReason());

        thrown.expect(ValidationException.class);
        thrown.expectMessage(testMessage.getOrderId());
        thrown.expectMessage("mixed");

        // act
        orderPartRejectedMessageValidator.validate(testMessage);

        // assert - exception thrown
    }

    @Test
    public void validate_orderLineStatesAreNotRouted_exceptionsThrown() throws ValidationException {
        // arrange
        orderModel.getOrderLineQuantities()
                .forEach(orderLineQuantityModel -> orderLineQuantityModel.setStatus(OrderLineQuantityStatus.ROUTING));

        thrown.expect(ValidationException.class);
        thrown.expectMessage(orderModel.getOrderId());
        thrown.expectMessage(ROUTED.toString());
        thrown.expectMessage(CANCELLED.toString());

        // act
        orderPartRejectedMessageValidator.validate(testMessage);

        // assert - exception thrown
    }

    @Test
    public void validate_missingEansInMessage_exceptionsThrown() throws ValidationException {
        // arrange
        testMessage.getOrderLines().get(0).setEan(OrderModelGenerator.OrderLineProperties.LINE_4_EAN);

        thrown.expect(ValidationException.class);
        thrown.expectMessage(allOf(
                containsString("OrderPartRejected"),
                containsString(testMessage.getOrderId()),
                containsString(testMessage.getOrderLines().iterator().next().getEan())));

        // act
        orderPartRejectedMessageValidator.validate(testMessage);
    }

    @Test
    public void validate_missingOrderLines_exceptionThrown() throws ValidationException {
        // arrange
        testMessage.setOrderLines(null);
        expect(thrown, ValidationException.class, NULL_OR_EMPTY_FIELD_TEMPLATE,
               "OrderPartRejected.orderLines");

        // act
        orderPartRejectedMessageValidator.validate(testMessage);

        // assert - exception expected
    }

    @Test
    public void validate_emptyOrderLines_exceptionThrown() throws ValidationException {
        // arrange
        testMessage.setOrderLines(Collections.emptyList());
        expect(thrown, ValidationException.class, NULL_OR_EMPTY_FIELD_TEMPLATE,
               "OrderPartRejected.orderLines");

        // act
        orderPartRejectedMessageValidator.validate(testMessage);

        // assert - exception expected
    }

    @Test
    public void validate_missingEan_exceptionThrown() throws ValidationException {
        // arrange
        List<OrderLine> orderLines = new ArrayList<>();
        OrderLine orderLineWithMissingEan = createFullOrderLine();
        orderLineWithMissingEan.setEan(null);
        orderLines.add(orderLineWithMissingEan);
        testMessage.setOrderLines(orderLines);

        expect(thrown, ValidationException.class, NULL_OR_EMPTY_FIELD_TEMPLATE,
                "OrderPartRejected.orderLines.ean");

        // act
        orderPartRejectedMessageValidator.validate(testMessage);

        // assert - exception expected
    }

    @Test
    public void validate_missingLineNumber_exceptionThrown() throws ValidationException {
        // arrange
        List<OrderLine> orderLines = new ArrayList<>();
        OrderLine orderLineWithMissingLineNumber = createFullOrderLine();
        orderLineWithMissingLineNumber.setLineNumber(null);
        orderLines.add(orderLineWithMissingLineNumber);
        testMessage.setOrderLines(orderLines);

        expect(thrown, ValidationException.class, NULL_OR_EMPTY_FIELD_TEMPLATE,
                "OrderPartRejected.orderLines.lineNumber");

        // act
        orderPartRejectedMessageValidator.validate(testMessage);

        // assert - exception expected
    }

    @Test
    public void validate_missingQuantity_exceptionThrown() throws ValidationException {
        // arrange
        List<OrderLine> orderLines = new ArrayList<>();
        OrderLine orderLineWithMissingQuantity = createFullOrderLine();
        orderLineWithMissingQuantity.setQuantity(null);
        orderLines.add(orderLineWithMissingQuantity);
        testMessage.setOrderLines(orderLines);

        expect(thrown, ValidationException.class, NULL_OR_EMPTY_FIELD_TEMPLATE,
                "OrderPartRejected.orderLines.quantity");

        // act
        orderPartRejectedMessageValidator.validate(testMessage);

        // assert - exception expected
    }

    @Test
    public void validate_negativeQuantity_exceptionThrown() throws ValidationException {
        // arrange
        List<OrderLine> orderLines = new ArrayList<>();
        OrderLine orderLineWithMissingQuantity = createFullOrderLine();
        orderLineWithMissingQuantity.setQuantity(-1);
        orderLines.add(orderLineWithMissingQuantity);
        testMessage.setOrderLines(orderLines);

        expect(thrown, ValidationException.class, NON_POSITIVE_FIELD_TEMPLATE,
                "OrderPartRejected.orderLines.quantity");

        // act
        orderPartRejectedMessageValidator.validate(testMessage);

        // assert - exception expected
    }

    @Test
    public void validate_missingCancelReason_exceptionThrown() throws ValidationException {
        // arrange
        List<OrderLine> orderLines = new ArrayList<>();
        OrderLine orderLineWithMissingCancelReason = createFullOrderLine();
        orderLineWithMissingCancelReason.setCancelReason(null);
        orderLines.add(orderLineWithMissingCancelReason);
        testMessage.setOrderLines(orderLines);

        expect(thrown, ValidationException.class, NULL_OR_EMPTY_FIELD_TEMPLATE,
                "OrderPartRejected.orderLines.cancelReason");

        // act
        orderPartRejectedMessageValidator.validate(testMessage);

        // assert - exception expected
    }

    @Test
    public void validate_emptyCancelReason_exceptionThrown() throws ValidationException {
        // arrange
        List<OrderLine> orderLines = new ArrayList<>();
        OrderLine orderLineWithEmptyCancelReason = createFullOrderLine();
        orderLineWithEmptyCancelReason.setCancelReason("");
        orderLines.add(orderLineWithEmptyCancelReason);
        testMessage.setOrderLines(orderLines);

        expect(thrown, ValidationException.class, NULL_OR_EMPTY_FIELD_TEMPLATE,
                "OrderPartRejected.orderLines.cancelReason");

        // act
        orderPartRejectedMessageValidator.validate(testMessage);

        // assert - exception expected
    }

    @Test
    public void validate_missingWarehouse() throws ValidationException {
        validateWarehouse(null);
    }

    @Test
    public void validate_blankWarehouse() throws ValidationException {
        validateWarehouse(" \t");
    }

    private void validateWarehouse(String invalidWarehouse) throws ValidationException {
        // invalidate the message
        testMessage.setWarehouse(invalidWarehouse);

        // validation should go off because of the warehouse field
        expect(thrown, ValidationException.class, NULL_OR_EMPTY_FIELD_TEMPLATE, "OrderPartRejected.warehouse");

        orderPartRejectedMessageValidator.validate(testMessage);
    }
}
