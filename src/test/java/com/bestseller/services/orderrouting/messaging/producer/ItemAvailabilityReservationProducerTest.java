package com.bestseller.services.orderrouting.messaging.producer;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailabilityReservation;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.Reservation;
import com.bestseller.services.orderrouting.configuration.messaging.ItemAvailabilityChannels;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;

import java.util.Arrays;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ItemAvailabilityReservationProducerTest {

    private static final int ONE_TIME = 1;
    private static final int TOTAL_EANS = 3;

    private static final String ORDER_ID = "ORDER_ID";
    private static final String EAN_1 = "1234";
    private static final String EAN_2 = "12345";
    private static final String EAN_3 = "123456";

    @Mock
    private ItemAvailabilityChannels itemAvailabilityChannelsConfig;

    @Mock
    private MessageChannel messageChannel;

    @Captor
    private ArgumentCaptor<Message<ItemAvailabilityReservation>> captor;

    @InjectMocks
    private ItemAvailabilityReservationProducer itemAvailabilityReservationProducer;

    @Test
    public void produce_normalInputIsGiven_isSendingToKafkaTopic() {

        //arrange
        final ItemAvailabilityReservation itemAvailabilityReservation = createItemAvailabilityReservation();
        when(itemAvailabilityChannelsConfig.itemAvailabilityReservation()).thenReturn(messageChannel);

        //act
        itemAvailabilityReservationProducer.produce(itemAvailabilityReservation);

        //assert
        verify(itemAvailabilityChannelsConfig.itemAvailabilityReservation(), times(ONE_TIME)).send(any());
    }

    @Test
    public void produce_normalInputIsGiven_correlationIdIsOrderId() {

        //arrange
        final ItemAvailabilityReservation itemAvailabilityReservation = createItemAvailabilityReservation();
        when(itemAvailabilityChannelsConfig.itemAvailabilityReservation()).thenReturn(messageChannel);

        //act
        itemAvailabilityReservationProducer.produce(itemAvailabilityReservation);
        verify(itemAvailabilityChannelsConfig.itemAvailabilityReservation(), times(ONE_TIME)).send(captor.capture());
        ItemAvailabilityReservation message = captor.getValue().getPayload();

        //assert
        Assert.assertEquals("Correlation Id is not same as the order Id", ORDER_ID, message.getCorrelationId());
    }

    private ItemAvailabilityReservation createItemAvailabilityReservation() {
        return new ItemAvailabilityReservation()
                .withCorrelationId(ORDER_ID).withReservations(Arrays.asList(
                        new Reservation().withEan(EAN_1).withQuantity(1),
                        new Reservation().withEan(EAN_2).withQuantity(1),
                        new Reservation().withEan(EAN_3).withQuantity(1)
                        ));
    }
}
