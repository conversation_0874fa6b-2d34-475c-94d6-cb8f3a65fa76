package com.bestseller.services.orderrouting.messaging.validation;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.Item;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailability;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailabilityResponse;
import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.model.OrderModel;
import com.bestseller.services.orderrouting.repository.OrderModelRepository;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailability.Type.STORE;
import static com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailability.Type.WAREHOUSE;
import static com.bestseller.services.orderrouting.utils.generator.ItemAvailabilityGenerator.FULL_AVAILABILITY;
import static com.bestseller.services.orderrouting.utils.generator.ItemAvailabilityGenerator.WAREHOUSE_1;
import static com.bestseller.services.orderrouting.utils.generator.ItemAvailabilityGenerator.WAREHOUSE_2;
import static com.bestseller.services.orderrouting.utils.generator.ItemAvailabilityGenerator.ZERO_AVAILABILITY;
import static com.bestseller.services.orderrouting.utils.generator.ItemAvailabilityGenerator.createItem;
import static com.bestseller.services.orderrouting.utils.generator.ItemAvailabilityGenerator.createItemAvailability;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ItemAvailabilityResponseValidatorTest {
    private static final String ORDER_LINE_EAN_1 = "ORDER-LINE-EAN-1";
    private static final String ORDER_LINE_EAN_2 = "ORDER-LINE-EAN-2";
    private static final String TEST_EAN = "TEST-EAN";
    private static final String BLANK_ID = " ";
    private static final String EMPTY_ID = "";
    private static final String COUNTRY = "NTE";
    private static final String STORE_1 = "STORE-1";
    private static final String STORE_2 = "STORE-2";

    @Rule
    public ExpectedException thrown = ExpectedException.none();

    @Mock
    private OrderModelRepository orderModelRepository;

    @InjectMocks
    private ItemAvailabilityResponseValidator validator;

    private ItemAvailabilityResponse testMessage;
    private Item testItem;
    private ItemAvailability testItemAvailability;

    @Before
    public void init() {
        testItemAvailability = createItemAvailability(WAREHOUSE_1, FULL_AVAILABILITY, WAREHOUSE, COUNTRY);
        testItem = createItem(TEST_EAN, testItemAvailability);
        testMessage = new ItemAvailabilityResponse()
                .withCorrelationId(UUID.randomUUID().toString())
                .withItems(Arrays.asList(testItem));
    }

    @Test
    public void validate_nullCorrelationId_exceptionThrown() throws Exception {
        // arrange
        testMessage.setCorrelationId(null);

        // assert
        thrown.expect(ValidationException.class);
        thrown.expectMessage("\"correlationId\" must not be empty or null!");

        // act
        validator.validate(testMessage);
    }

    @Test
    public void validate_emptyCorrelationId_exceptionThrown() throws Exception {
        // arrange
        testMessage.setCorrelationId(EMPTY_ID);

        // assert
        thrown.expect(ValidationException.class);
        thrown.expectMessage("\"correlationId\" must not be empty or null!");

        // act
        validator.validate(testMessage);
    }

    @Test
    public void validate_blankCorrelationId_exceptionThrown() throws Exception {
        // arrange
        testMessage.setCorrelationId(BLANK_ID);

        // assert
        thrown.expect(ValidationException.class);
        thrown.expectMessage("\"correlationId\" must not be empty or null!");

        // act
        validator.validate(testMessage);
    }

    @Test
    public void validate_nullItems_exceptionThrown() throws Exception {
        // arrange
        testMessage.setItems(null);

        // assert
        thrown.expect(ValidationException.class);
        thrown.expectMessage("\"items\" must not be empty or null!");

        // act
        validator.validate(testMessage);
    }

    @Test
    public void validate_emptyItems_exceptionThrown() throws Exception {
        // arrange
        testMessage.setItems(new ArrayList<>());

        // assert
        thrown.expect(ValidationException.class);
        thrown.expectMessage("\"items\" must not be empty or null!");

        // act
        validator.validate(testMessage);
    }

    @Test
    public void validate_nullEan_exceptionThrown() throws Exception {
        // arrange
        testItem.setEan(null);

        // assert
        thrown.expect(ValidationException.class);
        thrown.expectMessage("\"item.ean\" must not be empty or null!");

        // act
        validator.validate(testMessage);
    }

    @Test
    public void validate_emptyEan_exceptionThrown() throws Exception {
        // arrange
        testItem.setEan(EMPTY_ID);

        // assert
        thrown.expect(ValidationException.class);
        thrown.expectMessage("\"item.ean\" must not be empty or null!");

        // act
        validator.validate(testMessage);
    }

    @Test
    public void validate_blankEan_exceptionThrown() throws Exception {
        // arrange
        testItem.setEan(BLANK_ID);

        // assert
        thrown.expect(ValidationException.class);
        thrown.expectMessage("\"item.ean\" must not be empty or null!");

        // act
        validator.validate(testMessage);
    }

    @Test
    public void validate_nullItemAvailability_exceptionThrown() throws Exception {
        // arrange
        testItem.setItemAvailability(null);

        // assert
        thrown.expect(ValidationException.class);
        thrown.expectMessage("\"item.itemAvailability\" must not be empty or null!");

        // act
        validator.validate(testMessage);
    }

    @Test
    public void validate_emptyItemAvailability_exceptionThrown() throws Exception {
        // arrange
        testItem.setItemAvailability(new ArrayList<>());

        // assert
        thrown.expect(ValidationException.class);
        thrown.expectMessage("\"item.itemAvailability\" must not be empty or null!");

        // act
        validator.validate(testMessage);
    }

    @Test
    public void validate_nullWarehouse_exceptionThrown() throws Exception {
        // arrange
        testItemAvailability.setWarehouse(null);

        // assert
        thrown.expect(ValidationException.class);
        thrown.expectMessage("\"itemAvailability.warehouse\" must not be empty or null!");

        // act
        validator.validate(testMessage);
    }

    @Test
    public void validate_emptyWarehouse_exceptionThrown() throws Exception {
        // arrange
        testItemAvailability.setWarehouse(EMPTY_ID);

        // assert
        thrown.expect(ValidationException.class);
        thrown.expectMessage("\"itemAvailability.warehouse\" must not be empty or null!");

        // act
        validator.validate(testMessage);
    }

    @Test
    public void validate_blankWarehouse_exceptionThrown() throws Exception {
        // arrange
        testItemAvailability.setWarehouse(BLANK_ID);

        // assert
        thrown.expect(ValidationException.class);
        thrown.expectMessage("\"itemAvailability.warehouse\" must not be empty or null!");

        // act
        validator.validate(testMessage);
    }

    @Test
    public void validate_nullAvailableQuantity_exceptionThrown() throws Exception {
        // arrange
        testItemAvailability.setAvailableQuantity(null);

        // assert
        thrown.expect(ValidationException.class);
        thrown.expectMessage("\"itemAvailability.availableQuantity\" must not be empty or null!");

        // act
        validator.validate(testMessage);
    }

    @Test
    public void validate_negativeAvailableQuantity_exceptionThrown() throws Exception {
        // arrange
        testItemAvailability.setAvailableQuantity(-1);

        // assert
        thrown.expect(ValidationException.class);
        thrown.expectMessage("\"itemAvailability.availableQuantity\" must be more than zero!");

        // act
        validator.validate(testMessage);
    }

    @Test
    public void validate_notExistingOrder_exceptionThrown() throws Exception {
        // arrange
        when(orderModelRepository.findById(testMessage.getCorrelationId())).thenReturn(Optional.empty());

        // assert
        thrown.expect(ValidationException.class);
        thrown.expectMessage(MessageFormat.format("No order details found in cache for \"{0}\" reported by a {1} message",
                testMessage.getCorrelationId(), ItemAvailabilityResponse.class.getSimpleName()));

        // act
        validator.validate(testMessage);
    }

    @Test
    public void validate_responseContainsAllEan_noExceptionThrown() throws Exception {
        // arrange
        OrderModel orderModel = new OrderModel();
        OrderLineQuantityModel orderLineQuantityModel1 = new OrderLineQuantityModel();
        OrderLineQuantityModel orderLineQuantityModel2 = new OrderLineQuantityModel();
        orderModel.setOrderId(testMessage.getCorrelationId());
        orderModel.setOrderLineQuantities(new ArrayList<>());
        orderModel.getOrderLineQuantities().add(orderLineQuantityModel1);
        orderModel.getOrderLineQuantities().add(orderLineQuantityModel2);

        orderLineQuantityModel1.setEan(ORDER_LINE_EAN_1);
        orderLineQuantityModel2.setEan(ORDER_LINE_EAN_2);

        List<Item> items = Arrays.asList(
                createItem(ORDER_LINE_EAN_1,
                        createItemAvailability(WAREHOUSE_1, FULL_AVAILABILITY, WAREHOUSE, COUNTRY),
                        createItemAvailability(WAREHOUSE_2, ZERO_AVAILABILITY, WAREHOUSE, COUNTRY)),
                createItem(ORDER_LINE_EAN_2,
                        createItemAvailability(WAREHOUSE_1, FULL_AVAILABILITY, WAREHOUSE, COUNTRY),
                        createItemAvailability(WAREHOUSE_2, ZERO_AVAILABILITY, WAREHOUSE, COUNTRY))
        );

        testMessage.setItems(items);

        when(orderModelRepository.findById(testMessage.getCorrelationId())).thenReturn(Optional.of(orderModel));

        // assert - should not fail

        // act
        validator.validate(testMessage);
    }

    @Test
    public void validate_countryWithNameLengthOfOneGiven_exceptionThrown() throws Exception {
        validateInvalidCountryExceptionThrown("A");
    }

    @Test
    public void validate_noCountry_exceptionThrown() throws Exception {
        validateInvalidCountryExceptionThrown(null);
    }

    private void validateInvalidCountryExceptionThrown(String invalidCountry) throws Exception {
        // arrange
        testMessage.getItems().get(0).getItemAvailability().get(0).setCountry(invalidCountry);

        // assert
        thrown.expect(ValidationException.class);
        thrown.expectMessage("invalid availability, country name should be at least 2 characters");

        // act
        validator.validate(testMessage);
    }

    @Test
    public void validate_countryWithInvalidTypeGiven_exceptionThrown() throws Exception {
        // arrange
        testMessage.getItems().get(0).getItemAvailability().get(0).setType(null);

        // assert
        thrown.expect(ValidationException.class);
        thrown.expectMessage("invalid availability, no such a fulfillment node type");

        // act
        validator.validate(testMessage);
    }

    @Test
    public void validate_countryWithMoreThanOneStorePerCountryGiven_exceptionThrown() throws Exception {
        // arrange
        ItemAvailability testItemAvailability1 = createItemAvailability(STORE_1, FULL_AVAILABILITY, STORE, COUNTRY);
        ItemAvailability testItemAvailability2 = createItemAvailability(STORE_2, FULL_AVAILABILITY, STORE, COUNTRY);
        Item testItem1 = createItem(TEST_EAN, Arrays.asList(testItemAvailability1, testItemAvailability2));
        ItemAvailabilityResponse messageResponse = new ItemAvailabilityResponse()
                .withCorrelationId(UUID.randomUUID().toString())
                .withItems(Arrays.asList(testItem1));

        // assert
        thrown.expect(ValidationException.class);
        thrown.expectMessage("invalid availability, two stores per country");

        // act
        validator.validate(messageResponse);
    }

}
