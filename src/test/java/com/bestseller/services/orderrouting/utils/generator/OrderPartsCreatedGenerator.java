package com.bestseller.services.orderrouting.utils.generator;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsCreated.OrderPart;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsCreated.OrderPartsCreated;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.OrderLine;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

public class OrderPartsCreatedGenerator {
    /**
     * Order line properties.
     */
    public static class OrderLineProperties {
        public static final String EAN = "**********";
        public static final int LINE_NUMBER = 0;
        public static final int QUANTITY = 10;
        public static final String CANCEL_REASON = "ITEM_NOT_AVAILABLE";
    }

    /**
     * Creates a test order.
     *
     * @return
     */
    @SuppressWarnings("PMD.NcssCount")
    public static OrderPartsCreated createMessage(String orderId, Collection<String> eans) {
        final List<OrderLine> orderLines = eans.stream().map(ean -> createFullOrderLine().withEan(ean))
                .collect(Collectors.toList());

        IntStream.range(0, orderLines.size())
                .forEach(i -> orderLines.get(i).setLineNumber(i + 1));

        OrderPart orderPart = new OrderPart()
                .withOrderPartNumber(1)
                .withOrderLines(orderLines);

        List orderParts = new ArrayList();
        orderParts.add(orderPart);

        return new OrderPartsCreated()
                .withOrderId(orderId)
                .withOrderParts(orderParts);
    }

    /**
     * Creates full order line.
     *
     * @return full order line
     */
    public static OrderLine createFullOrderLine() {
        return new OrderLine().withEan(OrderLineProperties.EAN)
                .withLineNumber(OrderLineProperties.LINE_NUMBER)
                .withQuantity(OrderLineProperties.QUANTITY);
    }
}
