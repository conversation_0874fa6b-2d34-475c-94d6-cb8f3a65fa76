package com.bestseller.services.orderrouting.utils;

import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.util.OrderValueCalculator;
import org.hamcrest.Matcher;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.CoreMatchers.notNullValue;
import static org.junit.Assert.assertThat;

@RunWith(MockitoJUnitRunner.class)
public class OrderValueCalculatorTest {

    private static final BigDecimal ALMOST_HUNDRED = new BigDecimal("99.95");
    private static final BigDecimal BIG_BLACK_FRIDAY_DISCOUNT = new BigDecimal("0.75");
    private static final BigDecimal NOT_A_DOUBLE = new BigDecimal("1.****************");

    @Rule
    public ExpectedException thrown = ExpectedException.none();

    @InjectMocks
    private OrderValueCalculator orderValueCalculator;

    @Test
    public void orderValue_zeroQuantity_zeroValue() {
        BigDecimal discountAmount = ALMOST_HUNDRED.multiply(BIG_BLACK_FRIDAY_DISCOUNT);
        orderValueSingleLinewithPriceDiscountQuantityExpectOrderValue(ALMOST_HUNDRED, discountAmount, 0, equalTo(BigDecimal.ZERO));
    }

    @Test
    public void orderValue_fullGiveAwayDiscount_zeroValue() {
        orderValueSingleLinewithPriceDiscountQuantityExpectOrderValue(ALMOST_HUNDRED, ALMOST_HUNDRED, Integer.MAX_VALUE, equalTo(BigDecimal.ZERO));
    }

    @Test
    public void orderValue_noDiscount_sameAsPrice() {
        orderValueSingleLinewithPriceDiscountQuantityExpectOrderValue(ALMOST_HUNDRED, BigDecimal.ZERO, 1, equalTo(ALMOST_HUNDRED));
    }

    @Test
    public void orderValue_trickyPrice_precisionIsPreserved() {
        orderValueSingleLinewithPriceDiscountQuantityExpectOrderValue(NOT_A_DOUBLE, BigDecimal.ZERO, 1, equalTo(NOT_A_DOUBLE));
    }

    @Test
    public void orderValue_trickyDiscount_precisionIsPreserved() {
        orderValueSingleLinewithPriceDiscountQuantityExpectOrderValue(BigDecimal.ZERO, NOT_A_DOUBLE.negate(), 1, equalTo(NOT_A_DOUBLE));
    }

    @Test
    public void orderValue_multipleLines_totalValue() {
        // arrange
        int orderLineCount = BigDecimal.TEN.intValue();
        double almostThousand = ALMOST_HUNDRED.doubleValue() * orderLineCount;

        OrderLineQuantityModel zeroQtyModel = createOrderLineQuantity(ALMOST_HUNDRED, BigDecimal.ZERO, 1);
        List<OrderLineQuantityModel> tenOrderLines = Collections.nCopies(orderLineCount, zeroQtyModel);

        // act
        BigDecimal result = orderValueCalculator.totalOrderCost(tenOrderLines);

        // assert
        assertThat("Result can't be null", result, notNullValue());
        assertThat("Order value should match", result.doubleValue(), equalTo(almostThousand));
    }

    private void orderValueSingleLinewithPriceDiscountQuantityExpectOrderValue(
            BigDecimal retailPrice,
            BigDecimal discountValue,
            int quantity,
            Matcher<BigDecimal> expectedOrderValue) {

        // arrange
        OrderLineQuantityModel zeroQtyModel = createOrderLineQuantity(retailPrice, discountValue, quantity);

        // act
        BigDecimal result = orderValueCalculator.totalOrderCost(Collections.singletonList(zeroQtyModel));

        // assert
        assertThat("Result can't be null", result, notNullValue());
        assertThat("Order value should match", result.stripTrailingZeros(), expectedOrderValue);
    }

    private OrderLineQuantityModel createOrderLineQuantity(BigDecimal retailPrice, BigDecimal discountValue, int quantity) {
        OrderLineQuantityModel zeroQtyModel = new OrderLineQuantityModel();
        zeroQtyModel.setRetailPrice(retailPrice);
        zeroQtyModel.setDiscountValue(discountValue);
        zeroQtyModel.setQuantity(quantity);
        return zeroQtyModel;
    }

}
