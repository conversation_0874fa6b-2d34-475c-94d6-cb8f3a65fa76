package com.bestseller.services.orderrouting.utils;

import com.bestseller.services.orderrouting.feature.toggles.ORSFeatures;
import org.togglz.core.Feature;
import org.togglz.core.manager.FeatureManager;
import org.togglz.core.metadata.FeatureMetaData;
import org.togglz.core.repository.FeatureState;
import org.togglz.core.spi.ActivationStrategy;
import org.togglz.core.user.FeatureUser;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * A testing feature manager class that is used to mock the feature.
 */
public class TestingFeatureManager implements FeatureManager {

    private Map<Feature, Boolean> activeMap = new HashMap<>();

    @Override
    public String getName() {
        return getClass().getSimpleName();
    }

    @Override
    public Set<Feature> getFeatures() {
        return Collections.singleton(ORSFeatures.ROUTE_ORDERS);
    }

    @Override
    public FeatureMetaData getMetaData(Feature feature) {
        return null;
    }

    @Override
    public boolean isActive(Feature feature) {
        Boolean isActive = activeMap.get(feature);
        return isActive != null && isActive;
    }

    /**
     * Updates the feature active state.
     * @param feature the feature to update
     * @param isActive whether the feature is active or not
     */
    public void setActive(Feature feature, boolean isActive) {
        activeMap.put(feature, isActive);
    }

    @Override
    public FeatureUser getCurrentFeatureUser() {
        return null;
    }

    @Override
    public FeatureState getFeatureState(Feature feature) {
        return new FeatureState(feature, isActive(feature));
    }

    @Override
    public void setFeatureState(FeatureState state) {
        setActive(state.getFeature(), state.isEnabled());
    }

    @Override
    public List<ActivationStrategy> getActivationStrategies() {
        return Collections.emptyList();
    }

}
