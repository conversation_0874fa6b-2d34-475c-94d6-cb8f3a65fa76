package com.bestseller.services.orderrouting.utils.generator;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartRejected.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartRejected.OrderPartRejected;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * Generates {@link OrderPartRejected} message for test purposes.
 */
@SuppressWarnings("PMD.ClassNamingConventions")
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class OrderPartRejectedGenerator {

    /**
     * First OrderLineDispatched properties.
     */
    public static class OrderPartRejectedProperties {
        public static final String ORDER_ID = "orderId";
    }

    /**
     * Order line properties.
     */
    public static class OrderLineProperties {
        public static final String EAN = "1234567890";
        public static final int LINE_NUMBER = 0;
        public static final int QUANTITY = 10;
        public static final String CANCEL_REASON = "ITEM_NOT_AVAILABLE";
    }

    /**
     * Creates full order parts cancelled.
     * @return OPC
     */
    public static OrderPartRejected createFullOrderPartRejectedMessage() {
        return createFullOrderPartRejectedMessage(OrderPartRejectedProperties.ORDER_ID, "SHIP_FROM_STORE_NL",
                                                  Collections.singletonList(OrderLineProperties.EAN));
    }

    /**
     * Creates full order parts cancelled.
     * @param orderId order id
     * @param eans list of eans
     * @return OPC
     */
    public static OrderPartRejected createFullOrderPartRejectedMessage(String orderId, String warehouse, Collection<String> eans) {
        final List<OrderLine> orderLines = eans.stream().map(ean -> createFullOrderLine().withEan(ean))
                                               .collect(Collectors.toList());

        IntStream.range(0, orderLines.size())
                .forEach(i -> orderLines.get(i).setLineNumber(i + 1));

        return new OrderPartRejected()
                .withOrderId(orderId)
                .withOrderLines(orderLines)
                .withCancellationDate(ZonedDateTime.now())
                .withWarehouse(warehouse);
    }

    /**
     * Creates full order parts cancelled.
     * @param orderId order id
     * @param eans list of eans
     * @return OPC with that info
     */
    public static OrderPartRejected createFullOrderPartRejectedMessage(String orderId, String warehouse, String... eans) {
        return createFullOrderPartRejectedMessage(orderId, warehouse, Arrays.asList(eans));
    }

    /**
     * Creates full order line.
     * @return full order line
     */
    public static OrderLine createFullOrderLine() {
        final OrderLine orderLine = new OrderLine();
        orderLine.withEan(OrderLineProperties.EAN)
                 .withLineNumber(OrderLineProperties.LINE_NUMBER)
                 .withQuantity(OrderLineProperties.QUANTITY)
                 .withCancelReason(OrderLineProperties.CANCEL_REASON);
        return orderLine;
    }
}
