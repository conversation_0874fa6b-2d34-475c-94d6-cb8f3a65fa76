package com.bestseller.services.orderrouting.utils.generator;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.Item;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailability;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailability.Type;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailabilityResponse;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Stream;

import static com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailability.Type.STORE;
import static com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailability.Type.WAREHOUSE;
import static java.lang.String.format;

/**
 * Generates {@link Item} and {@link ItemAvailability} objects for test purposes.
 * <p> * Example: <p>
 * ItemAvailabilityResponse message = new ItemAvailabilityResponse()
 * .withCorrelationId(someOrderId)
 * .withItems(
 * Arrays.asList(
 * createItem(ORDER_LINE_EAN_1,
 * createItemAvailability(WAREHOUSE_1, FULL_AVAILABILITY),
 * createItemAvailability(WAREHOUSE_2, ZERO_AVAILABILITY)),
 * createItem(ORDER_LINE_EAN_2,
 * createItemAvailability(WAREHOUSE_1, FULL_AVAILABILITY),
 * createItemAvailability(WAREHOUSE_2, ZERO_AVAILABILITY)),
 * createItem(ORDER_LINE_EAN_3,
 * createItemAvailability(WAREHOUSE_1, FULL_AVAILABILITY),
 * createItemAvailability(WAREHOUSE_2, ZERO_AVAILABILITY))
 * ));
 * </p></p>
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ItemAvailabilityGenerator {
    public static final String WAREHOUSE_1 = "INGRAM_MICRO";
    public static final String WAREHOUSE_2 = "INGRAM_MICRO_NL";
    public static final String STORE_1 = "STORE-1";
    public static final String COUNTRY = "NL";
    public static final int FULL_AVAILABILITY = 1_000;
    public static final int ZERO_AVAILABILITY = 0;

    /**
     * Creates a test {@link Item} object.
     *
     * @param ean
     * @param availability
     * @return
     */
    public static Item createItem(String ean, ItemAvailability... availability) {
        return new Item().withEan(ean).withItemAvailability(Arrays.asList(availability));
    }

    /**
     * Creates a test {@link Item} object.
     *
     * @param ean
     * @param itemAvailabilities
     * @return
     */
    public static Item createItem(String ean, List<ItemAvailability> itemAvailabilities) {
        return new Item().withEan(ean).withItemAvailability(itemAvailabilities);
    }

    /**
     * Create items by given map with ean and warehouse available pairs.
     *
     * @param eanToAvailableWarehouseMap
     * @return
     */
    public static ItemAvailabilityResponse createItemAvailabilityResponse(Map<String, String> eanToAvailableWarehouseMap) {
        List<Item> items = new ArrayList<>();
        eanToAvailableWarehouseMap
                .forEach((key, value) -> items.add(createItem(key, createItemAvailability(value, Integer.MAX_VALUE))));

        return new ItemAvailabilityResponse().withItems(items);
    }

    /**
     * Create a test {@link ItemAvailability} object.
     *
     * @param warehouse
     * @param availableQty
     * @return
     */
    public static ItemAvailability createItemAvailability(String warehouse, int availableQty) {
        final ItemAvailability itemAvailability = new ItemAvailability()
                .withWarehouse(warehouse)
                .withAvailableQuantity(availableQty)
                .withCountry(COUNTRY);
        if (warehouse.matches("SHIP_FROM_STORE_.*")) {
            return itemAvailability.withType(STORE);
        } else if (warehouse.matches("INGRAM_MICRO.*|DEFAULT_.*|SECOND_.*|HERMES_.*|HOUSE_.*|FULFILLMENT_CENTER_.*|.*_FULFILLMENT_NODE")) {
            return itemAvailability.withType(WAREHOUSE);
        } else {
            throw new AssertionError(format("Is %s a %s or a %s?",
                    Stream.of(
                            Stream.of(warehouse),
                            Arrays.stream(Type.values()))
                    .flatMap(Function.identity())
                    .toArray()));
        }
    }

    /**
     * Create a test {@link ItemAvailability} object.
     *
     * @param warehouse
     * @param availableQuantity
     * @param backOrderQuantity
     * @param earliestAvailabilityDate
     * @return
     */
    public static ItemAvailability createItemAvailability(String warehouse, Integer availableQuantity, Integer backOrderQuantity,
                                                          ZonedDateTime earliestAvailabilityDate) {
        return createItemAvailability(warehouse, availableQuantity, WAREHOUSE, COUNTRY);
    }

    /**
     * Create a test {@link ItemAvailability} object.
     *
     * @param warehouse
     * @param availableQty
     * @param type
     * @param country
     * @return
     */
    public static ItemAvailability createItemAvailability(String warehouse, Integer availableQty, Type type, String country) {
        return new ItemAvailability()
                .withWarehouse(warehouse)
                .withAvailableQuantity(availableQty)
                .withType(type)
                .withCountry(country);
    }
}
