package com.bestseller.services.orderrouting.utils.verifier;

import org.jeasy.rules.api.Facts;

/**
 * Class to verify equallity of the {@link Facts}.
 */
@SuppressWarnings("PMD.UselessOverridingMethod")
public class FactsVerifier extends Facts {
    /**
     * Default constructor.
     */
    public FactsVerifier() {
        super();
    }

    /**
     * Constructor taking other map as initializing argument.
     *
     * @param facts the other collection.
     */
    public FactsVerifier(final Facts facts) {
        super();
        facts.asMap().forEach(this::put);
    }

    /**
     * Compares other map to this ignoring the order of the element and only looking if the map are
     * equal in size and contain all elements.
     *
     * @param o collection to be compared.
     * @return true if equals, false otherwise.
     */
    @Override
    public boolean equals(final Object o) {
        if (o instanceof Facts) {
            MapVerifier<String, Object> thisVerifier = new MapVerifier<>(this.asMap());
            return thisVerifier.equals(((Facts) o).asMap());
        }
        return false;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int hashCode() {
        return super.hashCode();
    }
}
