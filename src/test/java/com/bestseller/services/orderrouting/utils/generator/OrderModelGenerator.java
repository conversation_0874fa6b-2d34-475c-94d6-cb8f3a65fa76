package com.bestseller.services.orderrouting.utils.generator;

import com.bestseller.services.orderrouting.model.AdditionalOrderInformationModel;
import com.bestseller.services.orderrouting.model.AddressModel;
import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.model.OrderModel;
import com.bestseller.services.orderrouting.model.PaymentModel;
import com.bestseller.services.orderrouting.service.EntityType;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.RandomStringUtils;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

/**
 * Generates instances of {@link OrderModel} for test purposes and test data.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
@SuppressWarnings("PMD.ClassNamingConventions")
public final class OrderModelGenerator {

    private static final int EAN_LENGTH = 13;

    /**
     * Order properties.
     */
    public static class OrderProperties {
        public static final String CARRIER = "CARRIER";
        public static final String CARRIER_VARIANT = "CARRIER-VARIANT";
        public static final String CUSTOMER_EMAIL = "CUSTOMER-EMAIL";
        public static final String EXTERNAL_CUSTOMER_NUMBER = "EXTERNAL-CUSTOMER-NUMBER";
        public static final String EXTERNAL_ORDER_NUMBER = "EXTERNAL-ORDER-NUMBER";
        public static final int A_DAY_IN_SECONDS = 60 * 60 * 24;
        public static final double ORDER_VALUE = 199.99;
        public static final String CURRENCY = "EUR";
        public static final String CHECKOUT = "on";
        public static final String ORDER_TYPE = "ORDER-TYPE";
        public static final double SHIPPING_FEES = 19.99;
        public static final double SHIPPING_FEES_TAX_PERCENTAGE = 0.09;
        public static final String SHIPPING_METHOD = "STANDARD";
        public static final String CUSTOMER_ID = "CUSTOMER-ID";
        public static final String CUSTOMER_LOCALE = "LOCALE";
        public static final Boolean SHIPPING_FEES_CANCELLED = Boolean.TRUE;
        public static final String MARKETPLACE = "MARKETPLACE";
        public static final String STORE = "STORE";
        public static final String CHANNEL = "CHANNEL";
        public static final String BRAND = "BRAND";
        public static final String ACTION_CODE = "ACTION-CODE";
    }

    /**
     * Order line properties.
     */
    public static class OrderLineProperties {
        public static final String LINE_1_EAN = "1234567890";
        public static final String LINE_1_PRODUCT_NAME = "PRODUCT-1";
        public static final int LINE_1_QUANTITY = 5;
        public static final String LINE_2_EAN = "9876543210";
        public static final String LINE_2_PRODUCT_NAME = "PRODUCT-2";
        public static final int LINE_2_QUANTITY = 7;
        public static final int LINE_1_NUMBER = 1;
        public static final int LINE_2_NUMBER = 2;
        public static final int LINE_3_NUMBER = 3;
        public static final String LINE_3_EAN = "1234123400";
        public static final String LINE_3_PRODUCT_NAME = "PRODUCT-3";
        public static final int LINE_3_QUANTITY = 9;
        public static final double LINE_1_RETAIL_PRICE = 15.59;
        public static final double LINE_1_DISCOUNT_VALUE = 5.00;
        public static final double LINE_1_TAX_PERCENTAGE = 0.47;
        public static final double LINE_2_RETAIL_PRICE = 7.99;
        public static final double LINE_2_TAX_PERCENTAGE = 0.25;
        public static final double LINE_3_RETAIL_PRICE = 25.66;
        public static final String LINE_4_EAN = "4234567890";
        public static final String LINE_4_PRODUCT_NAME = "PRODUCT-4";
        public static final int LINE_4_QUANTITY = 5;
        public static final int LINE_4_NUMBER = 4;
        public static final double LINE_4_RETAIL_PRICE = 12.34;
        public static final double LINE_4_DISCOUNT_VALUE = 3.00;
        public static final double LINE_4_TAX_PERCENTAGE = 0.27;
        public static final String PARTNER_REFERENCE_1 = "PARTNER-REFERENCE-1";
        public static final String PARTNER_REFERENCE_2 = "PARTNER-REFERENCE-2";
        public static final String PARTNER_REFERENCE_3 = "PARTNER-REFERENCE-3";
    }

    /**
     * Billing address properties.
     */
    public static class BillingAddressProperties {
        public static final String BILLING_ADDRESS_LINE_1 = "BILLING-ADDRESS-LINE-1";
        public static final String BILLING_ADDRESS_LINE_2 = "BILLING-ADDRESS-LINE-2";
        public static final String BILLING_ADDRESS_LINE_3 = "BILLING-ADDRESS-LINE-3";
        public static final String BILLING_ADDRESS_CITY = "BILLING-ADDRESS-CITY";
        public static final String BILLING_ADDRESS_STATE = "AL";
        public static final String BILLING_ADDRESS_COUNTRY = "NLD";
        public static final String BILLING_ADDRESS_TITLE = "BILL-TITLE";
        public static final String BILLING_ADDRESS_FIRSTNAME = "BILLING-ADDRESS-FIRSTNAME";
        public static final String BILLING_ADDRESS_LASTNAME = "BILLING-ADDRESS-LASTNAME";
        public static final String BILLING_ADDRESS_HOUSE_NUMBER = "BILLING-ADDRESS-HOUSE-NUMBER";
        public static final String BILLING_HOUSE_NUMBER_EXT = "BILLING-HOUSE-NUMBER-EXT";
        public static final String BILLING_ADDRESS_PHONE = "BILLING-ADD-PHONE";
        public static final String BILLING_ADDRESS_LINE_ZIP = "BILL-ZIP";
    }

    /**
     * Shipping address properties.
     */
    public static class ShippingAddressProperties {
        public static final String SHIPPING_ADDRESS_LINE_1 = "SHIPPING-ADDRESS-LINE-1";
        public static final String SHIPPING_ADDRESS_LINE_2 = "SHIPPING-ADDRESS-LINE-2";
        public static final String SHIPPING_ADDRESS_LINE_3 = "SHIPPING-ADDRESS-LINE-3";
        public static final String SHIPPING_ADDRESS_CITY = "SHIPPING-ADDRESS-CITY";
        public static final String SHIPPING_ADDRESS_STATE = "FL";
        public static final String SHIPPING_ADDRESS_COUNTRY = "NL";
        public static final String SHIPPING_ADDRESS_TITLE = "SHIP-TITLE";
        public static final String SHIPPING_ADDRESS_FIRSTNAME = "SHIPPING-ADDRESS-FIRSTNAME";
        public static final String SHIPPING_ADDRESS_LASTNAME = "SHIPPING-ADDRESS-LASTNAME";
        public static final String SHIPPING_ADDRESS_HOUSE_NUMBER = "SHIPPING-ADDRESS-HOUSE-NUMBER";
        public static final String SHIPPING_HOUSE_NUMBER_EXT = "SHIPPING-HOUSE-NUMBER-EXT";
        public static final String SHIPPING_ADDRESS_PHONE = "SHIPPING-ADD-PHONE";
        public static final String SHIPPING_ADDRESS_LINE_ZIP = "SHIP-ZIP";
    }

    /**
     * Payment properties.
     */
    public static class PaymentProperties {
        public static final String PAYMENT_TYPE_1 = "PAYMENT-TYPE-1";
        public static final String PAYMENT_METHOD_1 = "PAYMENT-METHOD-1";
        public static final String PAYMENT_TYPE_2 = "PAYMENT-TYPE-2";
        public static final String PAYMENT_METHOD_2 = "PAYMENT-METHOD-2";
    }

    /**
     * Shipping information properties.
     */
    public static class ShippingInformationProperties {
        public static final String PARCEL_LOCKER = "A Locker somewhere";
        public static final List<AdditionalOrderInformationModel> ADDITIONAL_INFORMATION_LIST = List.of(
            new AdditionalOrderInformationModel(null, EntityType.SHIPPING_INFORMATION, "key", "value", "")
        );
    }

    /**
     * Creates a test order.
     *
     * @return
     */
    @SuppressWarnings("PMD.NcssCount")
    public static OrderModel createTestOrderModel() {
        OrderModel orderModel = new OrderModel();
        orderModel.setOrderId(UUID.randomUUID().toString());
        orderModel.setBillingAddress(new AddressModel());
        orderModel.getBillingAddress().setAddressLine1(BillingAddressProperties.BILLING_ADDRESS_LINE_1);
        orderModel.getBillingAddress().setAddressLine2(BillingAddressProperties.BILLING_ADDRESS_LINE_2);
        orderModel.getBillingAddress().setAddressLine3(BillingAddressProperties.BILLING_ADDRESS_LINE_3);
        orderModel.getBillingAddress().setCity(BillingAddressProperties.BILLING_ADDRESS_CITY);
        orderModel.getBillingAddress().setState(BillingAddressProperties.BILLING_ADDRESS_STATE);
        orderModel.getBillingAddress().setCountry(BillingAddressProperties.BILLING_ADDRESS_COUNTRY);
        orderModel.getBillingAddress().setTitle(BillingAddressProperties.BILLING_ADDRESS_TITLE);
        orderModel.getBillingAddress().setFirstName(BillingAddressProperties.BILLING_ADDRESS_FIRSTNAME);
        orderModel.getBillingAddress().setLastName(BillingAddressProperties.BILLING_ADDRESS_LASTNAME);
        orderModel.getBillingAddress().setHouseNumber(BillingAddressProperties.BILLING_ADDRESS_HOUSE_NUMBER);
        orderModel.getBillingAddress().setHouseNumberExtended(BillingAddressProperties.BILLING_HOUSE_NUMBER_EXT);
        orderModel.getBillingAddress().setPhoneNumber(BillingAddressProperties.BILLING_ADDRESS_PHONE);
        orderModel.getBillingAddress().setZipcode(BillingAddressProperties.BILLING_ADDRESS_LINE_ZIP);
        orderModel.setCarrier(OrderProperties.CARRIER);
        orderModel.setCarrierVariant(OrderProperties.CARRIER_VARIANT);
        orderModel.setCustomerEmail(OrderProperties.CUSTOMER_EMAIL);
        orderModel.setExternalCustomerNumber(OrderProperties.EXTERNAL_CUSTOMER_NUMBER);
        orderModel.setExternalOrderNumber(OrderProperties.EXTERNAL_ORDER_NUMBER);
        orderModel.setIsTest(false);
        orderModel.setOrderCreationDate(Instant.now().minusSeconds(OrderProperties.A_DAY_IN_SECONDS));
        orderModel.setOrderType(OrderProperties.ORDER_TYPE);
        orderModel.setOrderValue(BigDecimal.valueOf(OrderProperties.ORDER_VALUE));
        orderModel.setCurrency(OrderProperties.CURRENCY);
        orderModel.setCheckout(OrderProperties.CHECKOUT);
        orderModel.setPlacedDate(Instant.now());
        orderModel.setCustomerId(OrderProperties.CUSTOMER_ID);
        orderModel.setCustomerLocale(OrderProperties.CUSTOMER_LOCALE);
        orderModel.setShippingFeesCancelled(OrderProperties.SHIPPING_FEES_CANCELLED);
        orderModel.setMarketPlace(OrderProperties.MARKETPLACE);
        orderModel.setStore(OrderProperties.STORE);
        orderModel.setChannel(OrderProperties.CHANNEL);
        orderModel.setBrand(OrderProperties.BRAND);
        orderModel.setActionCode(OrderProperties.ACTION_CODE);
        orderModel.setShippingAddress(new AddressModel());
        orderModel.getShippingAddress().setAddressLine1(ShippingAddressProperties.SHIPPING_ADDRESS_LINE_1);
        orderModel.getShippingAddress().setAddressLine2(ShippingAddressProperties.SHIPPING_ADDRESS_LINE_2);
        orderModel.getShippingAddress().setAddressLine3(ShippingAddressProperties.SHIPPING_ADDRESS_LINE_3);
        orderModel.getShippingAddress().setCity(ShippingAddressProperties.SHIPPING_ADDRESS_CITY);
        orderModel.getShippingAddress().setState(ShippingAddressProperties.SHIPPING_ADDRESS_STATE);
        orderModel.getShippingAddress().setCountry(ShippingAddressProperties.SHIPPING_ADDRESS_COUNTRY);
        orderModel.getShippingAddress().setTitle(ShippingAddressProperties.SHIPPING_ADDRESS_TITLE);
        orderModel.getShippingAddress().setFirstName(ShippingAddressProperties.SHIPPING_ADDRESS_FIRSTNAME);
        orderModel.getShippingAddress().setLastName(ShippingAddressProperties.SHIPPING_ADDRESS_LASTNAME);
        orderModel.getShippingAddress().setHouseNumber(ShippingAddressProperties.SHIPPING_ADDRESS_HOUSE_NUMBER);
        orderModel.getShippingAddress().setHouseNumberExtended(ShippingAddressProperties.SHIPPING_HOUSE_NUMBER_EXT);
        orderModel.getShippingAddress().setPhoneNumber(ShippingAddressProperties.SHIPPING_ADDRESS_PHONE);
        orderModel.getShippingAddress().setZipcode(ShippingAddressProperties.SHIPPING_ADDRESS_LINE_ZIP);
        orderModel.setShippingFees(BigDecimal.valueOf(OrderProperties.SHIPPING_FEES));
        orderModel.setShippingFeesTaxPercentage(BigDecimal.valueOf(OrderProperties.SHIPPING_FEES_TAX_PERCENTAGE));
        orderModel.setShippingMethod(OrderProperties.SHIPPING_METHOD);
        orderModel.setParcelLocker(ShippingInformationProperties.PARCEL_LOCKER);

        List<AdditionalOrderInformationModel> additionalInformationList =
            new ArrayList<>(ShippingInformationProperties.ADDITIONAL_INFORMATION_LIST);

        additionalInformationList.forEach(item -> item.setOrderId(orderModel.getOrderId()));
        orderModel.setAdditionalOrderInformation(ShippingInformationProperties.ADDITIONAL_INFORMATION_LIST);
        return orderModel;
    }

    /**
     * Creates a test order.
     *
     * @param country
     *
     * @return
     */
    public static OrderModel createTestOrderModel(String country) {
        OrderModel orderModel = createTestOrderModel();
        orderModel.getShippingAddress().setCountry(country);
        return orderModel;
    }

    /**
     * Creates three order lines.
     *
     * @return
     */
    @SuppressWarnings("PMD.NcssCount")
    public static List<OrderLineQuantityModel> createThreeTestOrderLines(OrderModel orderModel) {
        OrderLineQuantityModel orderLine1 = new OrderLineQuantityModel();
        orderLine1.setOrderId(orderModel.getOrderId());
        orderLine1.setLineNumber(OrderLineProperties.LINE_1_NUMBER);
        orderLine1.setTotalOrderParts(1);
        orderLine1.setEan(OrderLineProperties.LINE_1_EAN);
        orderLine1.setProductName(OrderLineProperties.LINE_1_PRODUCT_NAME);
        orderLine1.setQuantity(OrderLineProperties.LINE_1_QUANTITY);
        orderLine1.setRetailPrice(BigDecimal.valueOf(OrderLineProperties.LINE_1_RETAIL_PRICE));
        orderLine1.setDiscountValue(BigDecimal.valueOf(OrderLineProperties.LINE_1_DISCOUNT_VALUE));
        orderLine1.setTaxPercentage(BigDecimal.valueOf(OrderLineProperties.LINE_1_TAX_PERCENTAGE));
        orderLine1.setIsGiftItem(false);
        orderLine1.setPartnerReference(OrderLineProperties.PARTNER_REFERENCE_1);

        OrderLineQuantityModel orderLine2 = new OrderLineQuantityModel();
        orderLine2.setOrderId(orderModel.getOrderId());
        orderLine2.setLineNumber(OrderLineProperties.LINE_2_NUMBER);
        orderLine2.setTotalOrderParts(1);
        orderLine2.setEan(OrderLineProperties.LINE_2_EAN);
        orderLine2.setProductName(OrderLineProperties.LINE_2_PRODUCT_NAME);
        orderLine2.setQuantity(OrderLineProperties.LINE_2_QUANTITY);
        orderLine2.setRetailPrice(BigDecimal.valueOf(OrderLineProperties.LINE_2_RETAIL_PRICE));
        orderLine2.setDiscountValue(BigDecimal.ZERO);
        orderLine2.setTaxPercentage(BigDecimal.valueOf(OrderLineProperties.LINE_2_TAX_PERCENTAGE));
        orderLine2.setIsGiftItem(false);
        orderLine2.setPartnerReference(OrderLineProperties.PARTNER_REFERENCE_2);

        OrderLineQuantityModel orderLine3 = new OrderLineQuantityModel();
        orderLine3.setOrderId(orderModel.getOrderId());
        orderLine3.setLineNumber(OrderLineProperties.LINE_3_NUMBER);
        orderLine3.setTotalOrderParts(1);
        orderLine3.setEan(OrderLineProperties.LINE_3_EAN);
        orderLine3.setProductName(OrderLineProperties.LINE_3_PRODUCT_NAME);
        orderLine3.setQuantity(OrderLineProperties.LINE_3_QUANTITY);
        orderLine3.setRetailPrice(BigDecimal.valueOf(OrderLineProperties.LINE_3_RETAIL_PRICE));
        orderLine3.setDiscountValue(BigDecimal.ZERO);
        orderLine3.setTaxPercentage(BigDecimal.ZERO);
        orderLine3.setIsGiftItem(true);
        orderLine3.setPartnerReference(OrderLineProperties.PARTNER_REFERENCE_3);

        return new ArrayList<>(Arrays.asList(orderLine1, orderLine2, orderLine3));
    }

    /**
     * Creates one test order line.
     *
     * @param brand
     *
     * @return
     */
    public static List<OrderLineQuantityModel> createThreeTestOrderLines(String brand, OrderModel orderModel) {
        List<OrderLineQuantityModel> orderLineQuantityModelList = createThreeTestOrderLines(orderModel);
        orderLineQuantityModelList.get(0).setBrand(brand);
        orderLineQuantityModelList.get(1).setBrand(brand);
        orderLineQuantityModelList.get(2).setBrand(brand);
        return orderLineQuantityModelList;
    }

    /**
     * Creates one test order line.
     *
     * @return
     */
    public static OrderLineQuantityModel createOneTestOrderLine(OrderModel orderModel) {
        OrderLineQuantityModel orderLine1 = new OrderLineQuantityModel();
        orderLine1.setOrderId(orderModel.getOrderId());
        orderLine1.setLineNumber(OrderLineProperties.LINE_4_NUMBER);
        orderLine1.setEan(OrderLineProperties.LINE_4_EAN);
        orderLine1.setProductName(OrderLineProperties.LINE_4_PRODUCT_NAME);
        orderLine1.setQuantity(OrderLineProperties.LINE_4_QUANTITY);
        orderLine1.setRetailPrice(BigDecimal.valueOf(OrderLineProperties.LINE_4_RETAIL_PRICE));
        orderLine1.setDiscountValue(BigDecimal.valueOf(OrderLineProperties.LINE_4_DISCOUNT_VALUE));
        orderLine1.setTaxPercentage(BigDecimal.valueOf(OrderLineProperties.LINE_4_TAX_PERCENTAGE));
        orderLine1.setIsGiftItem(false);

        return orderLine1;
    }

    /**
     * Create two test payments.
     *
     * @return
     */
    public static List<PaymentModel> createTwoPayments(OrderModel orderModel) {
        PaymentModel payment1 = new PaymentModel();
        payment1.setOrderId(orderModel.getOrderId());
        payment1.setName(PaymentProperties.PAYMENT_TYPE_1);
        payment1.setSubMethod(PaymentProperties.PAYMENT_METHOD_1);

        PaymentModel payment2 = new PaymentModel();
        payment2.setOrderId(orderModel.getOrderId());
        payment2.setName(PaymentProperties.PAYMENT_TYPE_2);
        payment2.setSubMethod(PaymentProperties.PAYMENT_METHOD_2);

        return Arrays.asList(payment1, payment2);
    }

    /**
     * Create <i>numberOfLines</i> with random EANs and specified orderId for testing.
     * @param orderId order id to assign to order lines.
     * @param numberOfLines number of lines to be created.
     * @return list of order lines with random eans.
     */
    public static List<OrderLineQuantityModel> createNOrderLines(String orderId, int numberOfLines) {
        List<OrderLineQuantityModel> orderLineQuantityModelList = new ArrayList<>();
        OrderLineQuantityModel orderLineQuantityModel;
        for (int i = 0; i < numberOfLines; i++) {
            orderLineQuantityModel = new OrderLineQuantityModel();
            orderLineQuantityModel.setOrderId(orderId);
            orderLineQuantityModel.setLineNumber(i);
            orderLineQuantityModel.setEan(RandomStringUtils.randomAlphabetic(EAN_LENGTH));
            orderLineQuantityModel.setProductName(OrderLineProperties.LINE_1_PRODUCT_NAME);
            orderLineQuantityModel.setQuantity(OrderLineProperties.LINE_1_QUANTITY);
            orderLineQuantityModel.setRetailPrice(BigDecimal.valueOf(OrderLineProperties.LINE_1_RETAIL_PRICE));
            orderLineQuantityModel.setDiscountValue(BigDecimal.valueOf(OrderLineProperties.LINE_1_DISCOUNT_VALUE));
            orderLineQuantityModel.setTaxPercentage(BigDecimal.valueOf(OrderLineProperties.LINE_1_TAX_PERCENTAGE));
            orderLineQuantityModel.setIsGiftItem(false);
            orderLineQuantityModel.setPartnerReference(OrderLineProperties.PARTNER_REFERENCE_1);
            orderLineQuantityModelList.add(orderLineQuantityModel);
        }

        return orderLineQuantityModelList;
    }

}
