package com.bestseller.services.orderrouting.utils.verifier;

import java.util.HashMap;
import java.util.Map;

/**
 * Class used for verifying equality of maps.
 *
 * @param <E> map key type.
 * @param <V> map value type.
 */
@SuppressWarnings("PMD.UselessOverridingMethod")
public class MapVerifier<E, V> extends HashMap<E, V> {
    private static final long serialVersionUID = -907102950868601730L;

    /**
     * Default constructor.
     */
    public MapVerifier() {
        super();
    }

    /**
     * Constructor taking other map as initializing argument..
     *
     * @param m the other collection.
     */
    public MapVerifier(final Map<E, V> m) {
        super(m);
    }

    /**
     * Compares other map to this ignoring the order of the element and only looking if the map are
     * equal in size and contain all elements.
     *
     * @param o collection to be compared.
     * @return true if equals, false otherwise.
     */
    @Override
    public boolean equals(final Object o) {
        if (o instanceof Map<?, ?>) {
            Map<?, ?> other = (Map<?, ?>) o;
            return this.size() == other.size()
                    && this.keySet().containsAll(other.keySet())
                    && this.values().containsAll(other.values())
                    && compareKeyAndValuePairs(this, other);
        }
        return false;
    }

    private boolean compareKeyAndValuePairs(final MapVerifier<E, V> map, final Map<?, ?> other) {
        for (final Entry<E, V> entry : map.entrySet()) {
            if (!entry.getValue().equals(other.get(entry.getKey()))) {
                return false;
            }
        }
        return true;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int hashCode() {
        return super.hashCode();
    }
}
