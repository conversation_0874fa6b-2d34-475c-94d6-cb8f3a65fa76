package com.bestseller.services.orderrouting.utils;

import org.junit.rules.ExpectedException;

import java.text.MessageFormat;

/**
 * Utility class for working with expected exceptions in test classes.
 */
public final class ExpectedExceptionUtils {

    private ExpectedExceptionUtils() {
    }

    /**
     * Sets the expected exception and it's expected message so this can be used in test method.
     * @param expectedExceptionPlaceholder The ExpectedException, annotated with @Rule annotation in test class.
     * @param clazz The exception class.
     * @param messageTemplate The expected message for the exception.
     * @param messageArgs The arguments for the exception message.
     */
    public static void expect(final ExpectedException expectedExceptionPlaceholder,
                              final Class<? extends Throwable> clazz, final String messageTemplate,
                              final Object... messageArgs) {
        expectedExceptionPlaceholder.expect(clazz);
        expectedExceptionPlaceholder.expectMessage(MessageFormat.format(messageTemplate, messageArgs));
    }
}
