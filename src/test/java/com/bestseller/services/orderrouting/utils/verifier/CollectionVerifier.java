package com.bestseller.services.orderrouting.utils.verifier;

import lombok.EqualsAndHashCode;

import java.util.Collection;
import java.util.LinkedList;

/**
 * Class used for verifying equality of collections.
 *
 * @param <E> collection type.
 */
@EqualsAndHashCode(callSuper = false)
public class CollectionVerifier<E> extends LinkedList<E> {
    private static final long serialVersionUID = 2460508006017327816L;

    /**
     * Constructor taking other collection as initializing argument..
     *
     * @param c the other collection.
     */
    public CollectionVerifier(final Collection<E> c) {
        super(c);
    }

}
