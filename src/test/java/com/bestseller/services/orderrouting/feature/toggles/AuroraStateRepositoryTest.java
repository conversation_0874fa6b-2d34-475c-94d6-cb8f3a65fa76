package com.bestseller.services.orderrouting.feature.toggles;

import com.bestseller.services.orderrouting.model.FeatureModel;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.core.convert.converter.Converter;
import org.springframework.data.repository.CrudRepository;
import org.togglz.core.Feature;
import org.togglz.core.repository.FeatureState;

import java.util.Optional;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class AuroraStateRepositoryTest {
    private AuroraStateRepository auroraStateRepository;

    @Mock
    private CrudRepository<FeatureModel, String> featureModelRepository;

    @Mock
    private Converter<FeatureModel, FeatureState> featureModelConverter;

    @Mock
    private Converter<FeatureState, FeatureModel> featureStateConverter;

    private Feature feature;
    private FeatureModel featureModel;
    private FeatureState featureState;

    @Before
    public void setUp() {
        feature = ORSFeatures.ROUTE_ORDERS;
        featureModel = new FeatureModel();
        featureState = new FeatureState(feature, true);

        auroraStateRepository = new AuroraStateRepository(featureModelRepository, featureModelConverter, featureStateConverter);
    }

    @Test
    public void getFeatureState_givenValidFeature_returnsValueState() {
        // arrange
        when(featureModelRepository.findById(feature.name())).thenReturn(Optional.of(featureModel));
        when(featureModelConverter.convert(featureModel)).thenReturn(featureState);

        // act
        FeatureState storedFeatureState = auroraStateRepository.getFeatureState(feature);

        // assert
        assertEquals("Should return a valid feature state", storedFeatureState, featureState);
    }

    @Test
    public void getFeatureState_givenNoFeatureFound_returnsNull() {
        // arrange
        when(featureModelRepository.findById(feature.name())).thenReturn(Optional.empty());

        // act
        FeatureState storedFeatureState = auroraStateRepository.getFeatureState(feature);

        // assert
        assertNull("Should return null", storedFeatureState);
    }

    @Test
    public void setFeatureState_givenValidFeatureState_savesModel() {
        // arrange
        when(featureStateConverter.convert(featureState)).thenReturn(featureModel);

        // act
        auroraStateRepository.setFeatureState(featureState);

        // assert
        verify(featureModelRepository).save(featureModel);
    }
}
