package com.bestseller.services.orderrouting.feature.toggles;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.core.env.Environment;
import org.togglz.core.repository.StateRepository;
import org.togglz.core.user.FeatureUser;

import com.bestseller.services.orderrouting.configuration.security.SecurityConfig;

@RunWith(MockitoJUnitRunner.class)
public class ORSFeatureToggleConfigTest {

    private static final String ADMIN_USER_NAME = "ADMIN";

    @Mock
    private StateRepository stateRepository;

    @Mock
    private Environment env;

    private ORSFeatureToggleConfig featureToggleConfig;

    @Before
    public void setUp() {
        featureToggleConfig = new ORSFeatureToggleConfig(stateRepository, env);
    }

    @Test
    public void getFeatureClass_returnsORSFeaturesClass() {
        // arrange

        // act
        Class<?> featureClass = featureToggleConfig.getFeatureClass();

        // assert
        assertEquals("Should return the ORSFeatures class", featureClass, ORSFeatures.class);
    }

    @Test
    public void getStateRepository_returnsCorrectStateRepository() {
        // arrange

        // act
        StateRepository repository = featureToggleConfig.getStateRepository();

        // assert
        assertEquals("Should return the correct state repository", repository, stateRepository);
    }

    @Test
    public void getUserProvider_getCurrentUserCalled_returnsAdministratorFeatureUser() {
        // arrange
        when(env.getProperty(SecurityConfig.USER_ADMINISTRATOR_NAME)).thenReturn(ADMIN_USER_NAME);

        // act
        FeatureUser featureUser = featureToggleConfig.getUserProvider().getCurrentUser();

        // assert
        assertEquals("Should return the administrator user name", featureUser.getName(), ADMIN_USER_NAME);
    }
}
