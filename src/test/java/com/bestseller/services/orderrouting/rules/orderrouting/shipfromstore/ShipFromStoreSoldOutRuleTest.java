package com.bestseller.services.orderrouting.rules.orderrouting.shipfromstore;

import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.model.OrderModel;
import com.bestseller.services.orderrouting.rules.facts.FactNames;
import org.jeasy.rules.api.Facts;
import org.jeasy.rules.api.Rule;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import static com.bestseller.services.orderrouting.rules.facts.FactNames.ORDER_MODEL_FACT;
import static com.bestseller.services.orderrouting.service.CancellationReason.ITEM_NOT_AVAILABLE;
import static com.bestseller.services.orderrouting.service.CancellationReason.STORE_REJECTION;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.hasEntry;
import static org.hamcrest.Matchers.hasKey;
import static org.hamcrest.Matchers.not;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ShipFromStoreSoldOutRuleTest {
    @Mock
    private Rule sfsSoldOutCompositeRule;
    @Mock
    private OrderModel orderModel;
    @Captor
    private ArgumentCaptor<Facts> factsCaptor;
    @InjectMocks
    private ShipFromStoreSoldOutRule shipFromStoreSoldOutRule;
    private Facts orderModelFact;
    private List<OrderLineQuantityModel> orderLineQuantities;

    @Before
    public void init() {
        orderLineQuantities = new ArrayList<>();
        orderModelFact = new Facts();
        orderModelFact.put(ORDER_MODEL_FACT, orderModel);
        when(orderModel.getOrderLineQuantities()).thenReturn(orderLineQuantities);
    }

    @Test
    public void evaluate_orderModelWithStoreRejection() {
        //arrange
        OrderLineQuantityModel orderLine = new OrderLineQuantityModel();
        orderLine.setCancelReason(STORE_REJECTION);
        orderLineQuantities.add(orderLine);

        //act
        shipFromStoreSoldOutRule.evaluate(orderModelFact);

        // assert
        verify(sfsSoldOutCompositeRule).evaluate(factsCaptor.capture());
        Map<String, Object> factsMap = toMap(factsCaptor.getValue());
        assertThat("OPC Fact is present", factsMap, hasEntry(FactNames.ORDER_PART_REJECTED_FACT, true));
    }

    @Test
    public void evaluate_orderModelWithCancelReasonDiffThanStoreRejection() {
        //arrange
        OrderLineQuantityModel orderLine = new OrderLineQuantityModel();
        orderLine.setCancelReason(ITEM_NOT_AVAILABLE);
        orderLineQuantities.add(orderLine);

        //act
        shipFromStoreSoldOutRule.evaluate(orderModelFact);

        // assert
        verify(sfsSoldOutCompositeRule).evaluate(factsCaptor.capture());
        Map<String, Object> factsMap = toMap(factsCaptor.getValue());
        assertThat("OPC fact is not present", factsMap, not(hasKey(FactNames.ORDER_PART_REJECTED_FACT)));
    }

    private Map<String, Object> toMap(Facts facts) {
        return StreamSupport.stream(facts.spliterator(), false)
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }
}
