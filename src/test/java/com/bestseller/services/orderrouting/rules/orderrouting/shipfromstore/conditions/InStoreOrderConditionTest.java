package com.bestseller.services.orderrouting.rules.orderrouting.shipfromstore.conditions;

import com.bestseller.services.orderrouting.model.OrderModel;
import org.jeasy.rules.api.Facts;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import static com.bestseller.services.orderrouting.rules.facts.FactNames.ORDER_MODEL_FACT;
import static org.assertj.core.api.Assertions.assertThat;

/**
 * Unit tests for {@link InStoreOrderCondition}.
 */
@RunWith(MockitoJUnitRunner.class)
public class InStoreOrderConditionTest {

    private Facts facts;

    @InjectMocks
    private InStoreOrderCondition inStoreOrderCondition;

    @Test
    public void when_givenNotIsoOrder_returnFalse() {
        // arrange
        OrderModel orderModel = new OrderModel();
        orderModel.setOrderId("********");

        facts = new Facts();
        facts.put(ORDER_MODEL_FACT, orderModel);

        // act
        boolean result = inStoreOrderCondition.evaluate(facts);

        // assert
        assertThat(result)
            .as("Condition evaluates false")
            .isFalse();
    }

    @Test
    public void when_givenIsoOrder_returnTrue() {
        // arrange
        OrderModel orderModel = new OrderModel();
        orderModel.setOrderId("OL********");
        orderModel.setIsoStoreId("ISO Store Id");

        facts = new Facts();
        facts.put(ORDER_MODEL_FACT, orderModel);

        // act
        boolean result = inStoreOrderCondition.evaluate(facts);

        // assert
        assertThat(result)
            .as("Condition evaluates true")
            .isTrue();
    }

    @Test(expected = IllegalArgumentException.class)
    public void when_givenNoOrder_throwException() {
        // arrange
        facts = new Facts();

        // act
        inStoreOrderCondition.evaluate(facts);

        // assert - exception is thrown
    }

}
