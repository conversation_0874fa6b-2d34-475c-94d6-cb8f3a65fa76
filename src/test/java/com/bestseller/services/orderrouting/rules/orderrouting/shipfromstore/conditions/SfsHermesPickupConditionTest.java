package com.bestseller.services.orderrouting.rules.orderrouting.shipfromstore.conditions;

import com.bestseller.services.orderrouting.model.OrderModel;
import com.bestseller.services.orderrouting.utils.generator.OrderModelGenerator;
import org.jeasy.rules.api.Condition;
import org.jeasy.rules.api.Facts;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import static com.bestseller.services.orderrouting.rules.facts.FactNames.ORDER_MODEL_FACT;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;

/**
 * Unit tests for {@link AbstractOrderNotRejectedCondition}.
 */
@RunWith(MockitoJUnitRunner.class)
public class SfsHermesPickupConditionTest {
    private Facts facts;
    private Condition sfsHermesPickupCondition;
    private OrderModel orderModel;

    @Before
    public void setUp() {
        sfsHermesPickupCondition = new SfsHermesPickupCondition();

        orderModel = OrderModelGenerator.createTestOrderModel();

        facts = new Facts();
        facts.put(ORDER_MODEL_FACT, orderModel);
    }

    @Test
    public void evaluate_carrierHermesAndVariantPickup_returnTrue() {
        // arrange
        orderModel.setCarrier("HERMES");
        orderModel.setCarrierVariant("PICKUP");

        // act
        var result = sfsHermesPickupCondition.evaluate(facts);

        // assert
        assertThat("Hermes condition is true", result, is(true));
    }

    @Test
    public void evaluate_carrierHermesAndVariantHome_returnFalse() {
        // arrange
        orderModel.setCarrier("HERMES");
        orderModel.setCarrierVariant("HOME");

        // act
        var result = sfsHermesPickupCondition.evaluate(facts);

        // assert
        assertThat("Hermes condition is false", result, is(false));
    }
}
