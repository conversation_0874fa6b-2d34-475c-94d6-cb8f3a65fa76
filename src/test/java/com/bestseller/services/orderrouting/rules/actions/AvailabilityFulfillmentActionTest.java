package com.bestseller.services.orderrouting.rules.actions;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.Item;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailability;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailabilityResponse;
import com.bestseller.services.orderrouting.configuration.Warehouses;
import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.model.OrderModel;
import com.bestseller.services.orderrouting.service.OrderService;
import com.bestseller.services.orderrouting.strategy.Fulfillment;
import com.bestseller.services.orderrouting.utils.TestingFeatureManager;
import lombok.extern.slf4j.Slf4j;
import org.jeasy.rules.api.Facts;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Answers;
import org.mockito.InOrder;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.togglz.core.context.StaticFeatureManagerProvider;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.bestseller.services.orderrouting.rules.facts.FactNames.ITEM_AVAILABILITY_RESPONSE_FACT;
import static com.bestseller.services.orderrouting.rules.facts.FactNames.ORDER_MODEL_FACT;
import static org.hamcrest.Matchers.sameInstance;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.inOrder;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.mockito.hamcrest.MockitoHamcrest.argThat;

@RunWith(MockitoJUnitRunner.class)
@Slf4j
public class AvailabilityFulfillmentActionTest {

    @Mock
    private OrderService orderService;

    @Mock
    private Fulfillment fulfillment;

    @Mock(answer = Answers.RETURNS_MOCKS)
    private Warehouses warehouses;

    @InjectMocks
    private AvailabilityFulfillmentAction availabilityFulfillmentAction;

    @Before
    public void setUp() {
        StaticFeatureManagerProvider.setFeatureManager(new TestingFeatureManager());
    }

    @Test
    public void execute_validFacts_availableInventoryEventIsPublished() {
        // arrange
        final int quantity = 42;
        Map<String, List<List<OrderLineQuantityModel>>> parts = new HashMap<>();
        parts.put("samplePart", new ArrayList<>());
        OrderModel order = new OrderModel();
        List<OrderLineQuantityModel> lines = new ArrayList<>();
        order.setOrderLineQuantities(lines);

        List<ItemAvailability> availabilities =
                List.of(new ItemAvailability(quantity, "SW", ItemAvailability.Type.WAREHOUSE, "HOUSE"));
        Map<String, List<ItemAvailability>> eanAvailability = Map.of("my ean", availabilities);

        var itemAvailabilityResponse = new ItemAvailabilityResponse("my id", List.of(new Item("my ean", availabilities)));
        Facts facts = new Facts();
        facts.put(ORDER_MODEL_FACT, order);
        facts.put(ITEM_AVAILABILITY_RESPONSE_FACT, itemAvailabilityResponse);

        when(fulfillment.allocateOrderParts(any(), any()))
                .thenReturn(parts);

        // act
        availabilityFulfillmentAction.execute(facts);

        // assert
        verify(fulfillment).allocateOrderParts(argThat(sameInstance(order)), eq(eanAvailability));
        InOrder inOrder = inOrder(orderService);
        inOrder.verify(orderService).assignOrderPartFulfillmentNodes(argThat(sameInstance(order)), argThat(sameInstance(parts)));
        inOrder.verify(orderService).saveOrderLines(argThat(sameInstance(lines)));
    }

    @Test
    public void execute_emptyAllocationReturned_availableInventoryEventIsNotPublished() {
        // arrange
        OrderModel order = new OrderModel();
        order.setOrderLineQuantities(new ArrayList<>());
        var itemAvailabilityResponse = new ItemAvailabilityResponse("", List.of(new Item("", new ArrayList<>())));

        Facts facts = new Facts();
        facts.put(ORDER_MODEL_FACT, order);
        facts.put(ITEM_AVAILABILITY_RESPONSE_FACT, itemAvailabilityResponse);

        when(fulfillment.allocateOrderParts(any(), any()))
                .thenReturn(Map.of());

        // act
        availabilityFulfillmentAction.execute(facts);

        // assert
        InOrder inOrder = inOrder(orderService);
        inOrder.verify(orderService, never()).assignOrderPartFulfillmentNodes(any(), any());
        inOrder.verify(orderService, never()).saveOrderLines(any());
    }
}
