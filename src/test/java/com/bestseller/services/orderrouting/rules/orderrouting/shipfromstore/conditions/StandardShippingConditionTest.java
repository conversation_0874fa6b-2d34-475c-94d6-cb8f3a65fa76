package com.bestseller.services.orderrouting.rules.orderrouting.shipfromstore.conditions;

import com.bestseller.services.orderrouting.model.OrderModel;
import org.jeasy.rules.api.Facts;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import static com.bestseller.services.orderrouting.rules.facts.FactNames.ORDER_MODEL_FACT;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

/**
 * Unit tests for {@link StandardShippingOrderCondition}.
 */
@RunWith(MockitoJUnitRunner.class)
public class StandardShippingConditionTest {

    private Facts facts;

    private StandardShippingOrderCondition shippingOrderCondition;

    @Before
    public void setUp() {
        shippingOrderCondition = new StandardShippingOrderCondition();
    }

    @Test
    public void when_givenStandardShipping_returnTrue() {
        // arrange
        OrderModel orderModel = new OrderModel();
        orderModel.setShippingMethod("STANDARD");

        facts = new Facts();
        facts.put(ORDER_MODEL_FACT, orderModel);

        // act
        boolean result = shippingOrderCondition.evaluate(facts);

        // assert
        assertTrue("Standard shipping should return true", result);
    }

    @Test
    public void when_givenExpressShipping_returnFalse() {
        // arrange
        OrderModel orderModel = new OrderModel();
        orderModel.setShippingMethod("EXPRESS");

        facts = new Facts();
        facts.put(ORDER_MODEL_FACT, orderModel);

        // act
        boolean result = shippingOrderCondition.evaluate(facts);

        // assert
        assertFalse("Express shipping should return false", result);
    }

    @Test(expected = IllegalArgumentException.class)
    public void when_givenNoOrder_throwException() {
        // arrange
        facts = new Facts();

        // act
        shippingOrderCondition.evaluate(facts);

        // assert - exception is thrown
    }

}
