package com.bestseller.services.orderrouting.rules.orderrouting.shipfromstore.conditions;

import com.bestseller.services.orderrouting.model.OrderModel;
import com.bestseller.services.orderrouting.utils.generator.OrderModelGenerator;
import org.jeasy.rules.api.Facts;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import static com.bestseller.services.orderrouting.rules.facts.FactNames.ORDER_MODEL_FACT;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

@RunWith(MockitoJUnitRunner.class)
public class SfsBigOrderConditionTest {

    private static final int THRESHOLD = 10;
    private static final int NORMAL_ORDER_LINES = 3;
    private static final int BIG_ORDER_LINES = 15;

    private SfsBigOrderCondition condition;
    private OrderModel orderModel;
    private Facts facts;

    @Before
    public void setUp() {
        condition = new SfsBigOrderCondition();
        condition.setThreshold(THRESHOLD);
        orderModel = OrderModelGenerator.createTestOrderModel();
        orderModel.setOrderLineQuantities(OrderModelGenerator.createNOrderLines(orderModel.getOrderId(), BIG_ORDER_LINES));

        facts = new Facts();
        facts.put(ORDER_MODEL_FACT, orderModel);
    }

    @Test(expected = NullPointerException.class)
    public void when_noOrder_throwException() {
        // arrange
        facts = new Facts();

        // act
        condition.evaluate(facts);
    }

    @Test
    public void evaluate_givenBigOrder_returnsTrue() {
        // arrange

        // act
        boolean evaluated = condition.evaluate(facts);

        // assert
        assertTrue("Evaluation of the condition should be true", evaluated);
    }

    @Test
    public void evaluate_givenNormalOrder_returnsFalse() {
        // arrange
        orderModel.setOrderLineQuantities(OrderModelGenerator.createNOrderLines(orderModel.getOrderId(), NORMAL_ORDER_LINES));

        // act
        boolean evaluated = condition.evaluate(facts);

        // assert
        assertFalse("Evaluation of the condition should be false", evaluated);
    }
}
