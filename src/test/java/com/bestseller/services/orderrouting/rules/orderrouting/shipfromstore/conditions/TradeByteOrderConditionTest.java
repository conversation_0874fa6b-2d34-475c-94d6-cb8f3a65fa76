package com.bestseller.services.orderrouting.rules.orderrouting.shipfromstore.conditions;

import com.bestseller.services.orderrouting.model.OrderModel;
import org.jeasy.rules.api.Facts;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import static com.bestseller.services.orderrouting.rules.facts.FactNames.ORDER_MODEL_FACT;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

/**
 * Unit tests for {@link TradebyteOrderCondition}.
 */
@RunWith(MockitoJUnitRunner.class)
public class TradeByteOrderConditionTest {

    private Facts facts;

    private TradebyteOrderCondition tradebyteOrderCondition;

    @Before
    public void setUp() {
        tradebyteOrderCondition = new TradebyteOrderCondition();
    }

    @Test
    public void when_givenNotTradebyteOrder_returnFalse() {
        // arrange
        OrderModel orderModel = new OrderModel();
        orderModel.setOrderId("********");

        facts = new Facts();
        facts.put(ORDER_MODEL_FACT, orderModel);

        // act
        boolean result = tradebyteOrderCondition.evaluate(facts);

        // assert
        assertFalse("Condition evaluates false", result);
    }

    @Test
    public void when_givenTradebyteOrder_returnTrue() {
        // arrange
        OrderModel orderModel = new OrderModel();
        orderModel.setOrderId("TB********");

        facts = new Facts();
        facts.put(ORDER_MODEL_FACT, orderModel);

        // act
        boolean result = tradebyteOrderCondition.evaluate(facts);

        // assert
        assertTrue("Condition evaluates true", result);
    }

    @Test(expected = IllegalArgumentException.class)
    public void when_givenNoOrder_throwException() {
        // arrange
        facts = new Facts();

        // act
        tradebyteOrderCondition.evaluate(facts);

        // assert - exception is thrown
    }

}
