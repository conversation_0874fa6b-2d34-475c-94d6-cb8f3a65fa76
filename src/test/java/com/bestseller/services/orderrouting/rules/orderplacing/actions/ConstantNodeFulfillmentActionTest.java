package com.bestseller.services.orderrouting.rules.orderplacing.actions;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderForFulfillment.OrderForFulfillment;
import com.bestseller.services.orderrouting.event.AvailableInventoryEvent;
import com.bestseller.services.orderrouting.model.OrderLineQuantityStatus;
import com.bestseller.services.orderrouting.model.OrderModel;
import com.bestseller.services.orderrouting.service.OrderService;
import com.bestseller.services.orderrouting.utils.generator.OrderModelGenerator;
import org.jeasy.rules.api.Facts;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.context.ApplicationEventPublisher;

import java.util.Collections;

import static com.bestseller.services.orderrouting.rules.facts.FactNames.ORDER_MODEL_FACT;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;

/**
 * Unit tests for {@link ConstantNodeFulfillmentAction}.
 */
@RunWith(MockitoJUnitRunner.class)
public class ConstantNodeFulfillmentActionTest {

    private static final String CONSTANT_WAREHOUSE = "FOO_BAR";

    @Mock
    private OrderService orderService;
    @InjectMocks
    private ConstantNodeFulfillmentAction constantNodeFulfillmentAction;
    @Mock
    private ApplicationEventPublisher eventPublisher;

    @Captor
    private ArgumentCaptor<AvailableInventoryEvent> avilableInventoryEventCaptor;

    private Facts facts;
    private OrderModel order;

    @Before
    public void setUp() {
        constantNodeFulfillmentAction = new ConstantNodeFulfillmentAction(orderService, eventPublisher, CONSTANT_WAREHOUSE);

        order = OrderModelGenerator.createTestOrderModel();
        order.setOrderLineQuantities(OrderModelGenerator.createThreeTestOrderLines(order));
        order.setOrderLineQuantities(Collections.emptyList());

        facts = new Facts();
        facts.put(ORDER_MODEL_FACT, order);
    }

    @Test
    public void execute_constantWarehouse_chainFactBroken() {
        // arrange - done in setup.

        // act
        constantNodeFulfillmentAction.execute(facts);

        // assert
        verify(orderService).updateOrderLinesStatus(eq(order.getOrderLineQuantities()), eq(OrderLineQuantityStatus.ROUTING));
        verify(eventPublisher).publishEvent(avilableInventoryEventCaptor.capture());
        var availableInventoryEvent = avilableInventoryEventCaptor.getValue();
        assertEquals("Order should match", order, availableInventoryEvent.getOrderModel());
    }

    @Test
    public void execute_getWarehouse_returnsConstantWarehouse() {
        // arrange - done in setup.

        // act
        var warehouse = constantNodeFulfillmentAction.getFulfillmentNode(new OrderForFulfillment(), order);

        // assert
        assertThat("Warehouse match", warehouse, equalTo(CONSTANT_WAREHOUSE));
    }
}
