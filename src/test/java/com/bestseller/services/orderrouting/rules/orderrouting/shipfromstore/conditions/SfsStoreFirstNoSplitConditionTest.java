package com.bestseller.services.orderrouting.rules.orderrouting.shipfromstore.conditions;

import com.bestseller.services.orderrouting.feature.toggles.ORSFeatures;
import com.bestseller.services.orderrouting.utils.TestingFeatureManager;
import org.jeasy.rules.api.Facts;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.togglz.core.context.StaticFeatureManagerProvider;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

/**
 * Unit tests for {@link SfsSoldOutCountryCondition}.
 */
@RunWith(MockitoJUnitRunner.class)
public class SfsStoreFirstNoSplitConditionTest {
    private static final TestingFeatureManager TESTING_FEATURE_MANAGER = new TestingFeatureManager();

    private Facts facts;
    private SfsStoreFirstNoSplitCondition sfsStoreFirstNoSplitCondition;

    @Before
    public void setUp() {
        facts = new Facts();
        sfsStoreFirstNoSplitCondition = new SfsStoreFirstNoSplitCondition();
        StaticFeatureManagerProvider.setFeatureManager(TESTING_FEATURE_MANAGER);
    }

    @Test
    public void when_sfsStoreFirstFeatureIsEnabled_returnFalse() {
        // arrange
        ORSFeatures.SFS_STORE_FIRST_SPLIT_ALLOWED.setActive(true);

        // act
        boolean result = sfsStoreFirstNoSplitCondition.evaluate(facts);

        // assert
        assertFalse("Condition evaluates false", result);
    }

    @Test
    public void when_sfsStoreFirstFeatureIsDisabled_returnTrue() {
        // arrange
        ORSFeatures.SFS_STORE_FIRST_SPLIT_ALLOWED.setActive(false);

        // act
        boolean result = sfsStoreFirstNoSplitCondition.evaluate(facts);

        // assert
        assertTrue("Condition evaluates true", result);
    }

}
