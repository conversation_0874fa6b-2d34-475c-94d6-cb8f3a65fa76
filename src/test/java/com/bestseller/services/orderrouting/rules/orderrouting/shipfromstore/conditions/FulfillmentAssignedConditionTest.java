package com.bestseller.services.orderrouting.rules.orderrouting.shipfromstore.conditions;

import org.jeasy.rules.api.Facts;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import static com.bestseller.services.orderrouting.rules.facts.FactNames.FULFILLMENT_INFORMATION_ASSIGNED_FACT;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

@RunWith(MockitoJUnitRunner.class)
public class FulfillmentAssignedConditionTest {

    @InjectMocks
    private FulfillmentAssignedCondition fulfillmentAssignedCondition;

    private Facts facts;

    @Before
    public void setUp() {
        facts = new Facts();
    }

    @Test
    public void evaluate_noFulfillmentInformationSaved_returnsTrue() {
        // arrange - done in setup

        // act
        boolean result = fulfillmentAssignedCondition.evaluate(facts);

        // assert
        assertTrue("Condition evaluates true", result);
    }

    @Test
    public void evaluate_fulfillmentInformationSaved_returnsFalse() {
        // arrange
        facts.put(FULFILLMENT_INFORMATION_ASSIGNED_FACT, true);

        // act
        boolean result = fulfillmentAssignedCondition.evaluate(facts);

        // assert
        assertFalse("Condition evaluates false", result);
    }
}
