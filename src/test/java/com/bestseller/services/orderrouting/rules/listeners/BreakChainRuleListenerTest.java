package com.bestseller.services.orderrouting.rules.listeners;

import org.jeasy.rules.api.Facts;
import org.jeasy.rules.api.Rule;
import org.jeasy.rules.core.BasicRule;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import static com.bestseller.services.orderrouting.rules.listeners.BreakChainRuleListener.BREAK_EXECUTION;
import static junit.framework.TestCase.assertTrue;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.mock;

/**
 * Unit tests for {@link BreakChainRuleListener}.
 */
@RunWith(MockitoJUnitRunner.class)
public class BreakChainRuleListenerTest {

    @InjectMocks
    private BreakChainRuleListener breakChainRuleListener;

    private Facts facts;
    private Rule rule;
    private Rule breakChainRule = new BreakChainRule();

    @Before
    public void setUp() {
        facts = new Facts();
        rule = mock(Rule.class);
    }

    @Test
    public void beforeEvaluate_noBreakChainFact_returnFalse() {
        // arrange - done in setup

        // act
        boolean result = breakChainRuleListener.beforeEvaluate(rule, facts);

        // assert
        assertTrue("Expecting true result when no break chain fact is given.", result);
    }

    @Test
    public void beforeEvaluate_breakChainFactFalse_returnFalse() {
        // arrange
        facts.put(BREAK_EXECUTION, false);

        // act
        boolean result = breakChainRuleListener.beforeEvaluate(rule, facts);

        // assert
        assertTrue("Expecting true result when break chain fact is false.", result);
    }

    @Test
    public void beforeEvaluate_breakChainFactTrue_returnTrue() {
        // arrange
        facts.put(BREAK_EXECUTION, true);

        // act
        boolean result = breakChainRuleListener.beforeEvaluate(rule, facts);

        // assert
        assertFalse("Expecting false result when break chain fact is false.", result);
    }

    @Test
    public void afterEvaluate_validFactsAndRule_noExceptionsThrown() {
        // arrange - done in setup

        // act
        breakChainRuleListener.afterEvaluate(rule, facts, true);

        // assert - no exceptions thrown
    }

    @Test
    public void beforeExecute_validFactsAndBreakChainRule_breakExecution() {
        // arrange - done in setup

        // act
        breakChainRuleListener.beforeExecute(breakChainRule, facts);

        // assert
        assertNotNull("Break chain facts are not null", facts.get(BREAK_EXECUTION));
        assertTrue("Break execution should be true", facts.get(BREAK_EXECUTION));
    }

    @Test
    public void onSuccess_validFactsAndRule_noExceptionsThrown() {
        // arrange - done in setup

        // act
        breakChainRuleListener.onSuccess(rule, facts);

        // assert - no exceptions thrown
    }

    @Test
    public void onFailure_validFactsAndRule_noExceptionsThrown() {
        // arrange - done in setup

        // act
        breakChainRuleListener.onFailure(rule, facts, new Exception());

        // assert - no exceptions thrown
    }

    /**
     * An empty implementation of BasicRule and annotated with BreakChain.
     * Used to test if rules annotated with BreakChain would break the rule chain execution.
     */
    @BreakChain
    public class BreakChainRule extends BasicRule {

    }
}
