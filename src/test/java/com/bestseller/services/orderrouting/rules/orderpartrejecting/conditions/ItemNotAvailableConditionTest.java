package com.bestseller.services.orderrouting.rules.orderpartrejecting.conditions;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartRejected.OrderPartRejected;
import com.bestseller.services.orderrouting.rules.facts.FactNames;
import com.bestseller.services.orderrouting.service.CancellationReason;
import com.bestseller.services.orderrouting.utils.generator.OrderPartRejectedGenerator;
import org.jeasy.rules.api.Facts;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.List;

import static com.bestseller.services.orderrouting.constants.FulfillmentNodeConstants.SHIP_FROM_STORE_NL;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

/**
 * Unit tests for {@link ItemNotAvailableCondition}.
 */
@RunWith(MockitoJUnitRunner.class)
public class ItemNotAvailableConditionTest {
    private OrderPartRejected orderPartRejected;

    @InjectMocks
    private ItemNotAvailableCondition itemNotAvailableCondition;

    @Before
    public void setUp() {
        List<String> eans = Arrays.asList("123456", "789101", "129346");

        orderPartRejected = OrderPartRejectedGenerator.createFullOrderPartRejectedMessage("*********", SHIP_FROM_STORE_NL, eans);
    }

    @Test
    public void evaluate_allOrderLinesAreRejectedByStore_evaluatesTrue() {
        // arrange
        orderPartRejected.getOrderLines().stream().forEach(ol -> ol.setCancelReason(CancellationReason.ITEM_NOT_AVAILABLE.getReason()));

        Facts facts = new Facts();
        facts.put(FactNames.ORDER_PART_REJECTED_FACT, orderPartRejected);

        // act
        boolean result = itemNotAvailableCondition.evaluate(facts);

        // assert
        assertTrue("Condition should evaluate true", result);
    }

    @Test
    public void evaluate_notAllOrderLinesAreRejectedByStore_evaluatesFalse() {
        // arrange
        orderPartRejected.getOrderLines().get(0).setCancelReason(CancellationReason.ITEM_DAMAGED.getReason());

        Facts facts = new Facts();
        facts.put(FactNames.ORDER_PART_REJECTED_FACT, orderPartRejected);

        // act
        boolean result = itemNotAvailableCondition.evaluate(facts);

        // assert
        assertFalse("Condition should evaluate false", result);
    }

    @Test
    public void evaluate_allOrderLinesAreNotAvailable_evaluatesFalse() {
        // arrange
        orderPartRejected.getOrderLines().stream().forEach(ol -> ol.setCancelReason(CancellationReason.MANUAL_CANCELLATION.getReason()));

        Facts facts = new Facts();
        facts.put(FactNames.ORDER_PART_REJECTED_FACT, orderPartRejected);

        // act
        boolean result = itemNotAvailableCondition.evaluate(facts);

        // assert
        assertFalse("Condition should evaluate false", result);
    }
}
