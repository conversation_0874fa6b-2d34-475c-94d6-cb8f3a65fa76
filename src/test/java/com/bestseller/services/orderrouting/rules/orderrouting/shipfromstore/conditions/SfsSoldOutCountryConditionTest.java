package com.bestseller.services.orderrouting.rules.orderrouting.shipfromstore.conditions;

import com.bestseller.services.orderrouting.configuration.pojos.sfs.OnlineFlag;
import com.bestseller.services.orderrouting.configuration.pojos.sfs.ShipFromStoreConfig;
import com.bestseller.services.orderrouting.configuration.SfsConfig;
import com.bestseller.services.orderrouting.model.AddressModel;
import com.bestseller.services.orderrouting.model.OrderModel;
import org.jeasy.rules.api.Facts;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;

import static com.bestseller.services.orderrouting.rules.facts.FactNames.ORDER_MODEL_FACT;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

/**
 * Unit tests for {@link SfsSoldOutCountryCondition}.
 */
@RunWith(MockitoJUnitRunner.class)
public class SfsSoldOutCountryConditionTest {
    private static final String NOT_CONFIGURED_COUNTRY = "JP";
    private static final String ENABLED_COUNTRY = "NO";
    private static final String DISABLED_COUNTRY = "NL";
    private static final String DIFFERENT_TYPE_COUNTRY = "XX";
    private static final String CONDITION_EVALUATES_FALSE = "Condition evaluates false";

    private SfsSoldOutCountryCondition condition;
    private OrderModel orderModel;
    private Facts facts;

    @Before
    public void setUp() {
        orderModel = new OrderModel();
        facts = new Facts();
        final SfsConfig sfsConfig = new SfsConfig();
        final ShipFromStoreConfig enabledConfig = ShipFromStoreConfig.builder().country(ENABLED_COUNTRY)
                                                                           .type(SfsConfig.SOLD_OUT)
                                                                            .build();
        final ShipFromStoreConfig disabledConfig = ShipFromStoreConfig.builder().country(DISABLED_COUNTRY)
                                                                            .type(SfsConfig.SOLD_OUT)
                                                                            .build();
        final ShipFromStoreConfig irrelevantConfig = ShipFromStoreConfig.builder().country(DIFFERENT_TYPE_COUNTRY)
                                                                              .type(SfsConfig.STORES_FIRST)
                                                                              .build();
        enabledConfig.setOnlineFlag(OnlineFlag.PRODUCTION);
        sfsConfig.addAll(Arrays.asList(enabledConfig, disabledConfig, irrelevantConfig));
        condition = new SfsSoldOutCountryCondition(sfsConfig);
    }

    @Test(expected = IllegalArgumentException.class)
    public void when_noOrder_throwException() {
        // arrange - done in setup

        // act
        condition.evaluate(facts);
    }

    @Test
    public void when_noShippingAddress_returnFalse() {
        // arrange
        facts.put(ORDER_MODEL_FACT, orderModel);

        // act
        boolean result = condition.evaluate(facts);

        // assert
        assertFalse(CONDITION_EVALUATES_FALSE, result);
    }

    @Test
    public void when_noCountry_returnFalse() {
        // arrange
        AddressModel shippingAddress = new AddressModel();
        orderModel.setShippingAddress(shippingAddress);
        facts.put(ORDER_MODEL_FACT, orderModel);

        // act
        boolean result = condition.evaluate(facts);

        // assert
        assertFalse(CONDITION_EVALUATES_FALSE, result);
    }

    @Test
    public void when_notConfiguredCountry_returnFalse() {
        // arrange
        AddressModel shippingAddress = new AddressModel();
        shippingAddress.setCountry(NOT_CONFIGURED_COUNTRY);
        orderModel.setShippingAddress(shippingAddress);
        facts.put(ORDER_MODEL_FACT, orderModel);

        // act
        boolean result = condition.evaluate(facts);

        // assert
        assertFalse(CONDITION_EVALUATES_FALSE, result);
    }

    @Test
    public void when_enabledCountry_returnTrue() {
        // arrange
        AddressModel shippingAddress = new AddressModel();
        shippingAddress.setCountry(ENABLED_COUNTRY);
        orderModel.setShippingAddress(shippingAddress);
        facts.put(ORDER_MODEL_FACT, orderModel);

        // act
        boolean result = condition.evaluate(facts);

        // assert
        assertTrue("Condition evaluates true", result);
    }

    @Test
    public void when_disabledCountry_returnFalse() {
        // arrange
        AddressModel shippingAddress = new AddressModel();
        shippingAddress.setCountry(DISABLED_COUNTRY);
        orderModel.setShippingAddress(shippingAddress);
        facts.put(ORDER_MODEL_FACT, orderModel);

        // act
        boolean result = condition.evaluate(facts);

        // assert
        assertFalse(CONDITION_EVALUATES_FALSE, result);
    }

    @Test
    public void when_otherTypeCountry_returnFalse() {
        // arrange
        AddressModel shippingAddress = new AddressModel();
        shippingAddress.setCountry(DIFFERENT_TYPE_COUNTRY);
        orderModel.setShippingAddress(shippingAddress);
        facts.put(ORDER_MODEL_FACT, orderModel);

        // act
        boolean result = condition.evaluate(facts);

        // assert
        assertFalse(CONDITION_EVALUATES_FALSE, result);
    }
}
