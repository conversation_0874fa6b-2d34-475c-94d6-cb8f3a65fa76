package com.bestseller.services.orderrouting.rules.orderpartrejecting.actions;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartRejected.OrderPartRejected;
import com.bestseller.services.orderrouting.configuration.Warehouses;
import com.bestseller.services.orderrouting.converter.OrderLineExtractor;
import com.bestseller.services.orderrouting.feature.toggles.ORSFeatures;
import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.model.OrderLineQuantityStatus;
import com.bestseller.services.orderrouting.model.OrderModel;
import com.bestseller.services.orderrouting.rules.facts.FactNames;
import com.bestseller.services.orderrouting.service.CancellationReason;
import com.bestseller.services.orderrouting.service.OrderService;
import com.bestseller.services.orderrouting.utils.TestingFeatureManager;
import com.bestseller.services.orderrouting.utils.generator.OrderModelGenerator;
import com.bestseller.services.orderrouting.utils.generator.OrderPartRejectedGenerator;
import org.jeasy.rules.api.Facts;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.context.ApplicationEventPublisher;
import org.togglz.core.context.StaticFeatureManagerProvider;

import java.util.List;
import java.util.Map;

import static com.bestseller.services.orderrouting.rules.facts.FactNames.ORDER_MODEL_FACT;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.nullValue;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class RerouteOrderPartActionTest {
    private static final String FULFILLMENT_CENTER_EAST = "HOUSE_EAST";
    private static final String FULFILLMENT_CENTER_WEST = "HOUSE_WEST";

    private Facts facts;
    private OrderModel order;
    private OrderPartRejected orderPartRejected;

    @Mock
    private OrderLineExtractor orderLineExtractor;

    @Mock
    private OrderService orderService;

    @Mock
    private ApplicationEventPublisher applicationEventPublisher;

    @Spy
    private Warehouses warehouses = new Warehouses();

    @InjectMocks
    private RerouteOrderPartAction rerouteOrderPartAction;

    @Captor
    private ArgumentCaptor<List<OrderLineQuantityModel>> orderLineCaptor;

    @Before
    public void setUp() {
        StaticFeatureManagerProvider.setFeatureManager(new TestingFeatureManager());
        ORSFeatures.PRIORITIZE_FCW_OVER_FCE.setActive(false);

        warehouses.setFulfillmentCenterEast(FULFILLMENT_CENTER_EAST);
        warehouses.setFulfillmentCenterWest(FULFILLMENT_CENTER_WEST);

        order = OrderModelGenerator.createTestOrderModel();
        order.setOrderLineQuantities(OrderModelGenerator.createThreeTestOrderLines(order));

        orderPartRejected = OrderPartRejectedGenerator.createFullOrderPartRejectedMessage();

        facts = new Facts();
        facts.put(FactNames.ORDER_MODEL_FACT, order);
        facts.put(FactNames.ORDER_PART_REJECTED_FACT, orderPartRejected);
    }

    @Test
    public void execute_someOrder_primaryWarehouseAssigned() {
        // act
        rerouteOrderPartAction.execute(facts);

        // assert
        verify(orderService).assignOrderPartFulfillmentNodes(order, Map.of(FULFILLMENT_CENTER_EAST, List.of(order.getOrderLineQuantities())));
    }

    @Test
    public void execute_ruleEvaluatesToTrue_orderLinesUpdated() {
        // arrange
        List<OrderLineQuantityModel> orderLinesInMessage = List.of(order.getOrderLineQuantities().get(0));
        when(orderLineExtractor.extractOrderLinesFromMessage(order, orderPartRejected)).thenReturn(orderLinesInMessage);

        // act
        rerouteOrderPartAction.execute(facts);

        // assert
        verify(orderService, atLeastOnce()).saveOrderLines(orderLineCaptor.capture());

        OrderLineQuantityModel orderLineChanged = orderLineCaptor.getValue().get(0);
        assertThat("Cancel reason should be STORE REJECTION", orderLineChanged.getCancelReason(),
                is(CancellationReason.STORE_REJECTION));
        assertThat("Status should be PLACED", orderLineChanged.getStatus(), is(OrderLineQuantityStatus.PLACED));
        assertThat("Fulfillment node should be null", orderLineChanged.getFulfillmentNode(), nullValue());
        assertThat("Order part number should be null", orderLineChanged.getOrderPartNumber(), nullValue());
    }

    @Test
    public void execute_jjCheckoutOrderAndFcwEnabled_fcwAssigned() {
        // arrange
        order.setCheckout("jj");
        facts.put(ORDER_MODEL_FACT, order);
        ORSFeatures.PRIORITIZE_FCW_OVER_FCE.setActive(true);

        // act
        rerouteOrderPartAction.execute(facts);

        // assert
        verify(orderService).assignOrderPartFulfillmentNodes(order, Map.of(FULFILLMENT_CENTER_WEST, List.of(order.getOrderLineQuantities())));
    }
}
