package com.bestseller.services.orderrouting.rules.orderrouting.shipfromstore.conditions;

import com.bestseller.services.orderrouting.feature.toggles.ORSFeatures;
import com.bestseller.services.orderrouting.utils.TestingFeatureManager;
import org.jeasy.rules.api.Facts;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.togglz.core.context.StaticFeatureManagerProvider;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

/**
 * Unit tests for {@link SfsSoldOutCountryCondition}.
 */
@RunWith(MockitoJUnitRunner.class)
public class SfsStoreFirstSplitConditionTest {
    private static final TestingFeatureManager TESTING_FEATURE_MANAGER = new TestingFeatureManager();

    private Facts facts;
    private SfsStoreFirstSplitCondition sfsStoreFirstSplitCondition;

    @Before
    public void setUp() {
        facts = new Facts();
        sfsStoreFirstSplitCondition = new SfsStoreFirstSplitCondition();
        StaticFeatureManagerProvider.setFeatureManager(TESTING_FEATURE_MANAGER);
    }

    @Test
    public void when_sfsStoreFirstFeatureIsEnabled_returnTrue() {
        // arrange
        ORSFeatures.SFS_STORE_FIRST_SPLIT_ALLOWED.setActive(true);

        // act
        boolean result = sfsStoreFirstSplitCondition.evaluate(facts);

        // assert
        assertTrue("Result should be true", result);
    }

    @Test
    public void when_sfsStoreFirstFeatureIsDisabled_returnFalse() {
        // arrange
        ORSFeatures.SFS_STORE_FIRST_SPLIT_ALLOWED.setActive(false);

        // act
        boolean result = sfsStoreFirstSplitCondition.evaluate(facts);

        // assert
        assertFalse("Result should be false", result);
    }
}
