package com.bestseller.services.orderrouting.rules.orderplacing.conditions;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderForFulfillment.FulfillmentAdvice;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderForFulfillment.OrderForFulfillment;
import com.bestseller.services.orderrouting.model.OrderModel;
import com.bestseller.services.orderrouting.rules.orderplacing.PredefinedFulfillmentNodeRule;
import com.bestseller.services.orderrouting.rules.orderplacing.actions.AdvisedFulfillmentNodeAction;
import org.jeasy.rules.api.Facts;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static com.bestseller.services.orderrouting.rules.facts.FactNames.ORDER_FOR_FULFILLMENT_FACT;
import static com.bestseller.services.orderrouting.rules.facts.FactNames.ORDER_MODEL_FACT;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

/**
 * Unit tests for {@link PredefinedFulfillmentNodeCondition}.
 */
@RunWith(MockitoJUnitRunner.class)
public class PredefinedFulfillmentNodeConditionTest {

    private static final String NODE = "HERMES";
    private static final String CONDITION_EVALUATES_FALSE = "Condition evaluates false";

    @InjectMocks
    private PredefinedFulfillmentNodeCondition fulfillmentNodeCondition;

    @Mock
    private AdvisedFulfillmentNodeAction fulfillmentNodeAction;
    @Mock
    private PredefinedFulfillmentNodeRule predefinedFulfillmentNodeRule;

    private OrderForFulfillment orderForFulfillment;
    private Facts facts;
    private OrderModel orderModel;
    private FulfillmentAdvice fulfillmentAdvice;

    @Before
    public void setUp() {
        orderForFulfillment = new OrderForFulfillment();
        facts = new Facts();
        predefinedFulfillmentNodeRule = new PredefinedFulfillmentNodeRule(fulfillmentNodeCondition, fulfillmentNodeAction);
        orderModel = new OrderModel();
        fulfillmentAdvice = new FulfillmentAdvice();
    }

    @Test
    public void evaluate_orderForFulfillmentReceived_noExceptionThrown() {
        //arrange
        facts.put(ORDER_MODEL_FACT, orderModel);
        facts.put(ORDER_FOR_FULFILLMENT_FACT, orderForFulfillment);

        //act
        predefinedFulfillmentNodeRule.evaluate(facts);
    }

    @Test
    public void evaluate_noOrderForFulfillment_returnFalse() {
        //arrange

        boolean result = predefinedFulfillmentNodeRule.evaluate(facts);

        //assert
        assertFalse(CONDITION_EVALUATES_FALSE, result);
    }

    @Test
    public void evaluate_noForFulfillmentAdvice_returnFalse() {
        //arrange
        facts.put(ORDER_MODEL_FACT, orderModel);
        facts.put(ORDER_FOR_FULFILLMENT_FACT, orderForFulfillment);

        //act
        boolean result = predefinedFulfillmentNodeRule.evaluate(facts);

        //assert
        assertFalse(CONDITION_EVALUATES_FALSE, result);
    }

    @Test
    public void evaluate_noForFulfillmentNode_returnFalse() {
        //arrange
        facts.put(ORDER_MODEL_FACT, orderModel);
        facts.put(ORDER_FOR_FULFILLMENT_FACT, orderForFulfillment);
        orderForFulfillment.setFulfillmentAdvice(fulfillmentAdvice);

        //act
        boolean result = predefinedFulfillmentNodeRule.evaluate(facts);

        //assert
        assertFalse(CONDITION_EVALUATES_FALSE, result);
    }

    @Test
    public void evaluate_orderForFulfillmentNodePresent_returnTrue() {
        //arrange
        facts.put(ORDER_MODEL_FACT, orderModel);
        facts.put(ORDER_FOR_FULFILLMENT_FACT, orderForFulfillment);
        orderForFulfillment.setFulfillmentAdvice(fulfillmentAdvice);
        fulfillmentAdvice.setFulfillmentNode(NODE);

        //act
        boolean result = predefinedFulfillmentNodeRule.evaluate(facts);

        //assert
        assertTrue("Condition evaluates true", result);
    }

    @Test
    public void evaluate_holdFromRouting_returnFalse() {
        //arrange
        facts.put(ORDER_MODEL_FACT, orderModel);
        facts.put(ORDER_FOR_FULFILLMENT_FACT, orderForFulfillment);
        orderForFulfillment.setFulfillmentAdvice(fulfillmentAdvice);
        fulfillmentAdvice.setFulfillmentNode(NODE);
        fulfillmentAdvice.setHoldFromRouting(true);

        //act
        boolean result = predefinedFulfillmentNodeRule.evaluate(facts);

        //assert
        assertFalse(CONDITION_EVALUATES_FALSE, result);
    }
}
