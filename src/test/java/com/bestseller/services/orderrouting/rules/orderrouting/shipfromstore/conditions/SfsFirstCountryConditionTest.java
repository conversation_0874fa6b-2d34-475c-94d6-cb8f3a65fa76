package com.bestseller.services.orderrouting.rules.orderrouting.shipfromstore.conditions;

import com.bestseller.services.orderrouting.configuration.SfsConfig;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashSet;
import java.util.Set;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

/**
 * Unit tests for {@link AbstractOrderPlacedInCountryCondition}.
 */
@RunWith(MockitoJUnitRunner.class)
public class SfsFirstCountryConditionTest {

    @InjectMocks
    private SfsFirstCountryCondition condition;

    @Mock
    private SfsConfig sfsConfig;

    private Set<String> enabledCountries;

    @Before
    public void setUp() {
        enabledCountries = new HashSet<>();
        when(sfsConfig.getEnabledCountriesByType(SfsConfig.STORES_FIRST)).thenReturn(enabledCountries);
    }

    @Test
    public void getCountries_called_returnsEnabledStoresFirst() {
        // arrange - done in setup

        // act
        Set<String> result = condition.getCountries();

        // assert
        assertEquals("Enabled countries should match", enabledCountries, result);
    }
}
