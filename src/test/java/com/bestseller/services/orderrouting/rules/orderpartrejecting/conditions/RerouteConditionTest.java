package com.bestseller.services.orderrouting.rules.orderpartrejecting.conditions;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartRejected.OrderPartRejected;
import com.bestseller.services.orderrouting.configuration.SfsConfig;
import com.bestseller.services.orderrouting.model.OrderModel;
import com.bestseller.services.orderrouting.rules.facts.FactNames;
import com.bestseller.services.orderrouting.utils.generator.OrderModelGenerator;
import com.bestseller.services.orderrouting.utils.generator.OrderPartRejectedGenerator;
import org.jeasy.rules.api.Facts;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.when;

/**
 * Unit tests for {@link RerouteCondition}.
 */
@RunWith(MockitoJUnitRunner.class)
public class RerouteConditionTest {

    private OrderModel order;

    private OrderPartRejected orderPartRejected;

    private Facts facts;

    @Mock
    private SfsConfig sfsConfig;

    @InjectMocks
    private RerouteCondition rerouteCondition;

    @Before
    public void setUp() {
        order = OrderModelGenerator.createTestOrderModel();
        order.setOrderLineQuantities(OrderModelGenerator.createThreeTestOrderLines(order));

        orderPartRejected = OrderPartRejectedGenerator.createFullOrderPartRejectedMessage();

        facts = new Facts();
        facts.put(FactNames.ORDER_MODEL_FACT, order);
        facts.put(FactNames.ORDER_PART_REJECTED_FACT, orderPartRejected);
    }

    @Test
    public void evaluate_nonShipFromStoreWarehouse_evaluatesFalse() {
        // arrange
        orderPartRejected.setWarehouse("INGRAM_MICRO");

        // act
        boolean result = rerouteCondition.evaluate(facts);

        // assert
        assertFalse("Condition should evaluate false", result);
    }

    @Test
    public void evaluate_nonShipFromStoreFirst_evaluatesFalse() {
        // arrange

        // act
        boolean result = rerouteCondition.evaluate(facts);

        // assert
        assertFalse("Condition should evaluate false", result);
    }

    @Test
    public void evaluate_shipFromStoreFirstAndMoreThanOneOrderPart_evaluatesFalse() {
        // arrange
        when(sfsConfig.getEnabledCountriesByType(SfsConfig.STORES_FIRST))
                .thenReturn(Collections.singleton(order.getShippingAddress().getCountry()));
        order.getOrderLineQuantities().get(0).setTotalOrderParts(2);

        // act
        boolean result = rerouteCondition.evaluate(facts);

        // assert
        assertFalse("Condition should evaluate false", result);
    }

    @Test
    public void evaluate_shipFromStoreFirstAndOneOrderPart_evaluatesTrue() {
        // arrange
        when(sfsConfig.getEnabledCountriesByType(SfsConfig.STORES_FIRST))
                .thenReturn(Collections.singleton(order.getShippingAddress().getCountry()));

        // act
        boolean result = rerouteCondition.evaluate(facts);

        // assert
        assertTrue("Condition should evaluate true", result);
    }
}
