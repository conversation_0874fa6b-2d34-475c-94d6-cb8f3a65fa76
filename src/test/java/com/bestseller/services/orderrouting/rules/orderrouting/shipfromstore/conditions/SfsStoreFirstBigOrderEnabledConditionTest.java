package com.bestseller.services.orderrouting.rules.orderrouting.shipfromstore.conditions;

import com.bestseller.services.orderrouting.feature.toggles.ORSFeatures;
import com.bestseller.services.orderrouting.utils.TestingFeatureManager;
import org.jeasy.rules.api.Facts;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.togglz.core.context.StaticFeatureManagerProvider;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

/**
 * Unit tests for {@link SfsStoreFirstBigOrderEnabledCondition}.
 */
@RunWith(MockitoJUnitRunner.class)
public class SfsStoreFirstBigOrderEnabledConditionTest {
    private static final TestingFeatureManager TESTING_FEATURE_MANAGER = new TestingFeatureManager();

    private Facts facts;
    private SfsStoreFirstBigOrderEnabledCondition sfsStoreFirstBigOrderEnabledCondition;

    @Before
    public void setUp() {
        facts = new Facts();
        sfsStoreFirstBigOrderEnabledCondition = new SfsStoreFirstBigOrderEnabledCondition();
        StaticFeatureManagerProvider.setFeatureManager(TESTING_FEATURE_MANAGER);
    }

    @Test
    public void evaluate_givenSfsStoreFirstBigOrderIsEnabled_returnTrue() {
        // arrange
        ORSFeatures.SFS_STORE_FIRST_BIG_ORDERS_ENABLED.setActive(true);

        // act
        boolean result = sfsStoreFirstBigOrderEnabledCondition.evaluate(facts);

        // assert
        assertTrue("Result should be true", result);
    }

    @Test
    public void evaluate_givenSfsStoreFirstBigOrderIsDisabled_returnFalse() {
        // arrange
        ORSFeatures.SFS_STORE_FIRST_BIG_ORDERS_ENABLED.setActive(false);

        // act
        boolean result = sfsStoreFirstBigOrderEnabledCondition.evaluate(facts);

        // assert
        assertFalse("Result should be false", result);
    }
}
