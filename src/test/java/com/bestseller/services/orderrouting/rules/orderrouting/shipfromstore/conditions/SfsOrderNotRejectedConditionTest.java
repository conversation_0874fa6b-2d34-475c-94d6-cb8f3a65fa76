package com.bestseller.services.orderrouting.rules.orderrouting.shipfromstore.conditions;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartRejected.OrderPartRejected;
import com.bestseller.services.orderrouting.utils.generator.OrderPartRejectedGenerator;
import org.jeasy.rules.api.Facts;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import static com.bestseller.services.orderrouting.rules.facts.FactNames.ORDER_PART_REJECTED_FACT;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

/**
 * Unit tests for {@link AbstractOrderNotRejectedCondition}.
 */
@RunWith(MockitoJUnitRunner.class)
public class SfsOrderNotRejectedConditionTest {

    private Facts facts;
    private AbstractOrderNotRejectedCondition sfsOrderNotRejectedCondition;
    private OrderPartRejected orderPartRejected;

    @Before
    public void setUp() {
        facts = new Facts();
        sfsOrderNotRejectedCondition = new SfsOrderNotRejectedCondition();
        orderPartRejected = OrderPartRejectedGenerator.createFullOrderPartRejectedMessage();
    }

    @Test
    public void when_noStoreRejectionFact_returnTrue() {
        // arrange - done in setup

        // act
        boolean result = sfsOrderNotRejectedCondition.evaluate(facts);

        // assert
        assertTrue("Condition evaluates true", result);
    }

    @Test
    public void when_givenStoreRejectionFact_returnFalse() {
        // arrange
        facts.put(ORDER_PART_REJECTED_FACT, orderPartRejected);

        // act
        boolean result = sfsOrderNotRejectedCondition.evaluate(facts);

        // assert
        assertFalse("Condition evaluates false", result);
    }
}
