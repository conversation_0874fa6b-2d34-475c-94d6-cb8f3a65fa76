package com.bestseller.services.orderrouting.rules.orderrouting.routeorder.actions;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailabilityResponse;
import com.bestseller.services.orderrouting.event.AvailableInventoryEvent;
import com.bestseller.services.orderrouting.model.OrderLineQuantityStatus;
import com.bestseller.services.orderrouting.model.OrderModel;
import com.bestseller.services.orderrouting.service.OrderService;
import org.jeasy.rules.api.Facts;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.context.ApplicationEventPublisher;

import static com.bestseller.services.orderrouting.rules.facts.FactNames.ITEM_AVAILABILITY_RESPONSE_FACT;
import static com.bestseller.services.orderrouting.rules.facts.FactNames.ORDER_MODEL_FACT;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;

@RunWith(MockitoJUnitRunner.class)
public class CreateOrderPartsActionTest {
    @Mock
    private ApplicationEventPublisher eventPublisher;
    @Mock
    private OrderService orderService;
    @InjectMocks
    private CreateOrderPartsAction action;

    private Facts facts;
    private OrderModel order;
    @Captor
    private ArgumentCaptor<AvailableInventoryEvent> eventCaptor;

    @Before
    public void init() {
        order = new OrderModel();
        final ItemAvailabilityResponse itemAvailabilityResponse = new ItemAvailabilityResponse();
        facts = new Facts();
        facts.put(ORDER_MODEL_FACT, order);
        facts.put(ITEM_AVAILABILITY_RESPONSE_FACT, itemAvailabilityResponse);
    }

    @Test
    public void execute_validFacts_availableInventoryEventPublished() {
        // arrange - done in setup

        // act
        action.execute(facts);

        // assert
        verify(orderService).updateOrderLinesStatus(eq(order.getOrderLineQuantities()), eq(OrderLineQuantityStatus.ROUTING));
        verify(eventPublisher).publishEvent(eventCaptor.capture());
        assertEquals("Order model should match", order, eventCaptor.getAllValues().iterator().next().getOrderModel());
        OrderModel publishedOrder = eventCaptor.getAllValues().iterator().next().getOrderModel();
        assertEquals("Published order should be as expected.", order, publishedOrder);
    }
}
