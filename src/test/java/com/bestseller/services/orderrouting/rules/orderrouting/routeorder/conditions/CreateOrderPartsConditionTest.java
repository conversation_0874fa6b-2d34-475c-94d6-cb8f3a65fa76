package com.bestseller.services.orderrouting.rules.orderrouting.routeorder.conditions;

import org.jeasy.rules.api.Facts;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertTrue;

@RunWith(MockitoJUnitRunner.class)
public class CreateOrderPartsConditionTest {
    private Facts facts;
    private CreateOrderPartsCondition condition;

    @Before
    public void init() {
        facts = new Facts();
        condition = new CreateOrderPartsCondition();
    }

    @Test
    public void evaluate_executed_returnsTrue() {
        // arrange - done in setup

        // act
        boolean result = condition.evaluate(facts);

        // assert
        assertTrue("Condition evaluates true", result);
    }
}
