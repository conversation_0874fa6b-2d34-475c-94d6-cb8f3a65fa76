package com.bestseller.services.orderrouting.rules.orderplacing.actions;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderForFulfillment.FulfillmentAdvice;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderForFulfillment.OrderForFulfillment;
import com.bestseller.services.orderrouting.event.AvailableInventoryEvent;
import com.bestseller.services.orderrouting.model.OrderLineQuantityStatus;
import com.bestseller.services.orderrouting.model.OrderModel;
import com.bestseller.services.orderrouting.service.OrderService;
import org.jeasy.rules.api.Facts;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.context.ApplicationEventPublisher;

import java.util.List;
import java.util.Map;

import static com.bestseller.services.orderrouting.rules.facts.FactNames.ORDER_FOR_FULFILLMENT_FACT;
import static com.bestseller.services.orderrouting.rules.facts.FactNames.ORDER_MODEL_FACT;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;

/**
 * Unit tests for {@link AdvisedFulfillmentNodeAction}.
 */
@RunWith(MockitoJUnitRunner.class)
public class AdvisedFulfillmentNodeActionTest {
    private static final String FULFILLMENT_NODE = "HERMES";

    @Mock
    private ApplicationEventPublisher eventPublisher;

    @Mock
    private OrderService orderService;

    @InjectMocks
    private AdvisedFulfillmentNodeAction fulfillmentNodeAction;

    @Captor
    private ArgumentCaptor<AvailableInventoryEvent> eventCaptor;

    @Test
    public void execute_orderRoutedToWarehouse_chainFactBroken() {
        // arrange
        OrderModel order = new OrderModel();
        final OrderForFulfillment orderForFulfillment = new OrderForFulfillment();
        final FulfillmentAdvice fulfillmentAdvice = new FulfillmentAdvice();
        fulfillmentAdvice.setFulfillmentNode(FULFILLMENT_NODE);
        orderForFulfillment.setFulfillmentAdvice(fulfillmentAdvice);

        Facts facts = new Facts();
        facts.put(ORDER_MODEL_FACT, order);
        facts.put(ORDER_FOR_FULFILLMENT_FACT, orderForFulfillment);

        // act
        fulfillmentNodeAction.execute(facts);

        // assert
        verify(orderService).assignOrderPartFulfillmentNodes(order, Map.of(FULFILLMENT_NODE, List.of(order.getOrderLineQuantities())));
        verify(orderService).updateOrderLinesStatus(eq(order.getOrderLineQuantities()), eq(OrderLineQuantityStatus.ROUTING));
        verify(eventPublisher).publishEvent(eventCaptor.capture());
        assertEquals("Order should match", order, eventCaptor.getAllValues().iterator().next().getOrderModel());

    }
}
