package com.bestseller.services.orderrouting.rules.orderrouting.shipfromstore.conditions;

import com.bestseller.services.orderrouting.configuration.properties.RulesProperties;
import com.bestseller.services.orderrouting.model.OrderModel;
import org.jeasy.rules.api.Facts;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import static com.bestseller.services.orderrouting.rules.facts.FactNames.ORDER_MODEL_FACT;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

/**
 * Unit tests for {@link AbstractCarrierVariantExcludingCondition}.
 */
@RunWith(MockitoJUnitRunner.class)
public class SfsCarrierVariantExcludingConditionTest {
    private static final String EXCLUDED_CARRIER = "CC";
    private static final String INCLUDED_CARRIER = "HOME";
    private static final String[] EXCLUDED_CARRIER_VARIANTS = {EXCLUDED_CARRIER};
    private AbstractCarrierVariantExcludingCondition condition;
    private OrderModel orderModel;
    private Facts facts;

    @Before
    public void setUp() {
        orderModel = new OrderModel();
        facts = new Facts();
        final RulesProperties properties = new RulesProperties();
        properties.getShipFromStoreFirst().setExcludedCarrierVariants(EXCLUDED_CARRIER_VARIANTS);
        condition = new SfsCarrierVariantExcludingCondition(properties);
    }

    @Test(expected = IllegalArgumentException.class)
    public void when_noOrder_throwException() {
        // arrange - done in setup

        // act
        condition.evaluate(facts);

        // assert - expect exception
    }

    @Test
    public void when_excludedCarrierVariant_returnFalse() {
        // arrange
        orderModel.setCarrierVariant(EXCLUDED_CARRIER);
        facts.put(ORDER_MODEL_FACT, orderModel);

        // act
        boolean result = condition.evaluate(facts);

        // assert
        assertFalse("Condition evaluates false", result);
    }

    @Test
    public void when_includedCarrierVariant_returnTrue() {
        // arrange
        orderModel.setCarrierVariant(INCLUDED_CARRIER);
        facts.put(ORDER_MODEL_FACT, orderModel);

        // act
        boolean result = condition.evaluate(facts);

        // assert
        assertTrue("Condition evaluates true", result);
    }
}
