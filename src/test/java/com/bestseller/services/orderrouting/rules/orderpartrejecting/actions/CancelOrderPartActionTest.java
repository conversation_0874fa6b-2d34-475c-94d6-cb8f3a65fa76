package com.bestseller.services.orderrouting.rules.orderpartrejecting.actions;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartRejected.OrderPartRejected;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartsCancelled.OrderPartsCancelled;
import com.bestseller.services.orderrouting.converter.OrderLineExtractor;
import com.bestseller.services.orderrouting.converter.OrderPartsCancelledConverter;
import com.bestseller.services.orderrouting.messaging.producer.OrderPartsCancelledProducer;
import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.model.OrderLineQuantityStatus;
import com.bestseller.services.orderrouting.model.OrderModel;
import com.bestseller.services.orderrouting.rules.facts.FactNames;
import com.bestseller.services.orderrouting.service.CancellationReason;
import com.bestseller.services.orderrouting.service.OrderService;
import com.bestseller.services.orderrouting.utils.generator.OrderModelGenerator;
import com.bestseller.services.orderrouting.utils.generator.OrderPartRejectedGenerator;
import org.jeasy.rules.api.Facts;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;
import java.util.List;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.Silent.class)
public class CancelOrderPartActionTest {
    private Facts facts;
    private OrderModel order;
    private OrderPartRejected orderPartRejected;

    @Mock
    private OrderLineExtractor orderLineExtractor;

    @Mock
    private OrderService orderService;

    @Mock
    private OrderPartsCancelledConverter orderPartsCancelledConverter;

    @Mock
    private OrderPartsCancelledProducer orderPartsCancelledProducer;

    @InjectMocks
    private CancelOrderPartAction cancelOrderPartAction;

    @Captor
    private ArgumentCaptor<List<OrderLineQuantityModel>> orderLineCaptor;

    @Before
    public void setUp() {
        order = OrderModelGenerator.createTestOrderModel();
        order.setOrderLineQuantities(OrderModelGenerator.createThreeTestOrderLines(order));

        orderPartRejected = OrderPartRejectedGenerator.createFullOrderPartRejectedMessage();

        facts = new Facts();
        facts.put(FactNames.ORDER_MODEL_FACT, order);
        facts.put(FactNames.ORDER_PART_REJECTED_FACT, orderPartRejected);
    }

    @Test
    public void execute_ruleExecuted_orderLinesUpdated() {
        // arrange
        List<OrderLineQuantityModel> orderLinesInMessage = Collections.singletonList(order.getOrderLineQuantities().get(0));
        when(orderLineExtractor.extractOrderLinesFromMessage(order, orderPartRejected)).thenReturn(orderLinesInMessage);

        // act
        cancelOrderPartAction.execute(facts);

        // assert
        verify(orderService).saveOrderLines(orderLineCaptor.capture());

        OrderLineQuantityModel orderLineChanged = orderLineCaptor.getValue().get(0);
        assertThat("Cancel reason should be ITEM_NOT_AVAILABLE", orderLineChanged.getCancelReason(),
                is(CancellationReason.ITEM_NOT_AVAILABLE));
        assertThat("Status should be CANCELLED", orderLineChanged.getStatus(), is(OrderLineQuantityStatus.CANCELLED));
    }

    @Test
    public void execute_ruleExecuted_orderPartCancelledProduced() {
        // act
        cancelOrderPartAction.execute(facts);

       // assert
       OrderPartsCancelled orderPartsCancelled = verify(orderPartsCancelledConverter).convert(orderPartRejected);
       verify(orderPartsCancelledProducer).produce(orderPartsCancelled);
    }

    @Test(expected = IllegalArgumentException.class)
    public void execute_cancellationWithUnknownCancelReason_illegalArgumentExceptionIsThrown() {
        // arrange
        List<OrderLineQuantityModel> orderLinesInMessage = Collections.singletonList(order.getOrderLineQuantities().get(0));
        orderPartRejected.getOrderLines().get(0).setCancelReason("randomUnknownReason");
        when(orderLineExtractor.extractOrderLinesFromMessage(order, orderPartRejected)).thenReturn(orderLinesInMessage);

        // act
        cancelOrderPartAction.execute(facts);

        // assert - exception thrown
    }
}
