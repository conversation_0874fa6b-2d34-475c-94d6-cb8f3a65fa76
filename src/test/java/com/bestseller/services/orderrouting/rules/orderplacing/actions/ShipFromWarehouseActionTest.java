package com.bestseller.services.orderrouting.rules.orderplacing.actions;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailabilityRequest;
import com.bestseller.services.orderrouting.messaging.producer.Producer;
import com.bestseller.services.orderrouting.model.OrderModel;
import org.jeasy.rules.api.Facts;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.core.convert.converter.Converter;

import static com.bestseller.services.orderrouting.rules.facts.FactNames.ORDER_MODEL_FACT;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Unit tests for {@link ShipFromWarehouseAction}.
 */
@RunWith(MockitoJUnitRunner.class)
public class ShipFromWarehouseActionTest {
    @InjectMocks
    private ShipFromWarehouseAction shipFromWarehouseAction;

    @Mock
    private Converter<OrderModel, ItemAvailabilityRequest> itemAvailabilityRequestConverter;

    @Mock
    private Producer<ItemAvailabilityRequest> itemAvailabilityRequestProducer;

    @Test
    public void execute_validFacts_taskSchedulerWithFixedDelayInvokedWithCorrectParameters() {
        // arrange
        Facts facts = new Facts();
        OrderModel orderModel = new OrderModel();
        ItemAvailabilityRequest itemAvailabilityRequest = new ItemAvailabilityRequest();
        facts.put(ORDER_MODEL_FACT, orderModel);
        when(itemAvailabilityRequestConverter.convert(orderModel)).thenReturn(itemAvailabilityRequest);

        // act
        shipFromWarehouseAction.execute(facts);

        // assert
        verify(itemAvailabilityRequestProducer).produce(itemAvailabilityRequest);
    }
}
