package com.bestseller.services.orderrouting.rules.orderrouting.shipfromstore.conditions;

import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.model.OrderModel;
import com.bestseller.services.orderrouting.utils.generator.OrderModelGenerator;
import org.jeasy.rules.api.Condition;
import org.jeasy.rules.api.Facts;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

import static com.bestseller.services.orderrouting.rules.facts.FactNames.ORDER_MODEL_FACT;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

/**
 * Unit tests for {@link SfsSingleBrandOrderCondition}.
 */
@RunWith(MockitoJUnitRunner.class)
public class SfsSingleBrandOrderConditionTest {
    private static final String DIFFERENT_BRAND = "differentBrand";
    private static final String VERO_MODA = "vero-moda";
    private static final String JACK_JONES = "jack-jones";
    private static final String NAME_IT = "name-it";
    private static final String ONLY = "only";

    private Condition conditionSingleBrand;
    private OrderModel orderModel;
    private Facts facts;

    @Before
    public void setUp() {
        facts = new Facts();
        orderModel = OrderModelGenerator.createTestOrderModel();
        List<OrderLineQuantityModel> orderLines = OrderModelGenerator.createThreeTestOrderLines(orderModel);
        orderLines.forEach(ol -> ol.setBrand(JACK_JONES));
        orderModel.setOrderLineQuantities(orderLines);
        conditionSingleBrand = new SfsSingleBrandOrderCondition();
    }

    @Test
    public void when_singleBrandOrderIsGiven_returnTrue() {
        // arrange
        facts.put(ORDER_MODEL_FACT, orderModel);

        // act
        boolean isSingleBrandOrder = conditionSingleBrand.evaluate(facts);

        // assert
        assertTrue("Condition evaluates true", isSingleBrandOrder);
    }

    @Test
    public void when_multipleBrandsOrderIsGiven_returnFalse() {
        // arrange
        orderModel.getOrderLineQuantities().get(0).setBrand(DIFFERENT_BRAND);
        facts.put(ORDER_MODEL_FACT, orderModel);

        // act
        boolean isSingleBrandOrder = conditionSingleBrand.evaluate(facts);

        // assert
        assertFalse("Condition evaluates false", isSingleBrandOrder);
    }

    @Test(expected = IllegalArgumentException.class)
    public void when_noOrder_throwException() {
        // arrange - done in setup

        // act
        conditionSingleBrand.evaluate(facts);
    }

    @Test
    public void when_noBrand_returnFalse() {
        // arrange
        orderModel.setOrderLineQuantities(OrderModelGenerator.createThreeTestOrderLines(orderModel));
        facts.put(ORDER_MODEL_FACT, orderModel);

        // act
        boolean result = conditionSingleBrand.evaluate(facts);

        // assert
        assertFalse("Condition evaluates false", result);
    }

    @Test
    public void when_vmBrand_returnTrue() {
        // arrange
        setOrderLinesToBrand(VERO_MODA);
        facts.put(ORDER_MODEL_FACT, orderModel);

        // act
        boolean result = conditionSingleBrand.evaluate(facts);

        // assert
        assertTrue("Condition evaluates true", result);
    }

    @Test
    public void when_niBrand_returnTrue() {
        // arrange
        setOrderLinesToBrand(NAME_IT);
        facts.put(ORDER_MODEL_FACT, orderModel);

        // act
        boolean result = conditionSingleBrand.evaluate(facts);

        // assert
        assertTrue("Condition evaluates false", result);
    }

    @Test
    public void when_onBrand_returnTrue() {
        // arrange
        setOrderLinesToBrand(ONLY);
        facts.put(ORDER_MODEL_FACT, orderModel);

        // act
        boolean isSingleBrand = conditionSingleBrand.evaluate(facts);

        // assert
        assertTrue("Condition evaluates true", isSingleBrand);
    }

    private void setOrderLinesToBrand(String brand) {
        orderModel.getOrderLineQuantities().get(0).setBrand(brand);
        orderModel.getOrderLineQuantities().get(1).setBrand(brand);
        orderModel.getOrderLineQuantities().get(2).setBrand(brand);
    }
}
