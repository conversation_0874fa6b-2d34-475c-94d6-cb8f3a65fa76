package com.bestseller.services.orderrouting.rules.orderpartrejecting;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartRejected.OrderPartRejected;
import com.bestseller.services.orderrouting.metric.CounterOperation;
import com.bestseller.services.orderrouting.model.OrderModel;
import com.bestseller.services.orderrouting.rules.facts.FactNames;
import com.bestseller.services.orderrouting.rules.orderpartrejecting.actions.RerouteOrderPartAction;
import com.bestseller.services.orderrouting.utils.generator.OrderModelGenerator;
import com.bestseller.services.orderrouting.utils.generator.OrderPartRejectedGenerator;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.jeasy.rules.api.Condition;
import org.jeasy.rules.api.Facts;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.core.IsEqual.equalTo;
import static org.mockito.Mockito.when;

/**
 * Unit tests for {@link RerouteOrderPartAction}.
 */
@RunWith(MockitoJUnitRunner.Silent.class)
public class ReroutingOrderPartRuleTest {
    private static final String WAREHOUSE = "WAREHOUSE_1";

    private Facts facts;

    @Mock
    private RerouteOrderPartAction rerouteOrderPartAction;

    @Spy
    private MeterRegistry meterRegistry = new SimpleMeterRegistry();

    @Mock
    private Condition storeRejectionCondition;

    @InjectMocks
    private ReroutingOrderPartRule reroutingOrderPartRule;

    @Before
    public void setUp() {
        final OrderModel order = OrderModelGenerator.createTestOrderModel();
        order.setOrderLineQuantities(OrderModelGenerator.createThreeTestOrderLines(order));

        final OrderPartRejected orderPartRejected = OrderPartRejectedGenerator
                .createFullOrderPartRejectedMessage();

        facts = new Facts();
        facts.put(FactNames.ORDER_MODEL_FACT, order);
        facts.put(FactNames.ORDER_PART_REJECTED_FACT, orderPartRejected);

        when(storeRejectionCondition.evaluate(facts)).thenReturn(false);
        when(rerouteOrderPartAction.getFulfillmentNode(order)).thenReturn(WAREHOUSE);
    }

    @Test
    public void execute_ruleEvaluatesToTrue_parameterRecorded() {
        // act
        reroutingOrderPartRule.execute(facts);

        // assert
        final Tags tags = Tags.of(
                "destinationWarehouse", WAREHOUSE,
                "resubmitOrderPart", "true"
        );
        assertThat("Wrong count", meterRegistry.counter(CounterOperation.REROUTE_ORDER_PART, tags).count(), equalTo((double) 1));
    }
}
