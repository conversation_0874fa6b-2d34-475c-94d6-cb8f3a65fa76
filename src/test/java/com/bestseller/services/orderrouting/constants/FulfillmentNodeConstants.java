package com.bestseller.services.orderrouting.constants;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class FulfillmentNodeConstants {
    public static final String FULFILLMENT_CENTER_EAST = "INGRAM_MICRO";
    public static final String FULFILLMENT_CENTER_WEST = "INGRAM_MICRO_NL";
    public static final String DEFAULT_WAREHOUSE = FULFILLMENT_CENTER_WEST;

    public static final String SHIP_FROM_STORE_NL = "SHIP_FROM_STORE_NL";
    public static final String SHIP_FROM_STORE_NO = "SHIP_FROM_STORE_NO";
    public static final String SHIP_FROM_STORE_DE = "SHIP_FROM_STORE_DE";
}
