package com.bestseller.services.orderrouting.model;

import org.junit.Test;

import static org.hamcrest.Matchers.containsString;
import static org.junit.Assert.assertThat;

public class OrderLineQuantityModelTest {

    @Test
    public void toString_givenInstance_returnsShortString() {
        // arrange
        String ean = "*************";
        String quantity = "42";
        String statusString = "ROUTED";

        OrderLineQuantityModel model = new OrderLineQuantityModel();
        model.setEan(ean);
        model.setQuantity(Integer.parseInt(quantity));
        model.setStatus(OrderLineQuantityStatus.valueOf(statusString));

        // act
        String string = model.toString();

        // assert
        assertThat(string, containsString(ean));
        assertThat(string, containsString(quantity));
        assertThat(string, containsString(statusString));
    }

}
