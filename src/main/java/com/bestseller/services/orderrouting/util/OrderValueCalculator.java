package com.bestseller.services.orderrouting.util;

import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;

import java.math.BigDecimal;

import static java.math.BigDecimal.ZERO;

/**
 * Utility for computing order value from its order lines.
 */
@Service
@AllArgsConstructor
public class OrderValueCalculator {

    /**
     * It calculates total order cost(value) based on the retail price, discount value and quantity.
     *
     * @param orderLineQuantityModels valid order lines with price, discount and quantity set
     * @return value of the lines in total
     */
    public BigDecimal totalOrderCost(Collection<OrderLineQuantityModel> orderLineQuantityModels) {
        return orderLineQuantityModels.stream()
                .map(this::calculateOrderLineValue)
                .reduce(ZERO, BigDecimal::add);
    }

    private BigDecimal calculateOrderLineValue(OrderLineQuantityModel orderLineQuantityModel) {
        BigDecimal quantity = BigDecimal.valueOf(orderLineQuantityModel.getQuantity());
        BigDecimal basicValue = orderLineQuantityModel.getRetailPrice().multiply(quantity);
        BigDecimal discountAmount = orderLineQuantityModel.getDiscountValue().multiply(quantity);
        return basicValue.subtract(discountAmount);
    }
}
