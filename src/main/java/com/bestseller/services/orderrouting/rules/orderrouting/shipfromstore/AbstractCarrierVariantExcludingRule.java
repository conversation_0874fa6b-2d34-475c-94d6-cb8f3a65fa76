package com.bestseller.services.orderrouting.rules.orderrouting.shipfromstore;

import org.jeasy.rules.api.Condition;
import org.jeasy.rules.api.Facts;
import org.jeasy.rules.core.BasicRule;

/**
 * Rule to evaluate if order is not certain Carrier Variant.
 */
public abstract class AbstractCarrierVariantExcludingRule extends BasicRule {

    private static final String RULE_NAME = "Carrier Variant Excluding Rule";
    private static final String RULE_DESCRIPTION = "Rule to evaluate if order is not certain Carrier Variant";

    /**
     * Constructor for AbstractCarrierVariantExcludingRule.
     */
    public AbstractCarrierVariantExcludingRule() {
        super(RULE_NAME, RULE_DESCRIPTION);
    }

    /**
     * Condition to be evaluated.
     */
    @Override
    public boolean evaluate(final Facts facts) {
        return getCarrierVariantExcludingCondition().evaluate(facts);
    }

    /**
     * Returns the specific condition that triggers this rule.
     * @return the condition object
     */
    protected abstract Condition getCarrierVariantExcludingCondition();
}
