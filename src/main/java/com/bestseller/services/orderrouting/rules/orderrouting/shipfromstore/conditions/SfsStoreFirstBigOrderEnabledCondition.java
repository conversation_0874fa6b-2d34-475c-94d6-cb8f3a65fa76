package com.bestseller.services.orderrouting.rules.orderrouting.shipfromstore.conditions;

import com.bestseller.services.orderrouting.feature.toggles.ORSFeatures;
import lombok.AllArgsConstructor;
import org.jeasy.rules.api.Condition;
import org.jeasy.rules.api.Facts;
import org.springframework.stereotype.Component;

/**
 * Creates the specific ship from store store first big order condition.
 */
@Component
@AllArgsConstructor
public class SfsStoreFirstBigOrderEnabledCondition implements Condition {

    @Override
    public boolean evaluate(final Facts facts) {
        return ORSFeatures.SFS_STORE_FIRST_BIG_ORDERS_ENABLED.isActive();
    }

}
