package com.bestseller.services.orderrouting.rules.orderrouting.routeorder;

import com.bestseller.services.orderrouting.rules.flows.OrderRoutingFlow;
import org.jeasy.rules.api.Action;
import org.jeasy.rules.api.Condition;
import org.jeasy.rules.api.Facts;
import org.jeasy.rules.core.BasicRule;
import org.springframework.stereotype.Component;

/**
 * Rule to evaluate if order should be sent to warehouses.
 */
@Component
public class WarehouseRule extends BasicRule implements OrderRoutingFlow {

    private static final String WAREHOUSE_RULE = "Warehouse Rule";
    private static final String WAREHOUSE_DESCRIPTION = "Warehouse Rule Description";
    private static final int WAREHOUSE_RULE_PRIORITY = 4;

    private final Condition fulfillmentAssignedCondition;
    private final Action warehouseAction;

    /**
     * Constructor for WarehouseRule.
     * @param fulfillmentAssignedCondition fulfillmentAssignedCondition
     * @param warehouseAction warehouseAction
     */
    public WarehouseRule(Condition fulfillmentAssignedCondition, Action warehouseAction) {
        super(WAREHOUSE_RULE, WAREHOUSE_DESCRIPTION, WAREHOUSE_RULE_PRIORITY);
        this.fulfillmentAssignedCondition = fulfillmentAssignedCondition;
        this.warehouseAction = warehouseAction;
    }

    /**
     * Evaluates whether a fulfillment node is assigned to order.
     * @param facts
     * @return
     */
    @Override
    public boolean evaluate(Facts facts) {
        return fulfillmentAssignedCondition.evaluate(facts);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void execute(Facts facts) throws Exception {
        warehouseAction.execute(facts);
    }
}
