package com.bestseller.services.orderrouting.rules.orderrouting.shipfromstore.composites;

import org.jeasy.rules.api.Condition;
import org.jeasy.rules.api.Facts;
import org.jeasy.rules.core.BasicRule;
import org.springframework.stereotype.Component;

/**
 * Creates the specific ship from store rule for eligible for splitting orders.
 */
@Component
public class SfsSplitNotAllowedRule extends BasicRule {
    private static final String RULE_NAME = "Sfs no split allowed rule";
    private static final String RULE_DESCRIPTION = "Rule to check if no split is allowed";

    private Condition sfsStoreFirstNoSplitCondition;

    /**
     * Constructor for SfsSplitNotAllowedRule.
     * @param sfsStoreFirstNoSplitCondition sfsStoreFirstNoSplitCondition.
     */
    public SfsSplitNotAllowedRule(Condition sfsStoreFirstNoSplitCondition) {
        super(RULE_NAME, RULE_DESCRIPTION);
        this.sfsStoreFirstNoSplitCondition = sfsStoreFirstNoSplitCondition;
    }

    /**
     * Evaluates if order is allowed to be split.
     *
     * @param facts a bundle of facts needed for evaluation.
     * @return true if condition is met, false otherwise.
     */
    @Override
    public boolean evaluate(final Facts facts) {
        return sfsStoreFirstNoSplitCondition.evaluate(facts);
    }
}
