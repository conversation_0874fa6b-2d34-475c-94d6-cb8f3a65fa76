package com.bestseller.services.orderrouting.rules.orderrouting.shipfromstore.composites;

import lombok.AllArgsConstructor;
import org.jeasy.rules.api.Condition;
import org.jeasy.rules.api.Facts;
import org.jeasy.rules.core.BasicRule;
import org.springframework.stereotype.Component;

/**
 * Creates the specific rule for countries eligible for Ship From Store Solution.
 */
@Component
@AllArgsConstructor
public class SfsBigOrderTypeEnabledRule extends BasicRule {

    private Condition sfsFirstCountryCondition;
    private Condition sfsStoreFirstBigOrderEnabledCondition;
    private Condition sfsSoldOutCountryCondition;

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean evaluate(final Facts facts) {
        return sfsStoreFirstBigOrderEnabled(facts) || sfsSoldOutCountryCondition.evaluate(facts);
    }

    private boolean sfsStoreFirstBigOrderEnabled(final Facts facts) {
        return sfsFirstCountryCondition.evaluate(facts) && sfsStoreFirstBigOrderEnabledCondition.evaluate(facts);
    }
}
