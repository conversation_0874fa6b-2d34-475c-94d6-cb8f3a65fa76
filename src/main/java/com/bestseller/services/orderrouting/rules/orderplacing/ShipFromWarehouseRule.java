package com.bestseller.services.orderrouting.rules.orderplacing;

import com.bestseller.services.orderrouting.rules.flows.OrderPlacingFlow;
import com.bestseller.services.orderrouting.rules.listeners.BreakChain;
import org.jeasy.rules.api.Action;
import org.jeasy.rules.api.Facts;
import org.jeasy.rules.core.BasicRule;
import org.springframework.stereotype.Component;

/**
 * Rule for routing of orders to warehouses.
 */
@Component
@BreakChain
public class ShipFromWarehouseRule extends BasicRule implements OrderPlacingFlow {
    private static final String RULE_NAME = "Ship From Warehouse Rule";
    private static final String RULE_DESCRIPTION = "Sends stock availability request in order to decide which warehouse is"
            + " best suited to fulfill this order";
    private final Action shipFromWarehouseAction;

    /**
     * Constructor for ShipFromWarehouseRule.
     * @param shipFromWarehouseAction shipFromWarehouseAction.
     */
    public ShipFromWarehouseRule(Action shipFromWarehouseAction) {
        super(RULE_NAME, RULE_DESCRIPTION);
        this.shipFromWarehouseAction = shipFromWarehouseAction;
    }

    /**
     * Evaluates whether the order is suited for fulfillment from warehouses. By default always true.
     * @param facts
     * @return
     */
    @Override
    public boolean evaluate(final Facts facts) {
        return true;
    }

    /**
     * Sends stock availability request to the Inventory Management System.
     * @param facts
     * @throws Exception
     */
    @Override
    public void execute(final Facts facts) throws Exception {
        shipFromWarehouseAction.execute(facts);
    }
}
