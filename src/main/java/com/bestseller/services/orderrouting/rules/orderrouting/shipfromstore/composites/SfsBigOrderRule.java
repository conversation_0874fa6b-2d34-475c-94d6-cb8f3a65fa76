package com.bestseller.services.orderrouting.rules.orderrouting.shipfromstore.composites;

import org.jeasy.rules.api.Condition;
import org.jeasy.rules.api.Facts;
import org.jeasy.rules.core.BasicRule;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Creates the specific rule to check the items on the order to determine if the order is big enough for splitting.
 */
@Component
public class SfsBigOrderRule extends BasicRule {

    private static final String RULE_NAME = "Big order Rule";
    private static final String RULE_DESCRIPTION = "Rule to evaluate if order big order";

    private final Condition sfsBigOrderCondition;

    /**
     * Constructor for SfsBigOrderRule.
     * @param sfsBigOrderCondition sfs big order condition bean
     * @param ruleName name of the rule
     * @param ruleDescription description of the rule
     */
    public SfsBigOrderRule(Condition sfsBigOrderCondition, String ruleName, String ruleDescription) {
        super(ruleName, ruleDescription);
        this.sfsBigOrderCondition = sfsBigOrderCondition;
    }

    /**
     * Constructor for SfsBigOrderRule.
     * @param sfsBigOrderCondition sfs big order condition bean
     */
    @Autowired
    public SfsBigOrderRule(Condition sfsBigOrderCondition) {
        super(RULE_NAME, RULE_DESCRIPTION);
        this.sfsBigOrderCondition = sfsBigOrderCondition;
    }

    /**
     * Evaluates if an order is big enough to activate splitting.
     *
     * @param facts a bundle of facts needed for evaluation.
     * @return true if order is big enough to split.
     */
    @Override
    public boolean evaluate(final Facts facts) {
        return sfsBigOrderCondition.evaluate(facts);
    }
}
