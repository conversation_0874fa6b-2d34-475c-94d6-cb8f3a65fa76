package com.bestseller.services.orderrouting.rules.orderrouting.shipfromstore;

import org.jeasy.rules.api.Condition;
import org.jeasy.rules.api.Facts;
import org.jeasy.rules.core.BasicRule;

/**
 * Rule to evaluate if order is not store rejected by OWC.
 */
public abstract class AbstractOrderNotRejectedRule extends BasicRule {

    private static final String RULE_NAME = "Order not rejected rule";
    private static final String RULE_DESCRIPTION = "Rule to evaluate if order is not rejected from store";

    /**
     * Constructor for AbstractOrderNotRejectedRule.
     */
    public AbstractOrderNotRejectedRule() {
        super(RULE_NAME, RULE_DESCRIPTION);
    }

    /**
     * Evaluates if order is not rejected by OWC.
     *
     * @param facts a bundle of facts needed for evaluation.
     * @return true if condition is met, false otherwise.
     */
    @Override
    public boolean evaluate(final Facts facts) {
        return getOrderNotRejectedCondition().evaluate(facts);
    }

    /**
     * Returns the specific condition that triggers this rule.
     * @return the condition object
     */
    protected abstract Condition getOrderNotRejectedCondition();
}
