package com.bestseller.services.orderrouting.rules.orderpartrejecting.conditions;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartRejected.OrderPartRejected;
import com.bestseller.services.orderrouting.configuration.SfsConfig;
import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.model.OrderModel;
import com.bestseller.services.orderrouting.rules.facts.FactNames;
import lombok.AllArgsConstructor;
import org.jeasy.rules.api.Condition;
import org.jeasy.rules.api.Facts;
import org.springframework.stereotype.Component;

/**
 * Condition to evaluate if the order can be rerouted because the order was a STORES_FIRST order.
 */
@Component
@AllArgsConstructor
public class RerouteCondition implements Condition {
    private SfsConfig sfsConfig;

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean evaluate(final Facts facts) {
        OrderPartRejected orderPartRejected = facts.get(FactNames.ORDER_PART_REJECTED_FACT);

        if (!orderPartRejected.getWarehouse().startsWith("SHIP_FROM_STORE")) {
            return false;
        }

        OrderModel orderModel = facts.get(FactNames.ORDER_MODEL_FACT);

        return orderModel.getOrderLineQuantities()
                .stream()
                .findFirst()
                .map(OrderLineQuantityModel::getTotalOrderParts)
                .map(totalOrderParts -> shouldReroute(orderModel.getShippingAddress().getCountry(), totalOrderParts))
                .orElse(false);

    }

    /**
     * The order part should be rerouted if it is a STORES_FIRST country. An extra check is made that there is only
     * one order part, which should be by default as STORES_FIRST orders route the whole order to OWC.
     * @param orderCountry the country of the order.
     * @param totalOrderParts the amount of order parts of this order.
     * @return returns if it should route or not.
     */
    private boolean shouldReroute(String orderCountry, Integer totalOrderParts) {
        return sfsConfig.getEnabledCountriesByType(SfsConfig.STORES_FIRST).contains(orderCountry) && totalOrderParts != null && totalOrderParts == 1;
    }
}
