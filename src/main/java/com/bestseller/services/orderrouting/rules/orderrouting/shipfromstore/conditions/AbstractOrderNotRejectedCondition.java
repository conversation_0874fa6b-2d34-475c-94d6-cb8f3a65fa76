package com.bestseller.services.orderrouting.rules.orderrouting.shipfromstore.conditions;

import lombok.AllArgsConstructor;
import org.jeasy.rules.api.Condition;
import org.jeasy.rules.api.Facts;

import static com.bestseller.services.orderrouting.rules.facts.FactNames.ORDER_PART_REJECTED_FACT;

/**
 * Evaluates if order is not rejected.
 */
@AllArgsConstructor
public abstract class AbstractOrderNotRejectedCondition implements Condition {

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean evaluate(final Facts facts) {
        return facts.get(ORDER_PART_REJECTED_FACT) == null;
    }
}
