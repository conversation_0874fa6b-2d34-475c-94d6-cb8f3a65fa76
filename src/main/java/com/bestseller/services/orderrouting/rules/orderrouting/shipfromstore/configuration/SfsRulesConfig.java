package com.bestseller.services.orderrouting.rules.orderrouting.shipfromstore.configuration;

import lombok.AllArgsConstructor;
import org.jeasy.rules.api.Rule;
import org.jeasy.rules.support.UnitRuleGroup;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Collection of rules for checking SFS order eligibility.
 */
@Configuration
@AllArgsConstructor
public class SfsRulesConfig {

    private Rule sfsFirstCountryRule;
    private Rule sfsSoldOutCountryRule;
    private Rule sfsCarrierVariantExcludingRule;
    private Rule sfsSingleBrandOrderRule;
    private Rule sfsOrderNotRejectedRule;
    private Rule sfsSplitAllowedRule;
    private Rule sfsSplitNotAllowedRule;
    private Rule fulfillmentAssignedRule;
    private Rule notTradebyteOrderRule;
    private Rule standardShippingOrderRule;
    private Rule sfsBigOrderTypeEnabledRule;
    private Rule sfsBigOrderRule;
    private Rule sfsNotBigOrderRule;
    private Rule sfsHermesPickupNotAllowedRule;
    private Rule notInStoreOrderingOrderRule;

    /**
     * Containing rules for SFS First Split Not Allowed.
     * @return a rule with all the components to be checked.
     */
    @Bean
    public UnitRuleGroup sfsFirstSplitNotAllowedCompositeRule() {
        UnitRuleGroup compositeRule = createBaseRule();
        compositeRule.addRule(sfsFirstCountryRule);
        compositeRule.addRule(sfsSingleBrandOrderRule);
        compositeRule.addRule(sfsSplitNotAllowedRule);
        compositeRule.addRule(sfsNotBigOrderRule);
        return compositeRule;
    }

    /**
     * Containing rules for SFS First Split Allowed.
     * @return a rule with all the components to be checked.
     */
    @Bean
    public UnitRuleGroup sfsFirstSplitAllowedCompositeRule() {
        UnitRuleGroup compositeRule = createBaseRule();
        compositeRule.addRule(sfsFirstCountryRule);
        compositeRule.addRule(sfsSingleBrandOrderRule);
        compositeRule.addRule(sfsSplitAllowedRule);
        compositeRule.addRule(sfsNotBigOrderRule);
        return compositeRule;
    }

    /**
     * Containing rules for SFS Sold Out Solution.
     * @return a rule with all the components to be checked.
     */
    @Bean
    public UnitRuleGroup sfsSoldOutCompositeRule() {
        UnitRuleGroup compositeRule = createBaseRule();
        compositeRule.addRule(sfsSoldOutCountryRule);
        compositeRule.addRule(sfsNotBigOrderRule);
        return compositeRule;
    }

    /**
     * Containing rules for SFS Big order split solution.
     * @return a rule with all the components to be checked.
     */
    @Bean
    public UnitRuleGroup sfsBigOrderSplitCompositeRule() {
        UnitRuleGroup compositeRule = createBaseRule();
        compositeRule.addRule(sfsBigOrderTypeEnabledRule);
        compositeRule.addRule(sfsBigOrderRule);
        return compositeRule;
    }

    private UnitRuleGroup createBaseRule() {
        UnitRuleGroup compositeRule = new UnitRuleGroup();
        compositeRule.addRule(sfsCarrierVariantExcludingRule);
        compositeRule.addRule(sfsOrderNotRejectedRule);
        compositeRule.addRule(fulfillmentAssignedRule);
        compositeRule.addRule(notTradebyteOrderRule);
        compositeRule.addRule(standardShippingOrderRule);
        compositeRule.addRule(sfsHermesPickupNotAllowedRule);
        compositeRule.addRule(notInStoreOrderingOrderRule);

        return compositeRule;
    }
}
