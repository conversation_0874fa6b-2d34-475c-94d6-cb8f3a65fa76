package com.bestseller.services.orderrouting.rules.orderrouting.shipfromstore;

import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.model.OrderModel;
import com.bestseller.services.orderrouting.rules.facts.FactNames;
import com.bestseller.services.orderrouting.rules.flows.OrderRoutingFlow;
import org.jeasy.rules.api.Action;
import org.jeasy.rules.api.Facts;
import org.jeasy.rules.api.Rule;
import org.jeasy.rules.core.BasicRule;
import org.springframework.stereotype.Component;

import static com.bestseller.services.orderrouting.rules.facts.FactNames.ORDER_MODEL_FACT;
import static com.bestseller.services.orderrouting.service.CancellationReason.STORE_REJECTION;

/**
 * Ship From Store Big Order Routing Rule.
 */
@Component
public class ShipFromStoreBigOrderSplitRule extends BasicRule implements OrderRoutingFlow {
    private static final String RULE_NAME = "Ship From Store Big Order Rule";
    private static final String RULE_DESCRIPTION = "Rule for splitting big orders orders for SFS scenarios";
    private static final int RULE_PRIORITY = 3;
    private Rule sfsBigOrderSplitCompositeRule;
    private Action shipFromStoreBigOrderAction;

    /**
     * Constructor for ShipFromStoreBigOrderSplitRule.
     *
     * @param sfsBigOrderSplitCompositeRule sfsBigOrderSplitCompositeRule
     * @param shipFromStoreBigOrderAction shipFromStoreBigOrderAction
     */
    public ShipFromStoreBigOrderSplitRule(Rule sfsBigOrderSplitCompositeRule, Action shipFromStoreBigOrderAction) {
        super(RULE_NAME, RULE_DESCRIPTION, RULE_PRIORITY);
        this.sfsBigOrderSplitCompositeRule = sfsBigOrderSplitCompositeRule;
        this.shipFromStoreBigOrderAction = shipFromStoreBigOrderAction;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int getPriority() {
        return RULE_PRIORITY;
    }

    /**
     * Returns true if order is placed in one of the ${country} properties, carrier variant is not one of the
     * ${carrier.variants} properties and order is single brand for one of the ${rule.specific.brands} properties.
     *
     * @param facts a bundle of facts needed for evaluation.
     *
     * @return true if condition is met, false otherwise.
     */
    @Override
    public boolean evaluate(Facts facts) {
        OrderModel orderModel = facts.get(ORDER_MODEL_FACT);
        orderModel.getOrderLineQuantities().stream()
                  .map(OrderLineQuantityModel::getCancelReason)
                  .filter(STORE_REJECTION::equals).findAny()
                  .ifPresent(order -> facts.put(FactNames.ORDER_PART_REJECTED_FACT, true));

        return sfsBigOrderSplitCompositeRule.evaluate(facts);
    }

    /**
     * Routes the order for fulfillment from stores.
     *
     * @param facts a bundle of facts needed for execution.
     *
     * @throws Exception if some error occurred during execution.
     */
    @Override
    public void execute(Facts facts) throws Exception {
        shipFromStoreBigOrderAction.execute(facts);
    }
}
