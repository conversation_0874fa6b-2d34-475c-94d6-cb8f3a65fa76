package com.bestseller.services.orderrouting.rules.orderrouting.shipfromstore;

import com.bestseller.services.orderrouting.rules.flows.OrderRoutingFlow;
import org.jeasy.rules.api.Action;
import org.jeasy.rules.api.Facts;
import org.jeasy.rules.api.Rule;
import org.jeasy.rules.core.BasicRule;
import org.springframework.stereotype.Component;

/**
 * Ship From Store Sold Out Routing Rule.
 */
@Component
public class ShipFromStoreFirstSplitRule extends BasicRule implements OrderRoutingFlow {
    private static final String RULE_NAME = "Ship From Store First Rule Split Allowed";
    private static final String RULE_DESCRIPTION = "Rule for routing orders for fulfillment from stores in SFS First "
            + "scenario allowing split orders";
    private static final int RULE_PRIORITY = 3;
    private final Rule sfsFirstSplitAllowedCompositeRule;
    private final Action shipFromStoreSplitAction;

    /**
     * Constructor for ShipFromStoreFirstSplitRule.
     * @param sfsFirstSplitAllowedCompositeRule sfsFirstSplitAllowedCompositeRule
     * @param shipFromStoreSplitAction shipFromStoreSplitAction
     */
    public ShipFromStoreFirstSplitRule(Rule sfsFirstSplitAllowedCompositeRule, Action shipFromStoreSplitAction) {
        super(RULE_NAME, RULE_DESCRIPTION, RULE_PRIORITY);
        this.sfsFirstSplitAllowedCompositeRule = sfsFirstSplitAllowedCompositeRule;
        this.shipFromStoreSplitAction = shipFromStoreSplitAction;
    }

    /**
     * Returns true if order is placed in one of the ${country} properties, carrier variant is not one of the ${carrier.variants}
     * properties and order is single brand for one of the ${rule.specific.brands} properties and SFS Store first
     * split is allowed.
     *
     * @param facts a bundle of facts needed for evaluation.
     * @return true if condition is met, false otherwise.
     */
    @Override
    public boolean evaluate(Facts facts) {
        return sfsFirstSplitAllowedCompositeRule.evaluate(facts);
    }

    /**
     * Routes the order for fulfillment from stores.
     *
     * @param facts a bundle of facts needed for execution.
     * @throws Exception if some error occurred during execution.
     */
    @Override
    public void execute(Facts facts) throws Exception {
        shipFromStoreSplitAction.execute(facts);
    }
}
