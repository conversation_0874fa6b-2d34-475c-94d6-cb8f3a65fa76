package com.bestseller.services.orderrouting.rules.orderplacing.actions;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderForFulfillment.OrderForFulfillment;
import com.bestseller.services.orderrouting.model.OrderModel;
import com.bestseller.services.orderrouting.service.OrderService;
import org.springframework.context.ApplicationEventPublisher;

/**
 * Route all orders to the same warehouse.
 */
public class ConstantNodeFulfillmentAction extends FulfillmentAction {

    private final String fulfillmentNode;

    /**
     * Explicit constructor due to the limitation of Lombok.
     *
     * @param fulfillmentNode warehouse to route all orders to
     */
    public ConstantNodeFulfillmentAction(OrderService orderService,
                                         ApplicationEventPublisher eventPublisher,
                                         String fulfillmentNode) {
        super(orderService, eventPublisher);
        this.fulfillmentNode = fulfillmentNode;
    }

    /**
     * {@inheritDoc}
     *
     * @param orderForFulfillment
     * @param orderModel
     */
    @Override
    protected String getFulfillmentNode(OrderForFulfillment orderForFulfillment, OrderModel orderModel) {
        return fulfillmentNode;
    }
}
