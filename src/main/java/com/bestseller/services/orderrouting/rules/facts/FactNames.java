package com.bestseller.services.orderrouting.rules.facts;

/**
 * Fact names constants.
 */
@SuppressWarnings("PMD.ClassNamingConventions")
public final class FactNames {

    /**
     * Order model fact.
     */
    public static final String ORDER_MODEL_FACT = "orderModel";

    /**
     * Item availability response fact.
     */
    public static final String ITEM_AVAILABILITY_RESPONSE_FACT = "itemAvailabilityResponse";

    /**
     * Order for fulfillment fact.
     */
    public static final String ORDER_FOR_FULFILLMENT_FACT = "orderForFulfillment";

    /**
     * Store rejection fact.
     */
    public static final String ORDER_PART_REJECTED_FACT = "orderPartRejected";

    /**
     * Fulfillment information has been evaluated and assigned to order lines.
     */
    public static final String FULFILLMENT_INFORMATION_ASSIGNED_FACT = "fulfillmentInformationAssigned";

    /**
     * Indicates that the time needed for fulfillment would breach the SLA.
     */
    public static final String FULFILLMENT_SLA_BREACHED_FACT = "fulfillmentSlaBreached";

    private FactNames() {
    }
}
