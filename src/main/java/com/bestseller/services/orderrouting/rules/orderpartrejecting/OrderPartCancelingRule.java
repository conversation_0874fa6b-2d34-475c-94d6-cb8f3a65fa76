package com.bestseller.services.orderrouting.rules.orderpartrejecting;

import com.bestseller.services.orderrouting.metric.CounterOperation;
import com.bestseller.services.orderrouting.rules.flows.OrderPartRejectingFlow;
import com.bestseller.services.orderrouting.rules.listeners.BreakChain;
import com.bestseller.services.orderrouting.rules.orderpartrejecting.actions.CancelOrderPartAction;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tag;
import org.jeasy.rules.api.Condition;
import org.jeasy.rules.api.Facts;
import org.jeasy.rules.core.BasicRule;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Store Rejection Rule.
 */
@Component
@BreakChain
public class OrderPartCancelingRule extends BasicRule implements OrderPartRejectingFlow {
    private static final String RULE_NAME = "Order Part Cancelled rule";
    private static final String RULE_DESCRIPTION = "Cancelled the order when it is not possible to route this part to the default warehouse";

    private final CancelOrderPartAction cancelOrderPartAction;
    private final MeterRegistry meterRegistry;
    private final Condition itemNotAvailableCondition;
    private final Condition rerouteCondition;

    /**
     * Constructor for OrderPartCancelingRule.
     * @param cancelOrderPartAction cancelOrderPartAction
     * @param meterRegistry meterRegistry
     * @param itemNotAvailableCondition itemNotAvailableCondition
     * @param rerouteCondition rerouteCondition
     */
    public OrderPartCancelingRule(CancelOrderPartAction cancelOrderPartAction,
                                  MeterRegistry meterRegistry,
                                  Condition itemNotAvailableCondition,
                                  Condition rerouteCondition) {
        super(RULE_NAME, RULE_DESCRIPTION);
        this.cancelOrderPartAction = cancelOrderPartAction;
        this.meterRegistry = meterRegistry;
        this.itemNotAvailableCondition = itemNotAvailableCondition;
        this.rerouteCondition = rerouteCondition;
    }

    /**
     * Evaluates whether the order is not able to send to the warehouse.
     * @param facts a bundle of facts needed for evaluation.
     * @return true if condition is met, false otherwise.
     */
    @Override
    public boolean evaluate(Facts facts) {
        boolean allItemsAreNotAvailable = itemNotAvailableCondition.evaluate(facts);
        boolean orderShouldBeRerouted = rerouteCondition.evaluate(facts);

        return allItemsAreNotAvailable && !orderShouldBeRerouted;
    }

    /**
     * Routes the order for fulfillment from primary warehouse and resets the order lines to the PLACED state.
     * @param facts a bundle of facts needed for execution.
     */
    @Override
    public void execute(Facts facts) {
        var tags = List.of(Tag.of("resubmitOrderPart", Boolean.FALSE.toString()));
        meterRegistry.counter(CounterOperation.REROUTE_ORDER_PART, tags).increment();

        cancelOrderPartAction.execute(facts);
    }
}
