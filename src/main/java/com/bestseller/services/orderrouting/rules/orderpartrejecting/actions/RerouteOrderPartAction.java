package com.bestseller.services.orderrouting.rules.orderpartrejecting.actions;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderForFulfillment.OrderForFulfillment;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartRejected.OrderPartRejected;
import com.bestseller.services.orderrouting.configuration.Warehouses;
import com.bestseller.services.orderrouting.converter.OrderLineExtractor;
import com.bestseller.services.orderrouting.feature.toggles.ORSFeatures;
import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.model.OrderLineQuantityStatus;
import com.bestseller.services.orderrouting.model.OrderModel;
import com.bestseller.services.orderrouting.rules.facts.FactNames;
import com.bestseller.services.orderrouting.rules.orderplacing.actions.FulfillmentAction;
import com.bestseller.services.orderrouting.service.CancellationReason;
import com.bestseller.services.orderrouting.service.OrderService;
import org.jeasy.rules.api.Facts;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Reroutes the order to the default warehouse.
 */
@Component
public class RerouteOrderPartAction extends FulfillmentAction {

    private final Warehouses warehouses;
    private final OrderService orderService;
    private final OrderLineExtractor orderLineExtractor;

    /**
     * Explicit constructor due to the limitation of Lombok.
     */
    public RerouteOrderPartAction(ApplicationEventPublisher eventPublisher,
                                  OrderService orderService,
                                  OrderLineExtractor orderLineExtractor,
                                  Warehouses warehouses) {
        super(orderService, eventPublisher);

        this.warehouses = warehouses;
        this.orderService = orderService;
        this.orderLineExtractor = orderLineExtractor;
    }

    @Override
    protected String getFulfillmentNode(OrderForFulfillment orderForFulfillment, OrderModel orderModel) {
        return getFulfillmentNode(orderModel);
    }

    /**
     * Where would an order been rerouted.
     * @param orderModel
     * @return warehouse
     */
    public String getFulfillmentNode(OrderModel orderModel) {
        if (ORSFeatures.PRIORITIZE_FCW_OVER_FCE.isActive() && isComingFromJackJonesSite(orderModel)) {
            return warehouses.getFulfillmentCenterWest();
        }
        return warehouses.getFulfillmentCenterEast();
    }

    private boolean isComingFromJackJonesSite(OrderModel orderModel) {
        return "jj".equals(orderModel.getCheckout());
    }

    @Override
    public void execute(Facts facts) {
        OrderModel orderModel = facts.get(FactNames.ORDER_MODEL_FACT);
        OrderPartRejected orderPartRejected = facts.get(FactNames.ORDER_PART_REJECTED_FACT);

        updateOrderLines(orderModel, orderPartRejected);

        super.execute(facts);
    }

    private void updateOrderLines(OrderModel orderModel, OrderPartRejected orderPartRejected) {
        List<OrderLineQuantityModel> orderLinesInMessage = orderLineExtractor.extractOrderLinesFromMessage(orderModel, orderPartRejected);
        for (OrderLineQuantityModel orderLine : orderLinesInMessage) {
            orderLine.setCancelReason(CancellationReason.STORE_REJECTION);
            orderLine.setStatus(OrderLineQuantityStatus.PLACED);
            orderLine.setFulfillmentNode(null);
            orderLine.setOrderPartNumber(null);
            orderLine.setOrderId(orderModel.getOrderId());
        }

        orderService.saveOrderLines(orderLinesInMessage);
    }
}
