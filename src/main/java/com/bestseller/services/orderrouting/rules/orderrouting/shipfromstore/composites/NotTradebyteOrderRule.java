package com.bestseller.services.orderrouting.rules.orderrouting.shipfromstore.composites;

import org.jeasy.rules.api.Condition;
import org.jeasy.rules.api.Facts;
import org.jeasy.rules.core.BasicRule;
import org.springframework.stereotype.Component;

/**
 * Rule to evaluate if an order is coming from tradebyte.
 */
@Component
public class NotTradebyteOrderRule extends BasicRule {

    private static final String RULE_NAME = "Not tradebyte order Rule";
    private static final String RULE_DESCRIPTION = "Rule to evaluate if order is not a tradebyte order";

    private final Condition tradebyteOrderCondition;

    /**
     * Constructor for NotTradebyteOrderRule.
     * @param tradebyteOrderCondition tradebyteOrderCondition
     */
    public NotTradebyteOrderRule(Condition tradebyteOrderCondition) {
        super(RULE_NAME, RULE_DESCRIPTION);
        this.tradebyteOrderCondition = tradebyteOrderCondition;
    }

    /**
     * Evaluates if an order is not from tradebyte.
     *
     * @param facts a bundle of facts needed for evaluation.
     * @return true if order is not from tradebyte.
     */
    @Override
    public boolean evaluate(final Facts facts) {
        return !tradebyteOrderCondition.evaluate(facts);
    }
}
