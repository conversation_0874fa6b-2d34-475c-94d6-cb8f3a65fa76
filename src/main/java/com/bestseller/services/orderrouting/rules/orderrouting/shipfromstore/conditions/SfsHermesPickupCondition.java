package com.bestseller.services.orderrouting.rules.orderrouting.shipfromstore.conditions;

import com.bestseller.services.orderrouting.model.OrderModel;
import lombok.AllArgsConstructor;
import org.jeasy.rules.api.Condition;
import org.jeasy.rules.api.Facts;
import org.springframework.stereotype.Component;

import static com.bestseller.services.orderrouting.rules.facts.FactNames.ORDER_MODEL_FACT;
import static java.util.Objects.requireNonNull;

/**
 * Creates the specific condition when an order carrier is HERMES and variant is PICKUP.
 */
@Component
@AllArgsConstructor
public class SfsHermesPickupCondition implements Condition {
    /**
     * {@inheritDoc}
     */
    @Override
    public boolean evaluate(Facts facts) {
        final OrderModel orderModel = requireNonNull(facts.get(ORDER_MODEL_FACT), "Missing fact");

        return orderModel.getCarrier().equalsIgnoreCase("HERMES") && orderModel.getCarrierVariant().equalsIgnoreCase("PICKUP");
    }
}
