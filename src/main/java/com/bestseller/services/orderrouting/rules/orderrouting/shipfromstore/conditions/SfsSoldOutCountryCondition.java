package com.bestseller.services.orderrouting.rules.orderrouting.shipfromstore.conditions;

import com.bestseller.services.orderrouting.configuration.SfsConfig;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Set;

/**
 * Creates the specific condition for countries eligible for Ship From Store Sold Out Solution.
 */
@Component
@AllArgsConstructor
public class SfsSoldOutCountryCondition extends AbstractOrderPlacedInCountryCondition {

    private SfsConfig sfsConfig;

    /**
     * {@inheritDoc}
     */
    @Override
    protected Set<String> getCountries() {
        return sfsConfig.getEnabledCountriesByType(SfsConfig.SOLD_OUT);
    }
}
