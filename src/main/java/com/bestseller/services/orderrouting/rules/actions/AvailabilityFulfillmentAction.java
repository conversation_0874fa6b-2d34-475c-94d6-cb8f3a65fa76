package com.bestseller.services.orderrouting.rules.actions;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.Item;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailability;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailabilityResponse;
import com.bestseller.services.orderrouting.configuration.Warehouses;
import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.model.OrderModel;
import com.bestseller.services.orderrouting.service.OrderService;
import com.bestseller.services.orderrouting.strategy.Fulfillment;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jeasy.rules.api.Action;
import org.jeasy.rules.api.Facts;

import java.util.List;
import java.util.Map;

import static com.bestseller.services.orderrouting.rules.facts.FactNames.FULFILLMENT_INFORMATION_ASSIGNED_FACT;
import static com.bestseller.services.orderrouting.rules.facts.FactNames.ITEM_AVAILABILITY_RESPONSE_FACT;
import static com.bestseller.services.orderrouting.rules.facts.FactNames.ORDER_MODEL_FACT;
import static java.util.stream.Collectors.toMap;

/**
 * Assign fulfillment nodes based on availability information.
 */
@AllArgsConstructor
@Slf4j
public class AvailabilityFulfillmentAction implements Action {
    private Fulfillment fulfillment;
    private OrderService orderService;
    private Warehouses warehouses;

    /**
     * {@inheritDoc}
     */
    @Override
    public void execute(final Facts facts) {
        // get information from facts
        OrderModel orderModel = facts.get(ORDER_MODEL_FACT);
        log.info("Evaluating node information for orderLineModels: {}", orderModel.getOrderLineQuantities());

        ItemAvailabilityResponse itemAvailabilityResponse = facts.get(ITEM_AVAILABILITY_RESPONSE_FACT);
        Map<String, List<ItemAvailability>> eanAvailability = itemAvailabilityResponse.getItems()
                .stream()
                .collect(toMap(Item::getEan, Item::getItemAvailability));

        // routing decision
        Map<String, List<List<OrderLineQuantityModel>>> partAssignment = fulfillment.allocateOrderParts(orderModel, eanAvailability);
        log.info("Order parts allocated: {}", partAssignment);

        if (partAssignment.isEmpty()) {
            return;
        }

        // apply routing decision
        orderService.assignOrderPartFulfillmentNodes(orderModel, partAssignment);
        orderService.saveOrderLines(orderModel.getOrderLineQuantities());

        facts.put(FULFILLMENT_INFORMATION_ASSIGNED_FACT, true);
    }
}
