package com.bestseller.services.orderrouting.rules.orderrouting.shipfromstore.composites;

import com.bestseller.services.orderrouting.rules.orderrouting.shipfromstore.AbstractOrderNotRejectedRule;
import lombok.AllArgsConstructor;
import org.jeasy.rules.api.Condition;
import org.springframework.stereotype.Component;

/**
 * Rule to evaluate if order is not store rejected.
 */
@Component
@AllArgsConstructor
public class SfsOrderNotRejectedRule extends AbstractOrderNotRejectedRule {

    private Condition sfsOrderNotRejectedCondition;

    /**
     * {@inheritDoc}
     */
    @Override
    protected Condition getOrderNotRejectedCondition() {
        return sfsOrderNotRejectedCondition;
    }

}
