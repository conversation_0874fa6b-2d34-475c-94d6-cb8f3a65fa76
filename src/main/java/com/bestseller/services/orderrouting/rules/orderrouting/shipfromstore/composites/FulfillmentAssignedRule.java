package com.bestseller.services.orderrouting.rules.orderrouting.shipfromstore.composites;

import org.jeasy.rules.api.Condition;
import org.jeasy.rules.api.Facts;
import org.jeasy.rules.core.BasicRule;
import org.springframework.stereotype.Component;

/**
 * Rule to evaluate if fulfillment node is assigned.
 */
@Component
public class FulfillmentAssignedRule extends BasicRule {

    private static final String RULE_NAME = "Fulfillment Assigned Rule";
    private static final String RULE_DESCRIPTION = "Rule to evaluate if fulfillment node is assigned";

    private final Condition fulfillmentAssignedCondition;

    /**
     * Constructor for FulfillmentAssignedRule.
     * @param fulfillmentAssignedCondition fulfillmentAssignedCondition
     */
    public FulfillmentAssignedRule(Condition fulfillmentAssignedCondition) {
        super(RULE_NAME, RULE_DESCRIPTION);
        this.fulfillmentAssignedCondition = fulfillmentAssignedCondition;
    }

    /**
     * Evaluates if fulfillment node is assigned.
     *
     * @param facts a bundle of facts needed for evaluation.
     * @return true if fulfillment node is assigned.
     */
    @Override
    public boolean evaluate(final Facts facts) {
        return fulfillmentAssignedCondition.evaluate(facts);
    }
}
