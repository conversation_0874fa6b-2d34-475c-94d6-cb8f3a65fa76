package com.bestseller.services.orderrouting.rules.orderplacing;

import com.bestseller.services.orderrouting.rules.flows.OrderPlacingFlow;
import com.bestseller.services.orderrouting.rules.listeners.BreakChain;
import org.jeasy.rules.api.Action;
import org.jeasy.rules.api.Condition;
import org.jeasy.rules.api.Facts;
import org.jeasy.rules.core.BasicRule;
import org.springframework.stereotype.Component;

/**
 * Rule to evaluate if order has predefined fulfillment node.
 */
@Component
@BreakChain
public class PredefinedFulfillmentNodeRule extends BasicRule implements OrderPlacingFlow {
    private static final String RULE_NAME = "Predefined Fulfillment Node Rule";
    private static final String RULE_DESCRIPTION = "Rule to evaluate if order has predefined fulfillment node";
    private static final int RULE_PRIORITY = 1;

    private final Condition predefinedFulfillmentNodeCondition;
    private final Action advisedFulfillmentNodeAction;

    /**
     * Constructor for PredefinedFulfillmentNodeRule.
     * @param predefinedFulfillmentNodeCondition predefinedFulfillmentNodeCondition
     * @param advisedFulfillmentNodeAction advisedFulfillmentNodeAction
     */
    public PredefinedFulfillmentNodeRule(Condition predefinedFulfillmentNodeCondition, Action advisedFulfillmentNodeAction) {
        super(RULE_NAME, RULE_DESCRIPTION, RULE_PRIORITY);
        this.predefinedFulfillmentNodeCondition = predefinedFulfillmentNodeCondition;
        this.advisedFulfillmentNodeAction = advisedFulfillmentNodeAction;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean evaluate(Facts facts) {
        return predefinedFulfillmentNodeCondition.evaluate(facts);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void execute(Facts facts) throws Exception {
        advisedFulfillmentNodeAction.execute(facts);
    }
}
