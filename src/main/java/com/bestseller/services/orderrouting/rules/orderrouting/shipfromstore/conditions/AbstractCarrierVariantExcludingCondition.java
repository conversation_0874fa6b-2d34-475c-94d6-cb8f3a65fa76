package com.bestseller.services.orderrouting.rules.orderrouting.shipfromstore.conditions;

import com.bestseller.services.orderrouting.model.OrderModel;
import lombok.AllArgsConstructor;
import org.jeasy.rules.api.Condition;
import org.jeasy.rules.api.Facts;

import java.util.Arrays;

import static com.bestseller.services.orderrouting.rules.facts.FactNames.ORDER_MODEL_FACT;

/**
 * Condition to evaluate if there order lines cancelled by IMS in an order.
 */
@AllArgsConstructor
public abstract class AbstractCarrierVariantExcludingCondition implements Condition {

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean evaluate(Facts facts) {
        final OrderModel orderModel = facts.get(ORDER_MODEL_FACT);
        if (orderModel == null) {
            throw new IllegalArgumentException("Missing orderModel from facts.");
        }

        return Arrays.stream(getCarrierVariants())
                     .noneMatch(carrierVariant -> carrierVariant.equals(orderModel.getCarrierVariant()));
    }

    /**
     * Returns the specific list of carriers for which this condition is matching.
     * @return a list of carrier variants
     */
    protected abstract String[] getCarrierVariants();
}
