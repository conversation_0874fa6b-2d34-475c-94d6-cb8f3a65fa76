package com.bestseller.services.orderrouting.rules.orderrouting.shipfromstore.conditions;

import com.bestseller.services.orderrouting.model.OrderModel;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.jeasy.rules.api.Condition;
import org.jeasy.rules.api.Facts;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Objects;

import static com.bestseller.services.orderrouting.rules.facts.FactNames.ORDER_MODEL_FACT;

/**
 * Creates the specific condition for orders with more than threshold.
 */
@Component
@NoArgsConstructor
public class SfsBigOrderCondition implements Condition {

    @Value("${rules.shipFromStoreBigOrder.threshold}")
    @Setter
    private Integer threshold;

    @Override
    public boolean evaluate(final Facts facts) {
        OrderModel orderModel = Objects.requireNonNull(facts.get(ORDER_MODEL_FACT), "Missing orderModel from facts.");

        return orderModel.getOrderLineQuantities().size() > threshold;
    }
}
