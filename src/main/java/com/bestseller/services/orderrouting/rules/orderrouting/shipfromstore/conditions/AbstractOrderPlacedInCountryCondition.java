package com.bestseller.services.orderrouting.rules.orderrouting.shipfromstore.conditions;

import com.bestseller.services.orderrouting.model.AddressModel;
import com.bestseller.services.orderrouting.model.OrderModel;
import lombok.AllArgsConstructor;
import org.jeasy.rules.api.Condition;
import org.jeasy.rules.api.Facts;

import java.util.Set;

import static com.bestseller.services.orderrouting.rules.facts.FactNames.ORDER_MODEL_FACT;

/**
 * Condition to evaluate if order was placed in specified set of countries.
 */
@AllArgsConstructor
public abstract class AbstractOrderPlacedInCountryCondition implements Condition {

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean evaluate(Facts facts) {
        final OrderModel orderModel = facts.get(ORDER_MODEL_FACT);
        if (orderModel == null) {
            throw new IllegalArgumentException("Missing orderModel from facts.");
        }

        AddressModel addressModel = orderModel.getShippingAddress();
        return addressModel != null && addressModel.getCountry() != null && getCountries().contains(orderModel.getShippingAddress().getCountry());
    }

    /**
     * Returns the specific set of countries for which this condition is matching.
     * @return a set of country codes in ISO 3166-1 alpha-2 format
     */
    protected abstract Set<String> getCountries();
}
