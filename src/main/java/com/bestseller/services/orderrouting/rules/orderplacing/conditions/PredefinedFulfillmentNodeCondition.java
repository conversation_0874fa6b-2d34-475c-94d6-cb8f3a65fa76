package com.bestseller.services.orderrouting.rules.orderplacing.conditions;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderForFulfillment.OrderForFulfillment;
import lombok.AllArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.jeasy.rules.api.Condition;
import org.jeasy.rules.api.Facts;
import org.springframework.stereotype.Component;

import static com.bestseller.services.orderrouting.rules.facts.FactNames.ORDER_FOR_FULFILLMENT_FACT;

/**
 * Condition to evaluate if there predefined fulfillment node in an order.
 */
@Component
@AllArgsConstructor
public class PredefinedFulfillmentNodeCondition implements Condition {

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean evaluate(final Facts facts) {

        final OrderForFulfillment orderForFulfillment = facts.get(ORDER_FOR_FULFILLMENT_FACT);

        return orderForFulfillment != null
                && orderForFulfillment.getFulfillmentAdvice() != null
                && !orderForFulfillment.getFulfillmentAdvice().getHoldFromRouting()
                && StringUtils.isNotBlank(orderForFulfillment.getFulfillmentAdvice().getFulfillmentNode());
    }
}
