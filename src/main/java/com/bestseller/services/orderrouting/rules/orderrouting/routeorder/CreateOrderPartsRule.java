package com.bestseller.services.orderrouting.rules.orderrouting.routeorder;

import com.bestseller.services.orderrouting.rules.flows.OrderRoutingFlow;
import org.jeasy.rules.api.Action;
import org.jeasy.rules.api.Condition;
import org.jeasy.rules.api.Facts;
import org.jeasy.rules.core.BasicRule;
import org.springframework.stereotype.Component;

/**
 * Create and publish OrderPartsRouted message(s) for the given order.
 */
@Component
public class CreateOrderPartsRule extends BasicRule implements OrderRoutingFlow {
    private static final String RULE_NAME = "Create order part rule";
    private static final String RULE_DESCRIPTION = "Rule to create order part(s) to be dispatched from the warehouse(s)";
    private static final Integer RULE_PRIORITY = 100;

    private final Condition createOrderPartsCondition;
    private final Action createOrderPartsAction;

    /**
     * Constructor for CreateOrderPartsRule.
     * @param createOrderPartsCondition createOrderPartsCondition
     * @param createOrderPartsAction createOrderPartsAction
     */
    public CreateOrderPartsRule(Condition createOrderPartsCondition, Action createOrderPartsAction) {
        super(RULE_NAME, RULE_DESCRIPTION, RULE_PRIORITY);
        this.createOrderPartsCondition = createOrderPartsCondition;
        this.createOrderPartsAction = createOrderPartsAction;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean evaluate(final Facts facts) {
        return createOrderPartsCondition.evaluate(facts);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void execute(final Facts facts) throws Exception {
        createOrderPartsAction.execute(facts);
    }
}
