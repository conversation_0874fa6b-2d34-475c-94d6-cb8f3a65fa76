package com.bestseller.services.orderrouting.rules.orderpartrejecting.conditions;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartRejected.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartRejected.OrderPartRejected;
import com.bestseller.services.orderrouting.rules.facts.FactNames;
import com.bestseller.services.orderrouting.service.CancellationReason;
import lombok.AllArgsConstructor;
import org.jeasy.rules.api.Condition;
import org.jeasy.rules.api.Facts;
import org.springframework.stereotype.Component;

/**
 * Condition to evaluate if all reasons from cancellation message are ITEM_NOT_AVAILABLE.
 */
@Component
@AllArgsConstructor
public class ItemNotAvailableCondition implements Condition {

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean evaluate(final Facts facts) {
        OrderPartRejected orderPartRejected = facts.get(FactNames.ORDER_PART_REJECTED_FACT);

        return orderPartRejected.getOrderLines().stream()
                        .map(OrderLine::getCancelReason)
                        .allMatch(reason -> reason.equalsIgnoreCase(CancellationReason.ITEM_NOT_AVAILABLE.getReason()));
    }
}
