package com.bestseller.services.orderrouting.rules.orderrouting.shipfromstore.conditions;

import com.bestseller.services.orderrouting.model.OrderModel;
import lombok.AllArgsConstructor;
import org.jeasy.rules.api.Condition;
import org.jeasy.rules.api.Facts;
import org.springframework.stereotype.Component;

import static com.bestseller.services.orderrouting.rules.facts.FactNames.ORDER_MODEL_FACT;

/**
 * Condition to evaluate if order has standard shipping.
 */
@Component
@AllArgsConstructor
public class StandardShippingOrderCondition implements Condition {

    private static final String STANDARD_SHIPPING = "STANDARD";

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean evaluate(Facts facts) {
        final OrderModel orderModel = facts.get(ORDER_MODEL_FACT);
        if (orderModel == null) {
            throw new IllegalArgumentException("Missing orderModel from facts.");
        }

        return STANDARD_SHIPPING.equalsIgnoreCase(orderModel.getShippingMethod());
    }

}
