package com.bestseller.services.orderrouting.rules.orderrouting.routeorder.conditions;

import org.jeasy.rules.api.Condition;
import org.jeasy.rules.api.Facts;
import org.springframework.stereotype.Component;

/**
 * Condition for creating order part(s).
 */
@Component
public class CreateOrderPartsCondition implements Condition {

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean evaluate(final Facts facts) {
        return true;
    }
}
