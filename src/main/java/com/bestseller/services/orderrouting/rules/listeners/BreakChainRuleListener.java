package com.bestseller.services.orderrouting.rules.listeners;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jeasy.rules.api.Facts;
import org.jeasy.rules.api.Rule;
import org.jeasy.rules.api.RuleListener;
import org.springframework.aop.support.AopUtils;
import org.springframework.stereotype.Component;

/**
 * Custom Listener to skip evaluation of not yet evaluated rules if break chain fact is put in the facts.
 */
@Component
@AllArgsConstructor
@Slf4j
public class BreakChainRuleListener implements RuleListener {

    public static final String BREAK_EXECUTION = "breakExecution";

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean beforeEvaluate(final Rule rule, final Facts facts) {
        log.debug("Evaluating break chain rule listener...");

        final Boolean breakChain = facts.get(BREAK_EXECUTION);
        boolean result = breakChain == null || !breakChain;
        log.debug("Break chain rule listener evaluation is {}", result);

        return result;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void afterEvaluate(final Rule rule, final Facts facts, boolean evaluationResult) {
        // empty - overridden because of the interface.
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void beforeExecute(final Rule rule, final Facts facts) {
        Class<?> ruleClass = AopUtils.getTargetClass(rule);

        BreakChain breakChain = ruleClass.getAnnotation(BreakChain.class);
        if (breakChain != null) {
            facts.put(BREAK_EXECUTION, true);
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void onSuccess(final Rule rule, final Facts facts) {
        // empty - overridden because of the interface.
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void onFailure(final Rule rule, final Facts facts, final Exception exception) {
        // empty - overridden because of the interface.
    }
}
