package com.bestseller.services.orderrouting.rules.orderrouting.shipfromstore.composites;

import org.jeasy.rules.api.Condition;
import org.jeasy.rules.api.Facts;
import org.jeasy.rules.core.BasicRule;
import org.springframework.stereotype.Component;

/**
 * Creates the specific ship from store rule for eligible single brand orders.
 */
@Component
public class SfsSingleBrandOrderRule extends BasicRule {
    private static final String RULE_NAME = "Single Brand Order Rule";
    private static final String RULE_DESCRIPTION = "Rule to route single brand order to the warehouses";

    private Condition sfsSingleBrandOrderCondition;

    /**
     * Constructor for SfsSingleBrandOrderRule.
     * @param sfsSingleBrandOrderCondition sfsSingleBrandOrderCondition.
     */
    public SfsSingleBrandOrderRule(Condition sfsSingleBrandOrderCondition) {
        super(RULE_NAME, RULE_DESCRIPTION);
        this.sfsSingleBrandOrderCondition = sfsSingleBrandOrderCondition;
    }

    /**
     * Evaluates if order is of single brand.
     *
     * @param facts a bundle of facts needed for evaluation.
     * @return true if condition is met, false otherwise.
     */
    @Override
    public boolean evaluate(final Facts facts) {
        return sfsSingleBrandOrderCondition.evaluate(facts);
    }
}
