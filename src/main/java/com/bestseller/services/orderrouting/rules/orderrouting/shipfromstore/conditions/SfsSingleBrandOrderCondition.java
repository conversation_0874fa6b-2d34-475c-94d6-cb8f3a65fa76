package com.bestseller.services.orderrouting.rules.orderrouting.shipfromstore.conditions;

import com.bestseller.services.orderrouting.model.OrderModel;
import lombok.AllArgsConstructor;
import org.jeasy.rules.api.Condition;
import org.jeasy.rules.api.Facts;
import org.springframework.stereotype.Component;

import static com.bestseller.services.orderrouting.rules.facts.FactNames.ORDER_MODEL_FACT;

/**
 * Creates the specific ship from store condition for matching specific single brands.
 */
@Component
@AllArgsConstructor
public class SfsSingleBrandOrderCondition implements Condition {

    @Override
    public boolean evaluate(Facts facts) {
        final OrderModel orderModel = facts.get(ORDER_MODEL_FACT);
        if (orderModel == null) {
            throw new IllegalArgumentException("Missing orderModel from facts.");
        }

        return orderModel.getOrderLineQuantities().stream().allMatch(orderLine -> orderLine.getBrand() != null
                && orderLine.getBrand().equalsIgnoreCase(orderModel.getOrderLineQuantities().get(0).getBrand()));
    }
}
