package com.bestseller.services.orderrouting.rules.listeners;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * If a rule must break the rule chain execution it must be annotated with this annotation.
 *
 * <code>
 *     {@literal @}Component
 *     {@literal @}BreakChain
 *     public class MyRule extends BasicRule {
 *       ...
 *     }
 * </code>
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface BreakChain {
}
