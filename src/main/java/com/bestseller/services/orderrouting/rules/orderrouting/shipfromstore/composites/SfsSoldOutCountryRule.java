package com.bestseller.services.orderrouting.rules.orderrouting.shipfromstore.composites;

import com.bestseller.services.orderrouting.rules.orderrouting.shipfromstore.AbstractOrderPlacedInCountryRule;
import lombok.AllArgsConstructor;
import org.jeasy.rules.api.Condition;
import org.springframework.stereotype.Component;

/**
 * Creates the specific rule for countries eligible for Ship From Store Sold Out Solution.
 */
@Component
@AllArgsConstructor
public class SfsSoldOutCountryRule extends AbstractOrderPlacedInCountryRule {

    private Condition sfsSoldOutCountryCondition;

    /**
     * {@inheritDoc}
     */
    @Override
    protected Condition getOrderPlacedInCountryCondition() {
        return sfsSoldOutCountryCondition;
    }

}
