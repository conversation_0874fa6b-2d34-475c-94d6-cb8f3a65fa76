package com.bestseller.services.orderrouting.rules.orderrouting.shipfromstore.composites;

import com.bestseller.services.orderrouting.rules.orderrouting.shipfromstore.AbstractOrderPlacedInCountryRule;
import org.jeasy.rules.api.Condition;
import org.springframework.stereotype.Component;

import lombok.AllArgsConstructor;

/**
 * Creates the specific rule for countries eligible for Ship From Store First.
 */
@Component
@AllArgsConstructor
public class SfsFirstCountryRule extends AbstractOrderPlacedInCountryRule {

    private Condition sfsFirstCountryCondition;

    /**
     * {@inheritDoc}
     */
    @Override
    protected Condition getOrderPlacedInCountryCondition() {
        return sfsFirstCountryCondition;
    }

}
