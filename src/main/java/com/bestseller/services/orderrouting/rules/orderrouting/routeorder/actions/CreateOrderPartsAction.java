package com.bestseller.services.orderrouting.rules.orderrouting.routeorder.actions;

import com.bestseller.services.orderrouting.event.AvailableInventoryEvent;
import com.bestseller.services.orderrouting.model.OrderLineQuantityStatus;
import com.bestseller.services.orderrouting.model.OrderModel;
import com.bestseller.services.orderrouting.service.OrderService;
import lombok.AllArgsConstructor;
import org.jeasy.rules.api.Action;
import org.jeasy.rules.api.Facts;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import static com.bestseller.services.orderrouting.rules.facts.FactNames.ORDER_MODEL_FACT;

/**
 * Action for creating order part(s).
 */
@Component
@AllArgsConstructor
public class CreateOrderPartsAction implements Action {
    private OrderService orderService;
    private ApplicationEventPublisher eventPublisher;

    /**
     * {@inheritDoc}
     */
    @Override
    public void execute(final Facts facts) {
        // Get information from facts.
        OrderModel orderModel = facts.get(ORDER_MODEL_FACT);

        // Change status to routing.
        orderService.updateOrderLinesStatus(orderModel.getOrderLineQuantities(), OrderLineQuantityStatus.ROUTING);

        // Publish order parts routed message for each warehouse.
        eventPublisher.publishEvent(new AvailableInventoryEvent(this, orderModel));
    }
}
