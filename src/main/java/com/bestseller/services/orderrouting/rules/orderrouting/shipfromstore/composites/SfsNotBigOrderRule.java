package com.bestseller.services.orderrouting.rules.orderrouting.shipfromstore.composites;

import org.jeasy.rules.api.Condition;
import org.jeasy.rules.api.Facts;
import org.springframework.stereotype.Component;

/**
 * Creates the specific rule to check the items on the order to determine if the order is not big enough for splitting.
 */
@Component
public class SfsNotBigOrderRule extends SfsBigOrderRule {

    private static final String RULE_NAME = "Not big order Rule";
    private static final String RULE_DESCRIPTION = "Rule to evaluate if order is not big order";

    /**
     * Constructor for SfsNotBigOrderRule.
     * @param sfsBigOrderCondition sfsBigOrderCondition bean.
     */
    public SfsNotBigOrderRule(final Condition sfsBigOrderCondition) {
        super(sfsBigOrderCondition, RULE_NAME, RULE_DESCRIPTION);
    }

    /**
     * Evaluates if an order is not big enough to activate splitting.
     *
     * @param facts a bundle of facts needed for evaluation.
     * @return true if order is not big enough to split.
     */
    @Override
    public boolean evaluate(final Facts facts) {
        return !super.evaluate(facts);
    }
}
