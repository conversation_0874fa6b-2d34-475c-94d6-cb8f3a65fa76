package com.bestseller.services.orderrouting.rules.orderrouting.shipfromstore.composites;

import org.jeasy.rules.api.Condition;
import org.jeasy.rules.api.Facts;
import org.jeasy.rules.core.BasicRule;
import org.springframework.stereotype.Component;

/**
 * Creates the specific ship from store rule for eligible for splitting orders.
 */
@Component
public class SfsSplitAllowedRule extends BasicRule {
    private static final String RULE_NAME = "Sfs split allowed rule";
    private static final String RULE_DESCRIPTION = "Rule to check if split is allowed";

    private Condition sfsStoreFirstSplitCondition;

    /**
     * Constructor for SfsSplitAllowedRule.
     * @param sfsStoreFirstSplitCondition sfsStoreFirstSplitCondition
     */
    public SfsSplitAllowedRule(Condition sfsStoreFirstSplitCondition) {
        super(RULE_NAME, RULE_DESCRIPTION);
        this.sfsStoreFirstSplitCondition = sfsStoreFirstSplitCondition;
    }

    /**
     * Evaluates if order is allowed to be split.
     *
     * @param facts a bundle of facts needed for evaluation.
     * @return true if condition is met, false otherwise.
     */
    @Override
    public boolean evaluate(final Facts facts) {
        return sfsStoreFirstSplitCondition.evaluate(facts);
    }
}
