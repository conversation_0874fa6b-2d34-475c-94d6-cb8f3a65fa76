package com.bestseller.services.orderrouting.rules.orderplacing.actions;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailabilityRequest;
import com.bestseller.services.orderrouting.configuration.messaging.ItemAvailabilityChannels;
import com.bestseller.services.orderrouting.messaging.producer.Producer;
import com.bestseller.services.orderrouting.model.OrderModel;
import datadog.trace.api.Trace;
import org.jeasy.rules.api.Action;
import org.jeasy.rules.api.Facts;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

import static com.bestseller.services.orderrouting.rules.facts.FactNames.ORDER_MODEL_FACT;

/**
 * Action triggered when condition for ship to warehouse is met.
 */
@Component
public class ShipFromWarehouseAction implements Action {

    @Autowired
    private Converter<OrderModel, ItemAvailabilityRequest> itemAvailabilityRequestConverter;
    @Autowired
    private Producer<ItemAvailabilityRequest> itemAvailabilityRequestProducer;

    /**
     * {@inheritDoc}
     */
    @Override
    @Trace(operationName = ItemAvailabilityChannels.ITEM_AVAILABILITY_REQUEST)
    public void execute(final Facts facts) {
        OrderModel orderModel = facts.get(ORDER_MODEL_FACT);
        sendItemAvailabilityRequest(orderModel);
    }

    private void sendItemAvailabilityRequest(OrderModel orderModel) {
        ItemAvailabilityRequest itemAvailabilityRequest = itemAvailabilityRequestConverter.convert(orderModel);

        itemAvailabilityRequestProducer.produce(itemAvailabilityRequest);
    }
}
