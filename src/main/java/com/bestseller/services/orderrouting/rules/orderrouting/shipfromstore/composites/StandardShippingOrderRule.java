package com.bestseller.services.orderrouting.rules.orderrouting.shipfromstore.composites;

import org.jeasy.rules.api.Condition;
import org.jeasy.rules.api.Facts;
import org.jeasy.rules.core.BasicRule;
import org.springframework.stereotype.Component;

/**
 * Rule to evaluate if an order has standard shipping method.
 */
@Component
public class StandardShippingOrderRule extends BasicRule {

    private static final String RULE_NAME = "Standard shipping order Rule";
    private static final String RULE_DESCRIPTION = "Rule to evaluate if order has standard shipping";

    private final Condition standardShippingOrderCondition;

    /**
     * Constructor for StandardShippingOrderRule.
     * @param standardShippingOrderCondition standardShippingOrderCondition.
     */
    public StandardShippingOrderRule(Condition standardShippingOrderCondition) {
        super(RULE_NAME, RULE_DESCRIPTION);
        this.standardShippingOrderCondition = standardShippingOrderCondition;
    }

    /**
     * Evaluates if an order is standard.
     *
     * @param facts a bundle of facts needed for evaluation.
     * @return true if order has standard shipping.
     */
    @Override
    public boolean evaluate(final Facts facts) {
        return standardShippingOrderCondition.evaluate(facts);
    }
}
