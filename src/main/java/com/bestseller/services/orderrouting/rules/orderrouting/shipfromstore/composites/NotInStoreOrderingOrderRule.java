package com.bestseller.services.orderrouting.rules.orderrouting.shipfromstore.composites;

import org.jeasy.rules.api.Condition;
import org.jeasy.rules.api.Facts;
import org.jeasy.rules.core.BasicRule;
import org.springframework.stereotype.Component;

/**
 * Rule to evaluate if an order is In-store ordering (ISO).
 */
@Component
public class NotInStoreOrderingOrderRule extends BasicRule {

    private static final String RULE_NAME = "Not an In-store ordering";
    private static final String RULE_DESCRIPTION = "Rule to evaluate if order is not an In-store ordering";

    private final Condition inStoreOrderCondition;

    /**
     * Constructor for the rule.
     *
     * @param inStoreOrderCondition the condition to evaluate if an order is an ISO.
     */
    public NotInStoreOrderingOrderRule(Condition inStoreOrderCondition) {
        super(RULE_NAME, RULE_DESCRIPTION);
        this.inStoreOrderCondition = inStoreOrderCondition;
    }

    /**
     * Evaluates if an order is an ISO.
     *
     * @param facts a bundle of facts needed for evaluation.
     * @return true if order is not ISO.
     */
    @Override
    public boolean evaluate(final Facts facts) {
        return !inStoreOrderCondition.evaluate(facts);
    }
}
