package com.bestseller.services.orderrouting.rules.orderrouting.shipfromstore;

import org.jeasy.rules.api.Condition;
import org.jeasy.rules.api.Facts;
import org.jeasy.rules.core.BasicRule;

/**
 * Rule to evaluate if order was placed from a specific channel.
 */
public abstract class AbstractOrderPlacedFromChannelRule extends BasicRule {

    private static final String RULE_NAME = "Order placed from channel rule";
    private static final String RULE_DESCRIPTION = "Rule to evaluate if order was placed from a channel";

    /**
     * Constructor for AbstractOrderPlacedFromChannelRule.
     */
    public AbstractOrderPlacedFromChannelRule() {
        super(RULE_NAME, RULE_DESCRIPTION);
    }

    /**
     * Evaluates if order was placed from a specific channel, for example OTTO.
     *
     * @param facts a bundle of facts needed for evaluation.
     * @return true if condition is met, false otherwise.
     */
    @Override
    public boolean evaluate(final Facts facts) {
        return getOrderPlacedFromChannelCondition().evaluate(facts);
    }

    /**
     * Returns the specific condition that triggers this rule.
     * @return the condition object
     */
    protected abstract Condition getOrderPlacedFromChannelCondition();
}
