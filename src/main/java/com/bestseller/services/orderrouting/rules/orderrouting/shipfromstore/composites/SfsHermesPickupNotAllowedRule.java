package com.bestseller.services.orderrouting.rules.orderrouting.shipfromstore.composites;

import org.jeasy.rules.api.Condition;
import org.jeasy.rules.api.Facts;
import org.jeasy.rules.core.BasicRule;
import org.springframework.stereotype.Component;

/**
 * Creates the specific ship from store rule for now allowing Hermes carrier.
 */
@Component
public class SfsHermesPickupNotAllowedRule extends BasicRule {
    private static final String RULE_NAME = "Sfs Hermes carrier not allowed rule";
    private static final String RULE_DESCRIPTION = "Rule to check if this carrier is not Her<PERSON>";

    private Condition sfsHermesPickupCondition;

    /**
     * Constructor for SfsHermesCarrierNotAllowedRule.
     * @param sfsHermesPickupCondition sfsHermesCarrierCondition.
     */
    public SfsHermesPickupNotAllowedRule(Condition sfsHermesPickupCondition) {
        super(RULE_NAME, RULE_DESCRIPTION);
        this.sfsHermesPickupCondition = sfsHermesPickupCondition;
    }

    /**
     * Evaluates if order is not Her<PERSON>.
     *
     * @param facts a bundle of facts needed for evaluation.
     * @return true if condition is met, false otherwise.
     */
    @Override
    public boolean evaluate(final Facts facts) {
        return !sfsHermesPickupCondition.evaluate(facts);
    }
}
