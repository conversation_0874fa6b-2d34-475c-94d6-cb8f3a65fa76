package com.bestseller.services.orderrouting.rules.orderrouting.shipfromstore.conditions;

import org.springframework.stereotype.Component;

import com.bestseller.services.orderrouting.configuration.properties.RulesProperties;

import lombok.AllArgsConstructor;

/**
 * Creates the specific ship from store condition for excluding carrier variants.
 */
@Component
@AllArgsConstructor
public class SfsCarrierVariantExcludingCondition extends AbstractCarrierVariantExcludingCondition {

    private RulesProperties properties;

    /**
     * {@inheritDoc}
     */
    @Override
    protected String[] getCarrierVariants() {
        return properties.getShipFromStoreFirst().getExcludedCarrierVariants();
    }
}
