package com.bestseller.services.orderrouting.rules.orderplacing.actions;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderForFulfillment.OrderForFulfillment;
import com.bestseller.services.orderrouting.event.AvailableInventoryEvent;
import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.model.OrderLineQuantityStatus;
import com.bestseller.services.orderrouting.model.OrderModel;
import com.bestseller.services.orderrouting.service.OrderService;
import lombok.AllArgsConstructor;
import org.jeasy.rules.api.Action;
import org.jeasy.rules.api.Facts;
import org.springframework.context.ApplicationEventPublisher;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import static com.bestseller.services.orderrouting.rules.facts.FactNames.ORDER_FOR_FULFILLMENT_FACT;
import static com.bestseller.services.orderrouting.rules.facts.FactNames.ORDER_MODEL_FACT;

/**
 * Action triggered when condition for predefined fulfillment node is met.
 */
@AllArgsConstructor
public abstract class FulfillmentAction implements Action {
    private OrderService orderService;
    private ApplicationEventPublisher eventPublisher;

    /**
     * {@inheritDoc}
     */
    @Override
    public void execute(final Facts facts) {
        // get information from facts
        OrderModel orderModel = facts.get(ORDER_MODEL_FACT);
        OrderForFulfillment orderForFulfillment = facts.get(ORDER_FOR_FULFILLMENT_FACT);

        // change status to routing
        orderService.updateOrderLinesStatus(orderModel.getOrderLineQuantities(), OrderLineQuantityStatus.ROUTING);

        // routing decision
        String warehouse = getFulfillmentNode(orderForFulfillment, orderModel);
        Map<String, List<List<OrderLineQuantityModel>>> routingDecision =
                Map.of(warehouse, Collections.singletonList(orderModel.getOrderLineQuantities()));

        // apply routing decision
        orderService.assignOrderPartFulfillmentNodes(orderModel, routingDecision);
        orderService.saveOrderLines(orderModel.getOrderLineQuantities());

        // Publish order parts routed message for each warehouse.
        eventPublisher.publishEvent(new AvailableInventoryEvent(this, orderModel));
    }

    /**
     * Determine the preferred fulfillment node to assign.
     *
     * @param orderForFulfillment OrderForFulfillment message to possibly get predefined fulfillment node from
     * @param orderModel order model with the information stored in the DB
     * @return the predefined fulfillment node
     */
    protected abstract String getFulfillmentNode(OrderForFulfillment orderForFulfillment, OrderModel orderModel);
}
