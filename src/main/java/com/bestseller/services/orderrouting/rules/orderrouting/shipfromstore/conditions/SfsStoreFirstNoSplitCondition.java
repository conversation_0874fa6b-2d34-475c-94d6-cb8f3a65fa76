package com.bestseller.services.orderrouting.rules.orderrouting.shipfromstore.conditions;

import com.bestseller.services.orderrouting.feature.toggles.ORSFeatures;
import lombok.AllArgsConstructor;
import org.jeasy.rules.api.Condition;
import org.jeasy.rules.api.Facts;
import org.springframework.stereotype.Component;

/**
 * Creates the specific ship from store store first condition for splitting orders.
 */
@Component
@AllArgsConstructor
public class SfsStoreFirstNoSplitCondition implements Condition {

    @Override
    public boolean evaluate(final Facts facts) {
        return !ORSFeatures.SFS_STORE_FIRST_SPLIT_ALLOWED.isActive();
    }
}
