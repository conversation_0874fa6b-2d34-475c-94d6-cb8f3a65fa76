package com.bestseller.services.orderrouting.rules.orderpartrejecting.actions;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartRejected.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartRejected.OrderPartRejected;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartsCancelled.OrderPartsCancelled;
import com.bestseller.services.orderrouting.converter.OrderLineExtractor;
import com.bestseller.services.orderrouting.converter.OrderPartsCancelledConverter;
import com.bestseller.services.orderrouting.messaging.producer.OrderPartsCancelledProducer;
import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.model.OrderLineQuantityStatus;
import com.bestseller.services.orderrouting.model.OrderModel;
import com.bestseller.services.orderrouting.rules.facts.FactNames;
import com.bestseller.services.orderrouting.service.CancellationReason;
import com.bestseller.services.orderrouting.service.OrderService;
import lombok.AllArgsConstructor;
import org.jeasy.rules.api.Action;
import org.jeasy.rules.api.Facts;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * Cancels the order lines from the order parts rejected message.
 */
@Component
@AllArgsConstructor
public class CancelOrderPartAction implements Action {
    private final OrderService orderService;
    private final OrderLineExtractor orderLineExtractor;

    private final OrderPartsCancelledConverter orderPartsCancelledConverter;
    private final OrderPartsCancelledProducer orderPartsCancelledProducer;

    @Override
    public void execute(Facts facts) {
        OrderModel orderModel = facts.get(FactNames.ORDER_MODEL_FACT);
        OrderPartRejected orderPartRejected = facts.get(FactNames.ORDER_PART_REJECTED_FACT);

        updateOrderLines(orderModel, orderPartRejected);

        OrderPartsCancelled orderPartsCancelled = orderPartsCancelledConverter.convert(orderPartRejected);
        orderPartsCancelledProducer.produce(orderPartsCancelled);
    }

    private void updateOrderLines(OrderModel orderModel, OrderPartRejected orderPartRejected) {
        List<OrderLineQuantityModel> orderLinesInMessage = orderLineExtractor.extractOrderLinesFromMessage(orderModel, orderPartRejected);

        // Cancellation reason is the same for all the order lines.
        CancellationReason cancellationReason = getCancellationReason(orderPartRejected);

        for (OrderLineQuantityModel orderLine : orderLinesInMessage) {
            orderLine.setCancelReason(cancellationReason);
            orderLine.setStatus(OrderLineQuantityStatus.CANCELLED);
            orderLine.setOrderId(orderModel.getOrderId());
        }

        orderService.saveOrderLines(orderLinesInMessage);
    }

    private CancellationReason getCancellationReason(OrderPartRejected orderPartRejected) {
        Optional<OrderLine> orderLine = orderPartRejected.getOrderLines().stream().findFirst();
        return orderLine.map(it -> CancellationReason.valueOf(it.getCancelReason())).orElse(CancellationReason.UNKNOWN);
    }
}
