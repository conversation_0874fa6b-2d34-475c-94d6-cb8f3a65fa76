package com.bestseller.services.orderrouting.rules.orderrouting.shipfromstore;

import org.jeasy.rules.api.Condition;
import org.jeasy.rules.api.Facts;
import org.jeasy.rules.core.BasicRule;

/**
 * Rule to evaluate if order was placed in a specific country.
 */
public abstract class AbstractOrderPlacedInCountryRule extends BasicRule {

    private static final String RULE_NAME = "Order placed in country rule";
    private static final String RULE_DESCRIPTION = "Rule to evaluate if order was placed in country";

    /**
     * Constructor for AbstractOrderPlacedInCountryRule.
     */
    public AbstractOrderPlacedInCountryRule() {
        super(RULE_NAME, RULE_DESCRIPTION);
    }

    /**
     * Evaluates if order was placed in a specific country, for example Norway.
     *
     * @param facts a bundle of facts needed for evaluation.
     * @return true if condition is met, false otherwise.
     */
    @Override
    public boolean evaluate(final Facts facts) {
        return getOrderPlacedInCountryCondition().evaluate(facts);
    }

    /**
     * Returns the specific condition that triggers this rule.
     * @return the condition object
     */
    protected abstract Condition getOrderPlacedInCountryCondition();
}
