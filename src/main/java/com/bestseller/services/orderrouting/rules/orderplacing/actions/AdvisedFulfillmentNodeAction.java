package com.bestseller.services.orderrouting.rules.orderplacing.actions;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderForFulfillment.OrderForFulfillment;
import com.bestseller.services.orderrouting.model.OrderModel;
import com.bestseller.services.orderrouting.service.OrderService;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * Action triggered when condition for predefined fulfillment node is met.
 */
@Component
public class AdvisedFulfillmentNodeAction extends FulfillmentAction {

    /**
     * Explicit constructor due to the limitation of Lombok.
     */
    public AdvisedFulfillmentNodeAction(OrderService orderService, ApplicationEventPublisher eventPublisher) {
        super(orderService, eventPublisher);
    }

    @Override
    protected String getFulfillmentNode(OrderForFulfillment orderForFulfillment, OrderModel orderModel) {
        return orderForFulfillment.getFulfillmentAdvice().getFulfillmentNode();
    }
}
