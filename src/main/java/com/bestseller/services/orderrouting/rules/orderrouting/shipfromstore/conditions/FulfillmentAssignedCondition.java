package com.bestseller.services.orderrouting.rules.orderrouting.shipfromstore.conditions;

import lombok.AllArgsConstructor;
import org.apache.commons.lang.BooleanUtils;
import org.jeasy.rules.api.Condition;
import org.jeasy.rules.api.Facts;
import org.springframework.stereotype.Component;

import static com.bestseller.services.orderrouting.rules.facts.FactNames.FULFILLMENT_INFORMATION_ASSIGNED_FACT;

/**
 * Evaluates if order is available in generic warehouses.
 */
@Component
@AllArgsConstructor
public class FulfillmentAssignedCondition implements Condition {

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean evaluate(final Facts facts) {
        // Evaluate ONLY if previous routing rules have not yet assigned fulfillment information on order lines in this
        // rule evaluation session.
        return !BooleanUtils.isTrue(facts.get(FULFILLMENT_INFORMATION_ASSIGNED_FACT));
    }
}
