package com.bestseller.services.orderrouting.rules.orderrouting.shipfromstore.composites;

import com.bestseller.services.orderrouting.rules.orderrouting.shipfromstore.AbstractCarrierVariantExcludingRule;
import org.jeasy.rules.api.Condition;
import org.springframework.stereotype.Component;

import lombok.AllArgsConstructor;

/**
 * Creates the specific ship from store rule for excluding carrier variants.
 */
@Component
@AllArgsConstructor
public class SfsCarrierVariantExcludingRule extends AbstractCarrierVariantExcludingRule {

    private Condition sfsCarrierVariantExcludingCondition;

    /**
     * {@inheritDoc}
     */
    @Override
    protected Condition getCarrierVariantExcludingCondition() {
        return sfsCarrierVariantExcludingCondition;
    }

}
