package com.bestseller.services.orderrouting.rules.orderpartrejecting;

import com.bestseller.services.orderrouting.metric.CounterOperation;
import com.bestseller.services.orderrouting.rules.facts.FactNames;
import com.bestseller.services.orderrouting.rules.flows.OrderPartRejectingFlow;
import com.bestseller.services.orderrouting.rules.listeners.BreakChain;
import com.bestseller.services.orderrouting.rules.orderpartrejecting.actions.RerouteOrderPartAction;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tag;
import org.jeasy.rules.api.Condition;
import org.jeasy.rules.api.Facts;
import org.jeasy.rules.core.BasicRule;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Store Rejection Rule.
 */
@Component
@BreakChain
public class ReroutingOrderPartRule extends BasicRule implements OrderPartRejectingFlow {
    private static final String RULE_NAME = "Store rejection rule";
    private static final String RULE_DESCRIPTION = "Routes store rejected orders to the default warehouse";

    private final RerouteOrderPartAction rerouteOrderPartAction;
    private final MeterRegistry meterRegistry;
    private final Condition itemNotAvailableCondition;
    private final Condition rerouteCondition;

    /**
     * Constructor for ReroutingOrderPartRule.
     * @param rerouteOrderPartAction rerouteOrderPartAction
     * @param meterRegistry meterRegistry
     * @param itemNotAvailableCondition itemNotAvailableCondition
     * @param rerouteCondition rerouteCondition
     */
    public ReroutingOrderPartRule(RerouteOrderPartAction rerouteOrderPartAction,
                                  MeterRegistry meterRegistry,
                                  Condition itemNotAvailableCondition,
                                  Condition rerouteCondition) {
        super(RULE_NAME, RULE_DESCRIPTION);
        this.rerouteOrderPartAction = rerouteOrderPartAction;
        this.meterRegistry = meterRegistry;
        this.itemNotAvailableCondition = itemNotAvailableCondition;
        this.rerouteCondition = rerouteCondition;
    }

    /**
     * Evaluates whether the order is suited rerouting from store to warehouse.
     * @param facts a bundle of facts needed for evaluation.
     * @return true if condition is met, false otherwise.
     */
    @Override
    public boolean evaluate(Facts facts) {
        boolean allItemsAreNotAvailable = itemNotAvailableCondition.evaluate(facts);
        boolean orderShouldBeRerouted = rerouteCondition.evaluate(facts);

        return allItemsAreNotAvailable && orderShouldBeRerouted;
    }

    /**
     * Routes the order for fulfillment from primary warehouse and resets the order lines to the PLACED state.
     * @param facts a bundle of facts needed for execution.
     */
    @Override
    public void execute(Facts facts) {
        var tags = List.of(Tag.of("destinationWarehouse", rerouteOrderPartAction.getFulfillmentNode(facts.get(FactNames.ORDER_MODEL_FACT))),
                Tag.of("resubmitOrderPart", Boolean.TRUE.toString()));
        meterRegistry.counter(CounterOperation.REROUTE_ORDER_PART, tags).increment();

        rerouteOrderPartAction.execute(facts);
    }
}
