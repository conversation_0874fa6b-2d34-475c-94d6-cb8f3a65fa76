package com.bestseller.services.orderrouting.service.idempotency;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderForFulfillment.OrderForFulfillment;
import com.bestseller.services.orderrouting.metric.KafkaMessageOperation;
import com.bestseller.services.orderrouting.model.OrderModel;
import com.bestseller.services.orderrouting.repository.OrderModelRepository;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.BooleanUtils;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * Service class to check for any duplicate orderForFulfillment message and records an event in case of duplicate
 * message.
 */
@Service
@Slf4j
public class OrderForFulfillmentMessageIdempotencyService
        extends AbstractMessageIdempotencyService<OrderForFulfillment> {
    private OrderModelRepository orderModelRepository;

    /**
     * Constructor.
     *
     * @param meterRegistry
     */
    public OrderForFulfillmentMessageIdempotencyService(MeterRegistry meterRegistry, OrderModelRepository orderModelRepository) {
        super(meterRegistry);
        this.orderModelRepository = orderModelRepository;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    protected boolean isDuplicateMessage(final OrderForFulfillment message) {
        log.debug("Checking for duplicate order for fulfillment message");

        Optional<OrderModel> orderModel = orderModelRepository.findById(message.getOrderId());

        return orderModel.isPresent() && BooleanUtils.isFalse(orderModel.get().getIsOrderAdvice());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    protected Tags provideEventData(final OrderForFulfillment message) {
        return Tags.of(ORDER_ID, message.getOrderId());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    protected KafkaMessageOperation provideEventName() {
        return KafkaMessageOperation.DUPLICATE_ORDER_FULFILLMENT_MESSAGE;
    }
}
