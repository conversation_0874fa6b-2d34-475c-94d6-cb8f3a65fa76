package com.bestseller.services.orderrouting.service.idempotency;

import com.bestseller.services.orderrouting.metric.KafkaMessageOperation;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * Abstract class for all validation services connected to idempotent validation of messages.
 */
@AllArgsConstructor
@Slf4j
public abstract class AbstractMessageIdempotencyService<T> implements MessageIdempotencyService<T> {

    protected static final String ORDER_ID = "orderId";
    protected static final String MESSAGE = "message";

    private MeterRegistry meterRegistry;

    /**
     * Hold the logic for checking if message is duplicate.
     *
     * @param message to be checked.
     * @return true if duplicate and false if not.
     */
    protected abstract boolean isDuplicateMessage(T message);

    /**
     * Extract tags with data to be logged for specific message.
     *
     * @return tags with the event data.
     */
    protected abstract Tags provideEventData(T message);

    /**
     * Returns the event name for logging.
     *
     * @return the event name.
     */
    protected abstract KafkaMessageOperation provideEventName();

    /**
     * Record duplicate event metric, the event name is recorded with the id of the order as a custom event
     * on Micrometer.
     * @param eventName event name for the event recorded
     * @param tags list of tags
     */
    private void recordDuplicateMessage(final KafkaMessageOperation eventName, final Tags tags) {
        log.debug("Recording event {} for duplicate message.", eventName);
        meterRegistry.counter(eventName.name(), tags).increment();
        log.info("Event {} for duplicate message sent.", eventName);
    }

    /**
     * Check if message is idempotent and reports to {@link MeterRegistry} such messages.
     * Concrete implementations for {@link this.isDuplicateMessage}, {@link this.provideEventData} and
     * {@link this.provideEventName} should be done in the typed sub-class implementation for specific messages.
     *
     * @param message message to be checked.
     * @return true if it's duplicate and false if not.
     */
    @Override
    public boolean checkDuplicateMessage(final T message) {
        if (isDuplicateMessage(message)) {
            recordDuplicateMessage(provideEventName(), provideEventData(message));
            return true;
        }
        return false;
    }
}
