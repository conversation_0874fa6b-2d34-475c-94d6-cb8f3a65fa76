package com.bestseller.services.orderrouting.service;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Collection;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Reasons for cancellation of order line.
 */
@AllArgsConstructor(access = AccessLevel.PACKAGE)
public enum CancellationReason {

    /**
     * Item not available at the moment in the warehouse.
     */
    ITEM_NOT_AVAILABLE("ITEM_NOT_AVAILABLE"),
    /**
     * Available (last) item was damaged.
     */
    ITEM_DAMAGED("ITEM_DAMAGED"),
    /**
     * Carrier refused the shipment.
     */
    REFUSED_BY_CARRIER("REFUSED_BY_CARRIER"),
    /**
     * Shipment cancelled in warehouse by advice of client company.
     */
    CLIENT_COMPANY_CANCELLATION("CLIENT_COMPANY_CANCELLATION"),
    /**
     * Cancellation from external warehouse.
     */
    WAREHOUSE_CANCELLATION("WAREHOUSE_CANCELLATION"),
    /**
     * Cancellation from external warehouse (3GL).
     */
    WAREHOUSE_3GL_CANCELLATION("WAREHOUSE_3GL_CANCELLATION"),
    /**
     * The cancel reason for orders rejected by call center.
     */
    MANUAL_CANCELLATION("MANUAL_CANCELLATION"),
    /**
     * The cancel reason for orders rejected by OWC.
     */
    STORE_REJECTION("STORE_REJECTION"),
    /**
     * The cancel reason for orders due to incomplete shipment (only for „all-or-nothing“ agreement: if
     * at least one detail was non deliverable, all other details have to be cancelled as well -> these will be flagged as auto cancel).
     */
    AUTO_CANCELLATION("AUTO_CANCELLATION"),
    /**
     * Used when we can't map the cancellation reason.
     */
    UNKNOWN("UNKNOWN");

    public static final Collection<String> REASONS = Stream.of(CancellationReason.values())
            .map(CancellationReason::getReason)
            .collect(Collectors.toSet());

    @Getter
    private final String reason;

}
