package com.bestseller.services.orderrouting.service;

import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.model.OrderLineQuantityStatus;
import com.bestseller.services.orderrouting.model.OrderModel;
import com.bestseller.services.orderrouting.repository.OrderLineQuantityModelRepository;
import com.bestseller.services.orderrouting.repository.OrderModelRepository;
import lombok.AllArgsConstructor;
import lombok.Generated;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.PrimitiveIterator;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * Service for {@link OrderModel} related operations.
 */
@Service
@AllArgsConstructor
@Transactional
@Slf4j
public class OrderService {
    public static final int FIRST_ORDER_PART_NUMBER = 1;

    private final OrderModelRepository orderModelRepository;
    private final OrderLineQuantityModelRepository orderLineQuantityModelRepository;

    /**
     * Returns an entity from cache by the given order id.
     *
     * @param orderId searched order id.
     * @return null if there is no entity for the given order id
     */
    public OrderModel findOrderModel(String orderId) {
        return orderModelRepository.findById(orderId).get();
    }

    /**
     * Full scans for orders along with their order line quantities in the cache.
     *
     * @return an iterable providing orders as its elements.
     */
    public Iterable<OrderModel> findAllOrderModels() {
        return orderModelRepository.findAll();
    }

    /**
     * Saves an entity to the cache in a transaction when such is available.
     *
     * @param orderModel order model to be saved.
     */
    public OrderModel saveOrderModel(OrderModel orderModel) {
        return orderModelRepository.save(orderModel);
    }

    /**
     * Delete an entity from the cache in a transaction when such is available.
     *
     * @param orderModel order model to be deleted.
     */
    public void deleteOrderModel(OrderModel orderModel) {
        orderModelRepository.delete(orderModel);

        log.info("Order (ID: {}) is deleted", orderModel.getOrderId());
    }

    /**
     * Updates order lines status in the cache in a transaction when such is available.
     *
     * @param orderLines - order lines to be updated.
     * @param status - new status for the order lines.
     */
    public void updateOrderLinesStatus(List<OrderLineQuantityModel> orderLines, OrderLineQuantityStatus status) {
        orderLines.forEach(ol -> ol.setStatus(status));

        saveOrderLines(orderLines);
    }

    /**
     * Saves order lines in the cache in a transaction when such is available.
     *
     * @param orderLines order lines to be saved.
     */
    public void saveOrderLines(List<OrderLineQuantityModel> orderLines) {
        orderLineQuantityModelRepository.saveAll(orderLines);

        for (OrderLineQuantityModel ol : orderLines) {
            log.info("Order line (EAN: {}) is updated", ol.getEan());
        }
    }

    private void assignOrderPartsNumbers(Map<String, List<List<OrderLineQuantityModel>>> orderParts) {
        PrimitiveIterator.OfInt orderPartNumbers = IntStream.range(FIRST_ORDER_PART_NUMBER, Integer.MAX_VALUE).iterator();
        orderParts.values()
                .stream()
                .flatMap(Collection::stream)
                .forEach(part -> {
                    int partNumber = orderPartNumbers.nextInt();
                    part.forEach(line -> line.setOrderPartNumber(partNumber));
                });
    }

    @Generated // can't be covered
    private void ensureFulfillmentNodeAssigned(OrderModel orderModel) {
        List<OrderLineQuantityModel> emptyOrderLines = orderModel.getOrderLineQuantities()
                .stream()
                .filter(ol -> StringUtils.isBlank(ol.getFulfillmentNode()))
                .collect(Collectors.toList());
        if (!emptyOrderLines.isEmpty()) {
            // can't cover this line
            throw new AssertionError("Order lines left unassigned " + emptyOrderLines);
        }
    }

    private void assignTotalOrderParts(OrderModel orderModel) {
        int totalOrderParts = (int) orderModel.getOrderLineQuantities()
                                              .stream()
                                              .map(OrderLineQuantityModel::getOrderPartNumber)
                                              .distinct()
                                              .count();

        orderModel.getOrderLineQuantities()
                  .forEach(orderLine -> orderLine.setTotalOrderParts(totalOrderParts));
    }

    /**
     * Update order line objects according to the chosen order parts and assigned warehouses.
     * @param orderModel order lines to update
     * @param partAssignment order parts and their assigned warehouses
     */
    public void assignOrderPartFulfillmentNodes(OrderModel orderModel, Map<String, List<List<OrderLineQuantityModel>>> partAssignment) {
        // set fulfillment node to order lines
        partAssignment.forEach((node, parts) -> parts.stream()
                .flatMap(Collection::stream)
                .forEach(line -> line.setFulfillmentNode(node)));

        assignOrderPartsNumbers(partAssignment);

        ensureFulfillmentNodeAssigned(orderModel);

        assignTotalOrderParts(orderModel);
    }
}
