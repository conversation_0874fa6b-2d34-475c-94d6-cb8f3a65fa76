package com.bestseller.services.orderrouting.service.idempotency;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartRejected.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartRejected.OrderPartRejected;
import com.bestseller.services.orderrouting.metric.KafkaMessageOperation;
import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.model.OrderLineQuantityStatus;
import com.bestseller.services.orderrouting.model.OrderModel;
import com.bestseller.services.orderrouting.service.CancellationReason;
import com.bestseller.services.orderrouting.service.OrderService;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * Performs checks to ensure OrderPartRejected message is idempotent.
 */
@Service
@Slf4j
public class OrderPartRejectedIdempotencyService extends AbstractMessageIdempotencyService<OrderPartRejected> {
    private OrderService orderService;

    /**
     * Default constructor.
     *
     * @param meterRegistry meterRegistry.
     * @param orderService the default {@link OrderService}.
     */
    public OrderPartRejectedIdempotencyService(MeterRegistry meterRegistry, OrderService orderService) {
        super(meterRegistry);
        this.orderService = orderService;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    protected boolean isDuplicateMessage(final OrderPartRejected message) {
        // Concrete implementation for OrderPartRejected message idempotency check.
        final String orderId = message.getOrderId();
        log.debug("Checking OrderPartRejected message for order {} for idempotency...", orderId);

        final OrderModel orderModel = orderService.findOrderModel(orderId);
        if (orderModel == null) {
            log.warn("OrderPartRejected message for order {} is received but no order is found. Previous "
                                     + "message OrderPartRejected may have deleted it", orderId);
            return true;
        }

        for (OrderLine orderLine : message.getOrderLines()) {
            Optional<OrderLineQuantityModel> orderLineQuantityModelFound = findOrderLineQuantityModel(orderModel, orderLine);

            if (orderLineQuantityModelFound.isEmpty()) {
                log.warn("OrderPartRejected message for order: {} is missing at least one order line with ean: {} and cancel reason: {}",
                        orderId, orderLine.getEan(), orderLine.getCancelReason());
                return true;
            }

            // If the order is a test order it shouldn't check status.
            if (orderModel.getIsTest() != null && orderModel.getIsTest()) {
                log.warn("OrderPartRejected message for order: {} is test and skipping status check for "
                                + "idempotency for ean: {} and cancel reason: {}",
                        orderId, orderLine.getEan(), orderLine.getCancelReason());
                continue;
            }

            OrderLineQuantityModel orderLineQuantityModel = orderLineQuantityModelFound.get();

            if (isOrderLineQuantityModelCancelled(orderLineQuantityModel)
                    || hasOrderLineRejected(orderLineQuantityModel, orderLine)) {
                log.warn("OrderPartRejected message for order: {} is duplicate, order line with ean: {} has been cancelled already.",
                        orderId, orderLine.getEan());
                return true;
            }

        }

        log.debug("OrderPartRejected message for order {} is new.", orderId);
        return false;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    protected Tags provideEventData(final OrderPartRejected message) {
        return Tags.of(ORDER_ID, message.getOrderId());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    protected KafkaMessageOperation provideEventName() {
        return KafkaMessageOperation.DUPLICATE_ORDER_PART_REJECTED_MESSAGE;
    }

    private boolean hasOrderLineRejected(OrderLineQuantityModel orderLineQuantityModel, OrderLine orderLine) {
        //Checks if order line is cancelled again with 'ITEM_NOT_AVAILABLE' reason after
        // rejection from STORE -> this is message is not idempotent.
        return hasOrderLineStateOfRoutedAndRejectedByStore(orderLineQuantityModel)
                && !CancellationReason.ITEM_NOT_AVAILABLE.getReason().equals(orderLine.getCancelReason());
    }

    private boolean isOrderLineQuantityModelCancelled(OrderLineQuantityModel orderLineQuantityModel) {
        // If there is order line model for this ean which is not cancelled -> this is valid ean from not
        // consumed message and it's not idempotent, otherwise it's idempotent.
        return orderLineQuantityModel.getStatus().equals(OrderLineQuantityStatus.CANCELLED);
    }

    private boolean hasOrderLineStateOfRoutedAndRejectedByStore(OrderLineQuantityModel orderLineQuantityModel) {
        // Checks if order line has previous state of 'Routed' and a Cancel reason of 'STORE REJECT';
        // indicating cancellation from store after it was routed and a previous store rejection has occurred
        return CancellationReason.STORE_REJECTION.equals(orderLineQuantityModel.getCancelReason())
                && orderLineQuantityModel.getStatus().equals(OrderLineQuantityStatus.ROUTED);
    }

    private Optional<OrderLineQuantityModel> findOrderLineQuantityModel(OrderModel orderModel, OrderLine orderLine) {
        return orderModel.getOrderLineQuantities()
                .stream()
                .filter(olqm -> olqm.getEan().equalsIgnoreCase(orderLine.getEan()))
                .findAny();
    }
}
