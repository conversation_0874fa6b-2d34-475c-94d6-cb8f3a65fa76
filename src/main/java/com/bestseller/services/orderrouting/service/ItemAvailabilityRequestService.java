package com.bestseller.services.orderrouting.service;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailabilityRequest;
import com.bestseller.services.orderrouting.model.OrderModel;
import lombok.RequiredArgsConstructor;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.Optional;

/**
 * Service to find an order and convert it to a valid {@link ItemAvailabilityRequest}.
 */
@Service
@RequiredArgsConstructor
public class ItemAvailabilityRequestService {

    private final OrderService orderService;

    private final Converter<OrderModel, ItemAvailabilityRequest> itemAvailabilityRequestConverter;

    /**
     * Finds an order and if found converts it to a valid {@link ItemAvailabilityRequest}.
     *
     * @param orderId order id to find
     * @return the item availability request or {@literal Optional#empty()} if none found
     */
    @Transactional
    public Optional<ItemAvailabilityRequest> findAndConvert(String orderId) {
        Optional<OrderModel> optionalOrderModel = Optional.ofNullable(orderService.findOrderModel(orderId));

        // Convert order and send item availability request.
        return optionalOrderModel.map(itemAvailabilityRequestConverter::convert);
    }
}
