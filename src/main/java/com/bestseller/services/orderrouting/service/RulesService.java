package com.bestseller.services.orderrouting.service;

import lombok.AllArgsConstructor;
import org.jeasy.rules.api.Facts;
import org.jeasy.rules.api.Rules;
import org.jeasy.rules.api.RulesEngine;
import org.springframework.stereotype.Service;

/**
 * Rule engine service wrapper for EasyRules framework.
 */
@Service
@AllArgsConstructor
public class RulesService {

    private Rules orderPlacingRules;
    private Rules orderRoutingRules;
    private Rules orderPartRejectedRules;
    private RulesEngine defaultRulesEngine;

    /**
     * Executing order placing rules with given facts ({@link Facts}).
     *
     * @param facts facts used for firing the engine.
     */
    public void executeOrderPlacingRules(final Facts facts) {
        defaultRulesEngine.fire(orderPlacingRules, facts);
    }

    /**
     * Executing order routing rules with given facts ({@link Facts}).
     *
     * @param facts facts used for firing the engine.
     */
    public void executeOrderRoutingRules(final Facts facts) {
        defaultRulesEngine.fire(orderRoutingRules, facts);
    }

    /**
     * Executing order routing rules with given facts ({@link Facts}).
     *
     * @param facts information available for the rules
     */
    public void executeOrderPartRejectedRules(Facts facts) {
        defaultRulesEngine.fire(orderPartRejectedRules, facts);
    }
}
