package com.bestseller.services.orderrouting.service.idempotency;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailabilityResponse;
import com.bestseller.services.orderrouting.metric.KafkaMessageOperation;
import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.model.OrderLineQuantityStatus;
import com.bestseller.services.orderrouting.model.OrderModel;
import com.bestseller.services.orderrouting.repository.OrderModelRepository;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * Performs checks to ensure ItemAvailabilityResponseConsumer is idempotent.
 */
@Service
@Slf4j
public class ItemAvailabilityResponseIdempotencyService extends
        AbstractMessageIdempotencyService<ItemAvailabilityResponse> {

    private final OrderModelRepository orderModelRepository;

    /**
     * ItemAvailabilityResponseIdempotencyServiceImpl constructor.
     *
     * @param meterRegistry        meterRegistry
     * @param orderModelRepository to get the ordermodel information
     */
    public ItemAvailabilityResponseIdempotencyService(MeterRegistry meterRegistry, OrderModelRepository orderModelRepository) {
        super(meterRegistry);
        this.orderModelRepository = orderModelRepository;
    }

    /**
     * Check if item availabilty response is duplicate, this message should always include placed lines.
     * If there are no placed orderlines, there is a sign that the message is problematic or duplicate.
     *
     * @param message item availabilty response message to be checked for duplication
     */
    @Override
    protected boolean isDuplicateMessage(final ItemAvailabilityResponse message) {
        log.debug("Checking for item availability response message duplication ");
        final Optional<OrderModel> orderModel = orderModelRepository.findById(message.getCorrelationId());
        if (orderModel.isEmpty()) {
            log.warn("Order model with order id {} is not found in the cache!", message.getCorrelationId());
            return false;
        }

        return orderModel.get()
                .getOrderLineQuantities()
                .stream()
                .noneMatch(this::isPlacedOrWaitingForStock);
    }

    private boolean isPlacedOrWaitingForStock(OrderLineQuantityModel orderLine) {
        return orderLine.getStatus().equals(OrderLineQuantityStatus.PLACED)
                || orderLine.getStatus().equals(OrderLineQuantityStatus.WAITING_FOR_STOCK);
    }

    /**
     * {@inheritDoc}
     * @return
     */
    @Override
    protected Tags provideEventData(final ItemAvailabilityResponse message) {
        return Tags.of(ORDER_ID, message.getCorrelationId());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    protected KafkaMessageOperation provideEventName() {
        return KafkaMessageOperation.DUPLICATE_ITEM_AVAILABILITY_RESPONSE_MESSAGE;
    }
}
