package com.bestseller.services.orderrouting.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * Feature model to persist toggle feature information in the Aurora.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Builder
@Table(name = "Features")
public class FeatureModel {

    @Id
    private String name;

    private String strategyId;

    private Boolean enabled;
}
