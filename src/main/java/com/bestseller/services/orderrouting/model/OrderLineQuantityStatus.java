package com.bestseller.services.orderrouting.model;

/**
 * Lifecycle of order line quantities requested for fulfillment.
 */
public enum OrderLineQuantityStatus {
    /**
     * Initial order line status. Order line has just been requested and placed in the cache.
     */
    PLACED,
    /**
     * Waiting for stock to be available at the warehouse.
     */
    WAITING_FOR_STOCK,
    /**
     * Stock availability has been reported for this order line.
     */
    ROUTING,
    /**
     * Fulfillment node and order part number has been assigned. OrderPart message has been published.
     */
    ROUTED,
    /**
     * Fulfillment connector receives notification from warehouse that order lines are dispatched.
     */
    DISPATCHED,
    /**
     * No stock availability has been reported for this order line.
     */
    CANCELLED,
    /**
     * Fulfillment connector has received unable to fulfil from warehouse.
     */
    RESUBMIT,
    /**
     * Order line status for records that are used for fulfillment advice.
     */
    ADVISED;
}
