package com.bestseller.services.orderrouting.model;

import com.bestseller.services.orderrouting.service.CancellationReason;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Id;
import javax.persistence.IdClass;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;

import static java.lang.String.format;

/**
 * Order line quantity model to persist split order line information in the Aurora.
 */
@Data
@NoArgsConstructor
@Entity
@AllArgsConstructor
@Builder
@Table(name = "OrderLineQuantity")
@IdClass(OrderLineQuantityModel.PrimaryKey.class)
public class OrderLineQuantityModel {

    @Id // Aurora primary key column
    private String orderId;

    private String ean;

    private String productName;

    @Id // Aurora primary key column
    private Integer lineNumber;

    private Integer quantity;

    @Enumerated(EnumType.STRING)
    private OrderLineQuantityStatus status = OrderLineQuantityStatus.PLACED;

    @Enumerated(EnumType.STRING)
    private CancellationReason cancelReason;

    private String fulfillmentNode;

    private Integer orderPartNumber;

    private Integer totalOrderParts;

    private BigDecimal retailPrice;

    private BigDecimal discountValue;

    private BigDecimal taxPercentage;

    private Boolean isGiftItem;

    private String partnerReference;

    private String brand;

    public PrimaryKey getId() {
        return PrimaryKey.builder()
            .orderId(getOrderId())
            .lineNumber(getLineNumber())
            .build();
    }

    public void setId(PrimaryKey primaryKey) {
        setOrderId(primaryKey.getOrderId());
        setLineNumber(primaryKey.getLineNumber());
    }

    @Override
    public String toString() {
        return format("{%s EAN %s × %s}", status, ean, quantity);
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class PrimaryKey implements Serializable {

        private String orderId;

        private Integer lineNumber;

    }
}

