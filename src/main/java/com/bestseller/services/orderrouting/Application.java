package com.bestseller.services.orderrouting;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.annotation.Bean;

import java.time.Clock;

/**
 * {@link SpringBootApplication} for OrderRoutingService project.
 */
@SpringBootApplication
public class Application extends SpringBootServletInitializer {
    /**
     * The main method for this application. Will run the Spring Application.
     * @param args
     */
    public static void main(final String[] args) {
        SpringApplication.run(Application.class, args);
    }

    /**
     * Configures the application.
     * @param application
     * @return
     */
    @Override
    protected SpringApplicationBuilder configure(final SpringApplicationBuilder application) {
        return application.sources(Application.class);
    }

    /**
     * UTC clock.
     */
    @Bean
    public Clock utcClock() {
        return Clock.systemUTC();
    }
}
