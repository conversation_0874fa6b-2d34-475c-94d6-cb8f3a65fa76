package com.bestseller.services.orderrouting.converter;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsRouted.OrderPartsRouted;
import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.model.OrderLineQuantityStatus;
import com.bestseller.services.orderrouting.model.OrderModel;
import com.bestseller.services.orderrouting.service.EntityType;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.modelmapper.convention.MatchingStrategies;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.TreeMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Converter from {@link OrderModel} to a {@link List} of {@link OrderPartsRouted}.
 * Converts each order part to a separate message.
 */
@Component
@AllArgsConstructor
@Slf4j
public class OrderPartsRoutedConverter implements Converter<OrderModel, List<OrderPartsRouted>> {
    private static final ZoneId DEFAULT_TIME_ZONE = ZoneId.of("UTC");

    private static final org.modelmapper.Converter<Instant, ZonedDateTime> DATE_CONVERTER = cxt ->
            cxt.getSource() == null ? null : cxt.getSource().atZone(DEFAULT_TIME_ZONE);

    private final ModelMapper modelMapper = createModelMapper();

    private static ModelMapper createModelMapper() {
        ModelMapper modelMapper = new ModelMapper();
        modelMapper.getConfiguration().setMatchingStrategy(MatchingStrategies.LOOSE);

        // allow totalOrderParts to lack a definite match, it's going to be skipped after all
        modelMapper.getConfiguration().setAmbiguityIgnored(true);

        modelMapper.createTypeMap(OrderLineQuantityModel.class, OrderLine.class);
        modelMapper.createTypeMap(OrderModel.class, OrderPartsRouted.class)
                .addMappings(mapping -> {
                    mapping.skip(OrderPartsRouted::setTotalOrderParts);
                    mapping.skip(OrderPartsRouted::setFulfillmentNode);
                    mapping.skip(OrderPartsRouted::setOrderPartNumber);
                    mapping.skip(OrderPartsRouted::setOrderLines);
                    mapping.using(DATE_CONVERTER).map(OrderModel::getPlacedDate, OrderPartsRouted::setPlacedDate);
                    mapping.using(DATE_CONVERTER).<ZonedDateTime>map(OrderModel::getOrderCreationDate,
                            (orderPartsRouted, value) -> orderPartsRouted.getOrderDetails().setOrderCreationDate(value));
                });
        return modelMapper;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<OrderPartsRouted> convert(final OrderModel source) {
        Map<Integer, String> orderPartNumbers = new HashMap<>();

        source.getOrderLineQuantities().stream()
              .filter(q -> isSubmittable(q))
              .forEach(q -> orderPartNumbers.putIfAbsent(q.getOrderPartNumber(), q.getFulfillmentNode()));

        filterAdditionalInformation(source);

        log.debug("{} order part numbers -> fulfillment nodes found in order", orderPartNumbers);

        // Group order lines by their fulfillment node and create an order part message for each group
        return source.getOrderLineQuantities().stream()
                     .filter(this::isSubmittable)
                     .collect(
                             Collectors.groupingBy(
                                     OrderLineQuantityModel::getOrderPartNumber,
                                     TreeMap::new,
                                     Collectors.toList()
                             ))
                     .entrySet()
                     .stream()
                     .map(orderPartMapper(source, orderPartNumbers))
                     .collect(Collectors.toList());
    }

    private boolean isSubmittable(final OrderLineQuantityModel olqm) {
        return olqm.getStatus().equals(OrderLineQuantityStatus.ROUTING)
                || olqm.getStatus().equals(OrderLineQuantityStatus.RESUBMIT);
    }

    /**
     * Returns a function which maps a list of {@link OrderLineQuantityModel} entities to an {@link OrderPartsRouted}
     * message. Order, customer and shipping information is provided by the enclosed {@link OrderModel} parameter.
     *
     * @param orderModel
     * @param orderPartNumbers
     * @return
     */
    private Function<Map.Entry<Integer, List<OrderLineQuantityModel>>, OrderPartsRouted> orderPartMapper(
            OrderModel orderModel, Map<Integer, String> orderPartNumbers) {

        return entry -> modelMapper
                .map(orderModel, OrderPartsRouted.class)
                .withFulfillmentNode(orderPartNumbers.get(entry.getKey()))
                .withOrderPartNumber(entry.getKey())
                .withTotalOrderParts(orderPartNumbers.size())
                .withOrderLines(
                        entry.getValue().stream()
                             .map(q -> modelMapper.map(q, OrderLine.class))
                             .collect(Collectors.toList())
                );
    }

    private void filterAdditionalInformation(final OrderModel source) {
        if (source.getAdditionalOrderInformation() != null) {
            source.setAdditionalOrderInformation(
                source.getAdditionalOrderInformation()
                    .stream()
                    .filter(Objects::nonNull)
                    .filter(additionalInformation ->
                        EntityType.SHIPPING_INFORMATION.equals(additionalInformation.getEntityType()))
                    .collect(Collectors.toList())
            );
        }
    }
}
