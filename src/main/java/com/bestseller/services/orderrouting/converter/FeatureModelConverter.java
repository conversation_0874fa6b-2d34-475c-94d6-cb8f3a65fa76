package com.bestseller.services.orderrouting.converter;

import com.bestseller.services.orderrouting.feature.toggles.ORSFeatures;
import com.bestseller.services.orderrouting.model.FeatureModel;
import org.modelmapper.ModelMapper;
import org.modelmapper.TypeMap;
import org.modelmapper.convention.MatchingStrategies;
import org.modelmapper.spi.DestinationSetter;
import org.springframework.core.convert.converter.Converter;
import org.togglz.core.repository.FeatureState;

/**
 * Implementation of {@link Converter} which converts {@link FeatureModel} to {@link FeatureState}. Uses {@link ModelMapper}, {@link TypeMap} and
 * {@link MatchingStrategies} from ModelMapper Java library.
 *
 * @see Converter
 * @see ModelMapper
 * @see TypeMap
 * @see MatchingStrategies
 */
public class FeatureModelConverter implements Converter<FeatureModel, FeatureState> {

    private final ModelMapper modelMapper = createModelMapper();

    private static ModelMapper createModelMapper() {
        ModelMapper modelMapper = new ModelMapper();
        modelMapper.getConfiguration().setMatchingStrategy(MatchingStrategies.LOOSE);

        TypeMap<FeatureModel, FeatureState> featureStateTypeMap = modelMapper.createTypeMap(FeatureModel.class, FeatureState.class);
        featureStateTypeMap.setProvider(src -> new FeatureState(ORSFeatures.valueOf(((FeatureModel) src.getSource()).getName())));
        featureStateTypeMap.addMapping(FeatureModel::getStrategyId, FeatureState::setStrategyId);

        // This is a workaround for ModelMapper's Java 13 issue.
        //
        // The problem is in TypeTools' `TypeResolver` class which is shipped with ModelMapper.
        // Here's the gist of the problematic piece of code
        //
        //     static {
        //         try {
        //             AccessibleObject.class.getDeclaredField("override")
        //             RESOLVES_LAMBDAS = true;
        //         } catch (Exception ignore) {
        //         }
        //     }
        //
        // `getDeclaredField` will result in `NoSuchPropertyException` on Java 13.
        // So λ resolution fails to get enabled during the initialization of the class.
        // Which is communicated to us by `addMapping` throwing `NullPointerException` when invoked with a λ.
        DestinationSetter<FeatureState, Boolean> featureStateSetEnabled = new DestinationSetter<>() {
            @Override
            public void accept(FeatureState destination, Boolean value) {
                destination.setEnabled(value);
            }
        };
        featureStateTypeMap.addMapping(FeatureModel::getEnabled, featureStateSetEnabled);

        return modelMapper;
    }

    /**
     * Converts FeatureModel to FeatureState.
     * @param source the FeatureModel source to be converted
     * @return FeatureState converted from the given FeatureModel
     */
    @Override
    public FeatureState convert(FeatureModel source) {
        return modelMapper.map(source, FeatureState.class);
    }

}
