package com.bestseller.services.orderrouting.converter;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailabilityRequest;
import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.model.OrderModel;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Converts {@link OrderModel} to {@link ItemAvailabilityRequest}.
 */
@Component
@AllArgsConstructor
@Slf4j
public class ItemAvailabilityRequestConverter implements Converter<OrderModel, ItemAvailabilityRequest> {

    /**
     * {@inheritDoc}
     */
    @Override
    public ItemAvailabilityRequest convert(final OrderModel orderModel) {
        List<String> eans = orderModel.getOrderLineQuantities().stream().map(OrderLineQuantityModel::getEan)
                                               .distinct()
                                               .collect(Collectors.toList());

        // Request the availability of the EANs in this order.
        ItemAvailabilityRequest itemAvailabilityRequest = new ItemAvailabilityRequest()
                .withCorrelationId(orderModel.getOrderId())
                .withEans(eans);
        log.debug("Created ItemAvailabilityRequest message: {}", itemAvailabilityRequest);

        return itemAvailabilityRequest;
    }
}
