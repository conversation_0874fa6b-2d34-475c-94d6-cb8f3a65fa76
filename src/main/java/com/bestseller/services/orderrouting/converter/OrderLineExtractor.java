package com.bestseller.services.orderrouting.converter;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartRejected.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartRejected.OrderPartRejected;
import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.model.OrderModel;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Converts the elements from a {@link OrderPartRejected} to a {@link OrderLineQuantityModel} instance.
 */
@Component
@AllArgsConstructor
public class OrderLineExtractor {
    /**
     * Extracts all the {@link OrderLineQuantityModel} instances from the repository based upon the
     * {@link OrderPartRejected} message.
     * @param order The order coming from the repository.
     * @param message The message as consumed.
     * @return list of order line quantity models
     */
    public List<OrderLineQuantityModel> extractOrderLinesFromMessage(OrderModel order, OrderPartRejected message) {
        Set<String> eans = message.getOrderLines().stream().map(OrderLine::getEan).collect(Collectors.toSet());

        // If there is order return filtered order lines as list.
        return order.getOrderLineQuantities()
                .stream()
                .filter(olq -> eans.contains(olq.getEan()))
                .collect(Collectors.toList());
    }
}
