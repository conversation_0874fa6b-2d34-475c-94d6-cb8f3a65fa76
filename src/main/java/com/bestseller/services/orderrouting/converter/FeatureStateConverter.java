package com.bestseller.services.orderrouting.converter;

import com.bestseller.services.orderrouting.model.FeatureModel;
import org.modelmapper.ModelMapper;
import org.modelmapper.TypeMap;
import org.modelmapper.convention.MatchingStrategies;
import org.springframework.core.convert.converter.Converter;
import org.togglz.core.repository.FeatureState;

/**
 * Implementation of {@link Converter} which converts {@link FeatureState} to {@link FeatureModel}. Uses {@link ModelMapper}, {@link TypeMap} and
 * {@link MatchingStrategies} from ModelMapper Java library.
 *
 * @see Converter
 * @see ModelMapper
 * @see TypeMap
 * @see MatchingStrategies
 */
public class FeatureStateConverter implements Converter<FeatureState, FeatureModel> {

    private final ModelMapper modelMapper = createModelMapper();

    private static ModelMapper createModelMapper() {
        ModelMapper modelMapper = new ModelMapper();
        modelMapper.getConfiguration().setMatchingStrategy(MatchingStrategies.LOOSE);

        TypeMap<FeatureState, FeatureModel> featureStateTypeMap = modelMapper.createTypeMap(FeatureState.class, FeatureModel.class);
        featureStateTypeMap.addMapping(src -> src.getFeature().name(), FeatureModel::setName);
        featureStateTypeMap.addMapping(FeatureState::getStrategyId, FeatureModel::setStrategyId);
        featureStateTypeMap.addMapping(FeatureState::isEnabled, FeatureModel::setEnabled);
        return modelMapper;
    }

    /**
     * Converts FeatureState to FeatureModel.
     * @param source input value to be converted
     * @return conversion result
     */
    @Override
    public FeatureModel convert(FeatureState source) {
        return modelMapper.map(source, FeatureModel.class);
    }

}
