package com.bestseller.services.orderrouting.converter;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartRejected.OrderPartRejected;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartsCancelled.OrderPartsCancelled;
import lombok.AllArgsConstructor;
import org.modelmapper.ModelMapper;
import org.modelmapper.convention.MatchingStrategies;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

/**
 * Converter from {@link OrderPartRejected} to a {@link OrderPartsCancelled}.
 * Converts each order part cancelledge.
 */
@Component
@AllArgsConstructor
public class OrderPartsCancelledConverter implements Converter<OrderPartRejected, OrderPartsCancelled> {
    private final ModelMapper modelMapper = createModelMapper();

    /**
     * {@inheritDoc}
     */
    @Override
    public OrderPartsCancelled convert(OrderPartRejected source) {
        return modelMapper.map(source, OrderPartsCancelled.class);
    }

    private static ModelMapper createModelMapper() {
        ModelMapper modelMapper = new ModelMapper();
        modelMapper.getConfiguration().setMatchingStrategy(MatchingStrategies.LOOSE);

        modelMapper.createTypeMap(OrderPartRejected.class, OrderPartsCancelled.class);

        return modelMapper;
    }
}
