package com.bestseller.services.orderrouting.converter;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsCreated.OrderPart;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsCreated.OrderPartsCreated;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsRouted.OrderPartsRouted;
import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.model.OrderLineQuantityStatus;
import com.bestseller.services.orderrouting.model.OrderModel;
import com.bestseller.services.orderrouting.service.EntityType;
import lombok.AllArgsConstructor;
import org.modelmapper.ModelMapper;
import org.modelmapper.convention.MatchingStrategies;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.TreeMap;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

/**
 * Converter from {@link OrderModel} to a {@link List} of {@link OrderPartsCreated}.
 * Converts each order part to a separate message.
 */
@Component
@AllArgsConstructor
public class OrderPartsCreatedConverter implements Converter<OrderModel, List<OrderPartsCreated>> {
    private static final ZoneId DEFAULT_TIME_ZONE = ZoneId.of("UTC");

    private static final org.modelmapper.Converter<Instant, ZonedDateTime> DATE_CONVERTER = cxt ->
            cxt.getSource() == null ? null : cxt.getSource().atZone(DEFAULT_TIME_ZONE);

    private final ModelMapper modelMapper = createModelMapper();

    private static ModelMapper createModelMapper() {
        ModelMapper modelMapper = new ModelMapper();
        modelMapper.getConfiguration().setMatchingStrategy(MatchingStrategies.LOOSE);

        // allow totalOrderParts to lack a definite match, it's going to be skipped after all
        modelMapper.getConfiguration().setAmbiguityIgnored(true);

        modelMapper.createTypeMap(OrderLineQuantityModel.class, OrderPart.class);
        modelMapper.createTypeMap(OrderLineQuantityModel.class, OrderLine.class);
        modelMapper.createTypeMap(OrderModel.class, OrderPartsCreated.class)
                .addMappings(mapping -> {
                    mapping.using(DATE_CONVERTER).map(OrderModel::getPlacedDate, OrderPartsCreated::setPlacedDate);
                    mapping.using(DATE_CONVERTER).<ZonedDateTime>map(OrderModel::getOrderCreationDate,
                            (orderPartsCreated, value) -> orderPartsCreated.getOrderDetails().setOrderCreationDate(value));
                });
        return modelMapper;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<OrderPartsCreated> convert(final OrderModel source) {
        filterAdditionalInformation(source);
        // Group order lines by their fulfillment node and create an order part message for each group
        return source.getOrderLineQuantities().stream()
                .filter(this::isSubmittable)
                .collect(
                        groupingBy(
                                OrderLineQuantityModel::getFulfillmentNode,
                                TreeMap::new,
                                Collectors.toList()
                        ))
                .entrySet()
                .stream()
                .map(orderPartMapper(source))
                .collect(Collectors.toList());
    }

    private boolean isSubmittable(final OrderLineQuantityModel olqm) {
        return olqm.getStatus().equals(OrderLineQuantityStatus.ROUTING)
                || olqm.getStatus().equals(OrderLineQuantityStatus.RESUBMIT);
    }

    /**
     * Returns a function which maps a list of {@link OrderLineQuantityModel} entities to an {@link OrderPartsRouted}
     * message. Order, customer and shipping information is provided by the enclosed {@link OrderModel} parameter.
     *
     * @param orderModel
     * @return
     */
    private Function<Map.Entry<String, List<OrderLineQuantityModel>>, OrderPartsCreated> orderPartMapper(OrderModel orderModel) {
        return entry -> modelMapper
                .map(orderModel, OrderPartsCreated.class)
                .withFulfillmentNode(entry.getValue().get(0).getFulfillmentNode())
                .withOrderParts(
                        entry.getValue().stream()
                                .collect(groupingBy(OrderLineQuantityModel::getOrderPartNumber))
                                .entrySet()
                                .stream()
                                .map(op ->
                                        modelMapper.map(op, OrderPart.class)
                                                .withOrderPartNumber(op.getKey())
                                                .withOrderLines(
                                                        entry.getValue().stream()
                                                                .filter(ol -> ol.getOrderPartNumber().equals(op.getKey()))
                                                                .map(ol -> modelMapper.map(ol, OrderLine.class))
                                                                .collect(Collectors.toList())
                                                )
                                )
                                .collect(Collectors.toList())
                );
    }

    private void filterAdditionalInformation(final OrderModel source) {
        if (source.getAdditionalOrderInformation() != null) {
            source.setAdditionalOrderInformation(
                source.getAdditionalOrderInformation()
                    .stream()
                    .filter(Objects::nonNull)
                    .filter(additionalInformation ->
                        EntityType.SHIPPING_INFORMATION.equals(additionalInformation.getEntityType()))
                    .collect(Collectors.toList())
            );
        }
    }
}
