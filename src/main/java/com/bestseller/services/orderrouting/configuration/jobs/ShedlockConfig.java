package com.bestseller.services.orderrouting.configuration.jobs;

import net.javacrumbs.shedlock.core.LockProvider;
import net.javacrumbs.shedlock.provider.jdbctemplate.JdbcTemplateLockProvider;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;

/**
 * Configuration for Shedlock to prevent running the same task on multiple instances.
 */
@Configuration
public class ShedlockConfig {
    /**
     * Configures the {@link LockProvider} bean.
     * @param dataSource the given data source.
     * @return LockProvider.
     */
    @Bean
    public LockProvider lockProvider(DataSource dataSource) {
        return new JdbcTemplateLockProvider(dataSource);
    }
}
