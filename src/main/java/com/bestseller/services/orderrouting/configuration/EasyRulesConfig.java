package com.bestseller.services.orderrouting.configuration;

import com.bestseller.services.orderrouting.rules.flows.OrderPartRejectingFlow;
import com.bestseller.services.orderrouting.rules.flows.OrderPlacingFlow;
import com.bestseller.services.orderrouting.rules.flows.OrderRoutingFlow;
import org.jeasy.rules.api.RuleListener;
import org.jeasy.rules.api.Rules;
import org.jeasy.rules.core.DefaultRulesEngine;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * Rules and rule engine.
 */
@Configuration
public class EasyRulesConfig {

    /**
     * Rules used for placing orders.
     *
     * @param list list of all beans implementing {@link OrderPlacingFlow}.
     * @return {@link Rules} from EasyRules framework.
     */
    @Bean
    public Rules orderPlacingRules(List<OrderPlacingFlow> list) {
        return new Rules(list.toArray());
    }

    /**
     * Rules used for routing of orders.
     *
     * @param list list of all beans implementing {@link OrderRoutingFlow}.
     * @return {@link Rules} from EasyRules framework.
     */
    @Bean
    public Rules orderRoutingRules(List<OrderRoutingFlow> list) {
        return new Rules(list.toArray());
    }

    /**
     * Rules used for handling store rejections.
     *
     * @param list all rules that are part of the cancellation flow
     * @return {@link Rules} from EasyRules framework.
     */
    @Bean
    public Rules orderPartRejectedRules(List<OrderPartRejectingFlow> list) {
        return new Rules(list.toArray());
    }

    /**
     * Default rules engine for routing flow. Breaks rule chain execution on first failed rule.
     *
     * @param breakChainRuleListener {@link RuleListener} for breaking the default chain if a rule annotated with BreakChain has been
     *                                                   evaluated to true.
     * @return default rules engine.
     */
    @Bean
    public DefaultRulesEngine defaultFlowRulesEngine(RuleListener breakChainRuleListener, RuleListener globalRuleListener) {
        DefaultRulesEngine defaultRulesEngine = new DefaultRulesEngine();
        defaultRulesEngine.registerRuleListener(breakChainRuleListener);
        defaultRulesEngine.registerRuleListener(globalRuleListener);
        defaultRulesEngine.getParameters().setSkipOnFirstFailedRule(true);

        return defaultRulesEngine;
    }
}
