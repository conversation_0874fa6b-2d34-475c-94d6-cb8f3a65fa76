package com.bestseller.services.orderrouting.configuration;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.togglz.core.manager.EnumBasedFeatureProvider;
import org.togglz.core.manager.FeatureManager;
import org.togglz.core.manager.FeatureManagerBuilder;
import org.togglz.core.manager.TogglzConfig;
import org.togglz.core.repository.StateRepository;
import org.togglz.core.spi.FeatureProvider;

import com.bestseller.services.orderrouting.converter.FeatureModelConverter;
import com.bestseller.services.orderrouting.converter.FeatureStateConverter;
import com.bestseller.services.orderrouting.feature.toggles.ORSFeatureToggleConfig;
import com.bestseller.services.orderrouting.feature.toggles.ORSFeatures;

/**
 * Feature Toggle configuration file for OrderRoutingService.
 */
@Configuration
public class FeatureToggleConfig {

    /**
     * Creates a FeatureModelConverter for converting FeatureModel to FeatureState.
     * @return the feature model converter
     */
    @Bean
    public FeatureModelConverter featureModelConverter() {
        return new FeatureModelConverter();
    }

    /**
     * Creates FeatureStateConverter for converting FeatureState to FeatureModel.
     * @return the feature state converter
     */
    @Bean
    public FeatureStateConverter featureStateConverter() {
        return new FeatureStateConverter();
    }

    /**
     * Creates a FeatureProvider for the ORS TogglzFeature.
     * @return the feature provider
     */
    @Bean
    @SuppressWarnings("unchecked")
    public FeatureProvider featureProvider() {
        return new EnumBasedFeatureProvider(ORSFeatures.class);
    }

    /**
     * Creates ORS Togglz Configuration.
     * @param stateRepository
     * @param environment
     * @return ORS Togglz Configuration
     */
    @Bean
    public TogglzConfig togglzConfig(StateRepository stateRepository, Environment environment) {
        return new ORSFeatureToggleConfig(stateRepository, environment);
    }

    /**
     * Creates a FeatureManager with a togglz configuration.
     * @param togglzConfig the togglzConfig
     * @return the featureManager
     */
    @Bean
    public FeatureManager featureManager(TogglzConfig togglzConfig) {
        return new FeatureManagerBuilder()
                .togglzConfig(togglzConfig)
                .build();
    }
}
