package com.bestseller.services.orderrouting.configuration.messaging;

import org.springframework.cloud.stream.annotation.Input;
import org.springframework.cloud.stream.annotation.Output;
import org.springframework.messaging.MessageChannel;

/**
 * Channel for {@link OrderCancellingChannels} message.
 */
public interface OrderCancellingChannels {
    String ORDER_PART_REJECTED = "orderPartRejected";
    String ORDER_PARTS_CANCELLED = "orderPartsCancelled";

    /**
     * Gets the ORDER_PART_REJECTED channel.
     *
     * @return MessageChannel
     */
    @Input(ORDER_PART_REJECTED)
    MessageChannel orderPartRejected();

    /**
     * Gets the ORDER_PARTS_CANCELLED channel.
     *
     * @return MessageChannel
     */
    @Output(ORDER_PARTS_CANCELLED)
    MessageChannel orderPartsCancelled();
}
