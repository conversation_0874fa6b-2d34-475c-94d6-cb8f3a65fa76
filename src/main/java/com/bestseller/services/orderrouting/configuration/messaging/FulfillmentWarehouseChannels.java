package com.bestseller.services.orderrouting.configuration.messaging;

import org.springframework.cloud.stream.annotation.Input;
import org.springframework.cloud.stream.annotation.Output;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.SubscribableChannel;

/**
 * Channels for {@link FulfillmentWarehouseChannels} messages.
 */
public interface FulfillmentWarehouseChannels {
    String FULFILLMENT_WAREHOUSE_REQUEST = "fulfillmentWarehouseRequest";
    String FULFILLMENT_WAREHOUSE_RESPONSE = "fulfillmentWarehouseResponse";

    /**
     * Gets the FULFILLMENT_WAREHOUSE_REQUEST channel.
     * @return SubscribableChannel
     */
    @Input(FULFILLMENT_WAREHOUSE_REQUEST)
    MessageChannel fulfillmentWarehouseRequest();

    /**
     * Gets the FULFILLMENT_WAREHOUSE_RESPONSE channel.
     * @return MessageChannel
     */
    @Output(FULFILLMENT_WAREHOUSE_RESPONSE)
    SubscribableChannel fulfillmentWarehouseResponse();
}
