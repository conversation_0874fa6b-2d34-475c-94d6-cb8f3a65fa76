package com.bestseller.services.orderrouting.configuration.micrometer;

import io.micrometer.core.aop.CountedAspect;
import io.micrometer.core.aop.TimedAspect;
import io.micrometer.core.instrument.MeterRegistry;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration for Micrometer.
 */
@Configuration
public class MicrometerConfig {
    /**
     * Registers the aspect for using the @Timed annotation.
     * @param registry the meter registry.
     * @return the aspect.
     */
    @Bean
    public TimedAspect timedAspect(MeterRegistry registry) {
        return new TimedAspect(registry);
    }

    /**
     * Registers the aspect for using the @Counted annotation.
     * @param registry the meter registry.
     * @return the aspect.
     */
    @Bean
    public CountedAspect countedAspect(MeterRegistry registry) {
        return new CountedAspect(registry);
    }
}
