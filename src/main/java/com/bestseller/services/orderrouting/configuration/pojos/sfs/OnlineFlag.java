package com.bestseller.services.orderrouting.configuration.pojos.sfs;

/**
 * Online flag for ship from store configuration.
 */
public enum OnlineFlag {
    /**
     * The store is not available for online orders.
     */
    OFF,

    /**
     * The store is available for online orders (staging).
     */
    STAGING,

    /**
     * The store is available for online orders (production).
     */
    PRODUCTION;
}
