package com.bestseller.services.orderrouting.configuration;

import com.bestseller.services.orderrouting.rules.actions.AvailabilityFulfillmentAction;
import com.bestseller.services.orderrouting.service.OrderService;
import com.bestseller.services.orderrouting.strategy.Fulfillment;
import com.bestseller.services.orderrouting.strategy.StockPerNodeAndSplitFulfillment;
import com.bestseller.services.orderrouting.strategy.StockPerNodeFulfillmentFactory;
import com.bestseller.services.orderrouting.strategy.StorePriorityFulfillment;
import lombok.AllArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * {@link org.jeasy.rules.api.Action}s that {@link org.jeasy.rules.api.Rule}s depend on.
 */
@AllArgsConstructor
@Configuration
public class ActionsConfig {

    private final Warehouses warehouses;
    private final OrderService orderService;

    /**
     * Assigns fulfillment nodes using {@link com.bestseller.services.orderrouting.strategy.StockPerNodeFulfillmentFactory#warehouseFulfillment()}.
     */
    @Bean
    public AvailabilityFulfillmentAction warehouseAction(Fulfillment warehouseFulfillment) {
        return new AvailabilityFulfillmentAction(warehouseFulfillment, orderService, warehouses);
    }

    /**
     * Assigns fulfillment nodes using {@link StockPerNodeAndSplitFulfillment}.
     */
    @Bean
    public AvailabilityFulfillmentAction shipFromStoreBigOrderAction(Fulfillment stockPerNodeAndSplitFulfillment) {
        return new AvailabilityFulfillmentAction(stockPerNodeAndSplitFulfillment, orderService, warehouses);
    }

    /**
     * Assigns fulfillment nodes using {@link StorePriorityFulfillment}.
     */
    @Bean
    public AvailabilityFulfillmentAction shipFromStoreNoSplitAction(Fulfillment storePriorityFulfillment) {
        return new AvailabilityFulfillmentAction(storePriorityFulfillment, orderService, warehouses);
    }

    /**
     * Assign fulfillment nodes using {@link StockPerNodeFulfillmentFactory#shipFromStoreWarehousePriorityFulfillment()}.
     */
    @Bean
    public AvailabilityFulfillmentAction shipFromStoreSoldOutAction(Fulfillment shipFromStoreWarehousePriorityFulfillment) {
        return new AvailabilityFulfillmentAction(shipFromStoreWarehousePriorityFulfillment, orderService, warehouses);
    }

    /**
     * Assign fulfillment nodes using {@link StockPerNodeFulfillmentFactory#shipFromStoreStorePriorityFulfillment()}.
     */
    @Bean
    public AvailabilityFulfillmentAction shipFromStoreSplitAction(Fulfillment shipFromStoreStorePriorityFulfillment) {
        return new AvailabilityFulfillmentAction(shipFromStoreStorePriorityFulfillment, orderService, warehouses);
    }
}
