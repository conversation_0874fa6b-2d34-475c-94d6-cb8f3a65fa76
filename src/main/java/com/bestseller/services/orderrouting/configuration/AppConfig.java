package com.bestseller.services.orderrouting.configuration;

import com.bestseller.services.orderrouting.configuration.messaging.FulfillmentWarehouseChannels;
import com.bestseller.services.orderrouting.configuration.messaging.ItemAvailabilityChannels;
import com.bestseller.services.orderrouting.configuration.messaging.OrderCancellingChannels;
import com.bestseller.services.orderrouting.configuration.messaging.OrderChannels;
import net.javacrumbs.shedlock.spring.annotation.EnableSchedulerLock;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;

/**
 * Default application configuration file for OrderRoutingService.
 */

@Configuration
@EnableAspectJAutoProxy(proxyTargetClass = true)
@EnableSchedulerLock(defaultLockAtMostFor = "5m")
@ComponentScan(basePackages = "com.bestseller.services.orderrouting")
@EnableJpaRepositories(basePackages = "com.bestseller.services.orderrouting.repository")
@EnableTransactionManagement
@EnableWebMvc
@EnableScheduling
@EnableBinding({OrderChannels.class, ItemAvailabilityChannels.class, FulfillmentWarehouseChannels.class, OrderCancellingChannels.class})
@EnableConfigurationProperties(SfsConfig.class)
public class AppConfig {
}
