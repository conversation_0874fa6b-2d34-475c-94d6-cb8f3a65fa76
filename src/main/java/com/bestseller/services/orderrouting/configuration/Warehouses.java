package com.bestseller.services.orderrouting.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Warehouse identifiers.
 */
@Configuration
@ConfigurationProperties("warehouse")
@Data
public class Warehouses {
    private String fulfillmentCenterWest;
    private String fulfillmentCenterEast;
}



