package com.bestseller.services.orderrouting.configuration.messaging;

import org.springframework.cloud.stream.annotation.Input;
import org.springframework.cloud.stream.annotation.Output;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.SubscribableChannel;

/**
 * Channels for OrderForFulfillment message.
 */
public interface OrderChannels {
    String ORDER_FOR_FULFILLMENT = "orderForFulfillment";
    String ORDER_PARTS_CREATED = "orderPartsCreated";
    String ORDER_PARTS_ROUTED = "orderPartsRouted";

    /**
     * Gets the ORDER_FOR_FULFILLMENT channel.
     *
     * @return SubscribableChannel
     */
    @Input(ORDER_FOR_FULFILLMENT)
    SubscribableChannel orderForFulfillment();

    /**
     * Gets the ORDER_PARTS_CREATED channel.
     *
     * @return MessageChannel
     */
    @Output(ORDER_PARTS_CREATED)
    MessageChannel orderPartsCreated();

    /**
     * Gets the ORDER_PARTS_ROUTED channel.
     *
     * @return MessageChannel
     */
    @Output(ORDER_PARTS_ROUTED)
    MessageChannel orderPartsRouted();
}
