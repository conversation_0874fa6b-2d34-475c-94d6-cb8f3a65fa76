package com.bestseller.services.orderrouting.configuration.messaging;

import org.springframework.cloud.stream.annotation.Input;
import org.springframework.cloud.stream.annotation.Output;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.SubscribableChannel;

/**
 * Channel for {@link ItemAvailabilityChannels} message.
 */
public interface ItemAvailabilityChannels {
    String ITEM_AVAILABILITY_REQUEST = "itemAvailabilityRequest";
    String ITEM_AVAILABILITY_RESPONSE = "itemAvailabilityResponse";
    String ITEM_AVAILABILITY_RESERVATION = "itemAvailabilityReservation";

    /**
     * Gets the ITEM_AVAILABILITY_REQUEST channel.
     * @return MessageChannel
     */
    @Output(ITEM_AVAILABILITY_REQUEST)
    MessageChannel itemAvailabilityRequest();

    /**
     * Gets the ITEM_AVAILABILITY_RESPONSE channel.
     * @return SubscribableChannel
     */
    @Input(ITEM_AVAILABILITY_RESPONSE)
    SubscribableChannel itemAvailabilityResponse();

    /**
     * Gets the ITEM_AVAILABILITY_RESERVATION channel.
     * @return MessageChannel
     */
    @Output(ITEM_AVAILABILITY_RESERVATION)
    MessageChannel itemAvailabilityReservation();
}
