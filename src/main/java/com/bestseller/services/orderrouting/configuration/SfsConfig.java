package com.bestseller.services.orderrouting.configuration;

import com.bestseller.services.orderrouting.configuration.pojos.sfs.OnlineFlag;
import com.bestseller.services.orderrouting.configuration.pojos.sfs.ShipFromStoreConfig;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.Optional;
import java.util.Set;
import java.util.TreeSet;
import java.util.stream.Collectors;

/**
 * Ship from store configuration properties from CCS.
 */
@Configuration
@ConfigurationProperties("shipfromstoreconfig")
public class SfsConfig extends ArrayList<ShipFromStoreConfig> {

    public static final String STORES_FIRST = "STORES_FIRST";
    public static final String SOLD_OUT = "SOLD_OUT";

    /**
     * Returns all enabled countries with the given type. The values are ISO 3166-1 alpha-2 in lowercase and they
     * can be looked up in a case insensitive order, ex: "nl" == "NL".
     * @param type the given type
     * @return set of qualifying countries in a case insensitive order
     */
    public Set<String> getEnabledCountriesByType(String type) {
        return this.stream()
                   .filter(config -> type.equalsIgnoreCase(config.getType()))
                   .filter(config -> !OnlineFlag.OFF.equals(Optional.ofNullable(config.getOnlineFlag()).orElse(OnlineFlag.OFF)))
                   .map(ShipFromStoreConfig::getCountry)
                   .collect(Collectors.toCollection(() -> new TreeSet<>(String.CASE_INSENSITIVE_ORDER)));
    }

    /**
     * Returns all countries in the configuration. The values are ISO 3166-1 alpha-2 in lowercase and they
     * can be looked up in a case insensitive order, ex: "nl" == "NL".
     * @return set of all countries in a case insensitive order
     */
    public Set<String> getAllCountries() {
        return this.stream()
                   .map(ShipFromStoreConfig::getCountry)
                   .collect(Collectors.toCollection(() -> new TreeSet<>(String.CASE_INSENSITIVE_ORDER)));
    }
}
