package com.bestseller.services.orderrouting.configuration.security;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.http.SessionCreationPolicy;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Configuration for Spring Security. Secures the Spring Actuator endpoints.
 * Credentials for the MONITOR role are injected through the system properties: user.monitor.name &
 * user.monitor.password.
 */
@EnableWebSecurity
public class SecurityConfig extends WebSecurityConfigurerAdapter {

    public static final String USER_MONITOR_NAME = "user.monitor.name";
    public static final String USER_MONITOR_PASSWORD = "user.monitor.password";
    public static final String USER_ADMINISTRATOR_NAME = "user.administrator.name";
    public static final String USER_ADMINISTRATOR_PASSWORD = "user.administrator.password";
    public static final String NO_OP_PASSWORD_PREFIX = "{noop}";

    @Autowired
    private Environment env;

    /**
     * Configures the http security.
     * @param http
     * @throws Exception
     */
    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http.sessionManagement()
                .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
                .and()
                .httpBasic()
                .and()
                .authorizeRequests().antMatchers("/actuator/health").permitAll()
                .and()
                .authorizeRequests().antMatchers("/actuator/info").permitAll()
                .and()
                .authorizeRequests().antMatchers("/actuator/**").hasRole(UserRoles.MONITOR.toString())
                .and()
                .authorizeRequests().antMatchers("/features/**").hasRole(UserRoles.ADMINISTRATOR.toString())
                .and()
                .authorizeRequests().anyRequest().authenticated()
                .and()
                .formLogin().disable()
                .csrf().disable();
    }

    /**
     * Configures the monitoring user.
     * @param auth
     * @throws Exception
     */
    @Autowired
    public void configureGlobal(AuthenticationManagerBuilder auth) throws Exception {
        auth.inMemoryAuthentication()
            .withUser(env.getProperty(USER_MONITOR_NAME))
            .password(NO_OP_PASSWORD_PREFIX + env.getProperty(USER_MONITOR_PASSWORD))
            .roles(UserRoles.MONITOR.getValue());

        auth.inMemoryAuthentication()
            .withUser(env.getProperty(USER_ADMINISTRATOR_NAME))
            .password(NO_OP_PASSWORD_PREFIX + env.getProperty(USER_ADMINISTRATOR_PASSWORD))
            .roles(UserRoles.ADMINISTRATOR.getValue());
    }

    /**
     * User Roles enum.
     */
    @AllArgsConstructor
    public enum UserRoles {
        MONITOR("MONITOR"),
        ADMINISTRATOR("ADMINISTRATOR");

        @Getter
        private String value;
    }
}
