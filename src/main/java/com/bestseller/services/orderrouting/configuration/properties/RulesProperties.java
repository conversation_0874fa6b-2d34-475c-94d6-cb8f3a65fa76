package com.bestseller.services.orderrouting.configuration.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import javax.validation.constraints.NotNull;

/**
 * Properties used by the rules.
 */
@Configuration
@Data
@ConfigurationProperties(prefix = "rules")
public class RulesProperties {

    @NotNull
    private ShipFromStore shipFromStoreFirst = new ShipFromStore();

    @NotNull
    private ShipFromStore shipFromStoreSoldOut = new ShipFromStore();

    @NotNull
    private CrossDockOrders crossDockOrders = new CrossDockOrders();

    /**
     * Contains ship from store specific properties.
     */
    @Data
    public class ShipFromStore {

        @NotNull
        private String[] excludedCarrierVariants;
    }

    /**
     * Contains cross dock orders specific properties.
     */
    @Data
    public class CrossDockOrders {

        @NotNull
        private String[] marketPlaces;
    }
}
