package com.bestseller.services.orderrouting.event;

import com.bestseller.services.orderrouting.model.OrderModel;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * A class used for OrderModel based events. Wraps an instance of OrderModel.
 */
public abstract class AbstractOrderEvent extends ApplicationEvent {

    private static final long serialVersionUID = -3247890330045742470L;

    @Getter
    private final OrderModel orderModel;

    /**
     * Create a new ApplicationEvent.
     *
     * @param source the object on which the event initially occurred (never {@code null})
     * @param orderModel the order for which the event is triggered
     */
    public AbstractOrderEvent(Object source, OrderModel orderModel) {
        super(source);
        this.orderModel = orderModel;
    }
}
