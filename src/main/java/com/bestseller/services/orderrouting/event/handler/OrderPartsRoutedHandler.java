package com.bestseller.services.orderrouting.event.handler;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsRouted.OrderPartsRouted;
import com.bestseller.services.orderrouting.event.AvailableInventoryEvent;
import com.bestseller.services.orderrouting.messaging.producer.Producer;
import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.model.OrderLineQuantityStatus;
import com.bestseller.services.orderrouting.model.OrderModel;
import com.bestseller.services.orderrouting.service.OrderService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.core.annotation.Order;
import org.springframework.core.convert.converter.Converter;
import org.springframework.messaging.MessagingException;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Listener for Spring Context events of type {@link AvailableInventoryEvent}. When fired, a batch of order parts
 * messages will be created based on the provided order lines by the event. The messages will then be published to the
 * "OrderPartsRouted" Kafka channel. If publishing fails for a given message then all the order lines for this message
 * will be changed to PLACED and their fulfillment node and order part number will be set to null. In case everything
 * goes fine then the order lines which are part of the messages will be changed to ROUTED.
 */
@Component
@AllArgsConstructor
@Slf4j
public class OrderPartsRoutedHandler {
    private Producer<OrderPartsRouted> orderPartsRoutedProducer;
    private Converter<OrderModel, List<OrderPartsRouted>> orderPartsConverter;
    private OrderService orderService;

    /**
     * Converts the incoming {@link AvailableInventoryEvent} into a {@link List} of {@link OrderPartsRouted}
     * messages and publishes them to OrderChannels.ORDER_PARTS_ROUTED. Changes order line states
     * depending on the result of publishing.
     * @param event
     */
    @EventListener
    @Order(2)
    public void handleAvailableInventory(AvailableInventoryEvent event) {
        OrderModel orderModel = event.getOrderModel();
        List<OrderPartsRouted> orderPartMessages = orderPartsConverter.convert(orderModel);

        log.debug("Order part messages created for order{}: {} ", orderModel, orderPartMessages);

        List<OrderLineQuantityModel> orderLineQuantityModels = orderModel.getOrderLineQuantities();

        for (OrderPartsRouted orderPartMessage : orderPartMessages) {
            boolean isMessageSent = false;
            try {
                orderPartsRoutedProducer.produce(orderPartMessage);

                isMessageSent = true;
            } catch (final MessagingException e) {
                // We can't throw checked exceptions from @EventListener since they will be wrapped in
                // java.lang.reflect.UndeclaredThrowableException
                log.error("MessagingException thrown in OrderPartsRoutedHandler. Will be handled in LoggerAspect.");
            } finally {
                if (isMessageSent) {
                    orderService.updateOrderLinesStatus(getOrderLinesForMessage(orderPartMessage, orderLineQuantityModels),
                            OrderLineQuantityStatus.ROUTED);
                } else {
                    log.error("An order part message for order {}, lines {} and fulfillment node {} failed to be "
                                    + "published",
                            orderPartMessage.getOrderId(),
                            orderPartMessage.getOrderLines()
                                    .stream()
                                    .map(OrderLine::getLineNumber)
                                    .collect(Collectors.toList()),
                            orderPartMessage.getFulfillmentNode());
                    revertOrderLine(orderPartMessage, orderLineQuantityModels);
                }
            }
        }
    }

    private void revertOrderLine(OrderPartsRouted orderPartMessage, List<OrderLineQuantityModel> orderLineQuantityModels) {
        List<OrderLineQuantityModel> orderLinesToRevert = getOrderLinesForMessage(orderPartMessage,
                orderLineQuantityModels);
        orderLinesToRevert.forEach(olqm -> {
            olqm.setFulfillmentNode(null);
            olqm.setOrderPartNumber(null);
        });
        orderService.updateOrderLinesStatus(orderLinesToRevert, OrderLineQuantityStatus.ROUTING);
    }

    private List<OrderLineQuantityModel> getOrderLinesForMessage(OrderPartsRouted orderPartMessage,
                                                                 List<OrderLineQuantityModel> orderLineQuantityModels) {
        return orderLineQuantityModels
                .stream()
                .filter(olqm -> olqm.getOrderPartNumber() != null
                        && olqm.getOrderPartNumber().intValue() == orderPartMessage.getOrderPartNumber().intValue())
                .collect(Collectors.toList());
    }
}
