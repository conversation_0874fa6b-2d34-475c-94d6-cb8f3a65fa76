package com.bestseller.services.orderrouting.event.handler;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsCreated.OrderPartsCreated;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsRouted.OrderPartsRouted;
import com.bestseller.services.orderrouting.event.AvailableInventoryEvent;
import com.bestseller.services.orderrouting.messaging.producer.Producer;
import com.bestseller.services.orderrouting.model.OrderModel;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.core.annotation.Order;
import org.springframework.core.convert.converter.Converter;
import org.springframework.messaging.MessagingException;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Listener for Spring Context events of type {@link AvailableInventoryEvent}. When fired, a batch of order parts
 * messages will be created based on the provided order lines by the event. The messages will then be published to the
 * "OrderPartsCreated" Kafka channel. If publishing fails for a given message then all the order lines for this message
 * will be changed to PLACED and their fulfillment node and order part number will be set to null. In case everything
 * goes fine then the order lines which are part of the messages will be changed to ROUTED.
 */
@Component
@AllArgsConstructor
@Slf4j
public class OrderPartsCreatedHandler {
    private Producer<OrderPartsCreated> orderPartsCreatedProducer;
    private Converter<OrderModel, List<OrderPartsCreated>> orderPartsCreatedConverter;

    /**
     * Converts the incoming {@link AvailableInventoryEvent} into a {@link List} of {@link OrderPartsRouted}
     * messages and publishes them to OrderRoutingChannels.ORDER_PARTS_ROUTED. Changes order line states
     * depending on the result of publishing.
     * @param event
     */
    @EventListener
    @Order(1)
    public void handleAvailableInventory(AvailableInventoryEvent event) {
        OrderModel orderModel = event.getOrderModel();

        List<OrderPartsCreated> orderPartMessages = orderPartsCreatedConverter.convert(orderModel);

        for (OrderPartsCreated message : orderPartMessages) {
            boolean isMessageSent = false;
            try {
                orderPartsCreatedProducer.produce(message);

                isMessageSent = true;
            } catch (final MessagingException e) {
                // We can't throw checked exceptions from @EventListener since they will be wrapped in
                // java.lang.reflect.UndeclaredThrowableException
                log.error("MessagingException thrown in OrderPartsCreatedHandler. Will be handled in LoggerAspect.");
            } finally {
                if (!isMessageSent) {
                    log.error("An order part created message for order {} and fulfillment node {} failed to be published",
                            message.getOrderId(), message.getFulfillmentNode());
                }
            }
        }
    }
}
