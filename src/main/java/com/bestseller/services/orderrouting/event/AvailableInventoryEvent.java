package com.bestseller.services.orderrouting.event;

import com.bestseller.services.orderrouting.model.OrderModel;

/**
 * Use this event to notify all interested listeners that there are items available in stock for lines of a given order.
 */
public class AvailableInventoryEvent extends AbstractOrderEvent {

    private static final long serialVersionUID = 1634108787547271023L;

    /**
     * AvailableInventoryEvent constructor.
     * @param source
     * @param orderModel
     */
    public AvailableInventoryEvent(Object source, OrderModel orderModel) {
        super(source, orderModel);
    }
}
