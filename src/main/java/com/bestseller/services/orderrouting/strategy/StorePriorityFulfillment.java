package com.bestseller.services.orderrouting.strategy;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailability;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import javax.annotation.Nonnull;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;

import static com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailability.Type.STORE;

/**
 * Tries to ship entire order to the stores provided they have satisfying availability.
 */
@Component
@AllArgsConstructor
public class StorePriorityFulfillment extends AbstractScoreFulfillment<Boolean> {

    @Override
    protected Boolean updateScore(Boolean accumulatedScore, int quantity, ItemAvailability availability, String shippingCountry) {
        return accumulatedScore && isSatisfyingStoreAvailability(quantity, availability, shippingCountry);
    }

    private boolean isSatisfyingStoreAvailability(int quantity, ItemAvailability availability, String shippingCountry) {
        boolean satisfying = availability.getAvailableQuantity() >= quantity;
        boolean store = availability.getType() == STORE;
        boolean local = shippingCountry.equals(availability.getCountry());
        return satisfying && local && store;
    }

    @Override
    protected Boolean initialScore() {
        // start with optimistic score
        return true;
    }

    @Nonnull
    @Override
    protected Optional<String> chooseWinnerWarehouse(Map<String, Boolean> warehouseScores) {
        return warehouseScores.entrySet().stream()
                .filter(Entry::getValue)
                .map(Entry::getKey)
                .findAny();
    }
}
