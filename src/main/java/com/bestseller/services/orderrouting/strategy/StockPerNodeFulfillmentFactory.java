package com.bestseller.services.orderrouting.strategy;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailability;
import com.bestseller.services.orderrouting.configuration.Warehouses;
import com.bestseller.services.orderrouting.feature.toggles.ORSFeatures;
import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.model.OrderModel;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.List;
import java.util.Optional;

import static com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailability.Type.STORE;
import static com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailability.Type.WAREHOUSE;
import static java.util.Comparator.comparing;

/**
 * Factory for creating different variations of the {@link StockPerNodeFulfillment} fulfillment strategy.
 * Different strategies may vary upon the used fulfillment filters. For example: one strategy may put
 * fulfillment from stores with higher priority than fulfillment from warehouses.
 */
@Component
@RequiredArgsConstructor
public class StockPerNodeFulfillmentFactory {

    private final Warehouses warehouses;

    /**
     * Searches for fulfillment nodes of type STORE in the country where the order will be shipped. The availability
     * in the fulfillment node should cover the requested quantity. Returns the first one in the list.
     *
     * @return filter to be used in strategy
     */
    private Optional<String> firstSatisfyingStoreAvailability(List<ItemAvailability> availabilities,
                                                              OrderLineQuantityModel orderLineQuantityModel,
                                                              OrderModel orderModel) {
        return availabilities
                .stream()
                .filter(itemAvailability -> STORE == itemAvailability.getType())
                .filter(itemAvailability -> orderModel.getShippingAddress()
                                                                .getCountry()
                                                                .equals(itemAvailability.getCountry()))
                .filter(itemAvailability -> itemAvailability.getAvailableQuantity() >= orderLineQuantityModel.getQuantity())
                .map(ItemAvailability::getWarehouse)
                .findFirst();
    }

    /**
     * Searches for fulfillment nodes of type WAREHOUSE that has enough availability for the ordered quantity
     * and prefers Fulfillment Center West over Fulfillment Center East.
     *
     * @return filter to be used in strategy
     */
    @SuppressWarnings("PMD.UnusedFormalParameter")
    private Optional<String> satisfyingWarehouseWestFirst(List<ItemAvailability> availabilities, OrderLineQuantityModel orderLineQuantityModel,
                                                          OrderModel order) {
        return availabilities
                .stream()
                .filter(itemAvailability -> WAREHOUSE == itemAvailability.getType())
                .filter(itemAvailability -> itemAvailability.getAvailableQuantity() >= orderLineQuantityModel.getQuantity())
                .max(getComparator())
                .map(ItemAvailability::getWarehouse);
    }

    private Comparator<ItemAvailability> getComparator() {
        if (ORSFeatures.PRIORITIZE_FCW_OVER_FCE.isActive()) {
            return comparing(ItemAvailability::getWarehouse, comparing(this::isWest))
                .thenComparing(ItemAvailability::getAvailableQuantity);
        }

        return comparing(ItemAvailability::getAvailableQuantity).thenComparing(ItemAvailability::getAvailableQuantity);
    }

    private boolean isWest(String warehouse) {
        return warehouse.endsWith("_NL");
    }

    /**
     * Searches for fulfillment nodes of type WAREHOUSE that have non-zero availability
     * and returns the node that has most of it.
     *
     * @return filter to be used in strategy
     */
    @SuppressWarnings("PMD.UnusedFormalParameter")
    private Optional<String> maxAvailableQuantityAmongWarehousesThatHaveNonZeroAvailability(List<ItemAvailability> availabilities,
                                                                                                  OrderLineQuantityModel orderLineQuantityModel,
                                                                                                  OrderModel orderModel) {
        return availabilities
                .stream()
                .filter(itemAvailability -> WAREHOUSE == itemAvailability.getType())
                .filter(itemAvailability -> itemAvailability.getAvailableQuantity() > 0)
                .max(Comparator.comparingInt(ItemAvailability::getAvailableQuantity))
                .map(ItemAvailability::getWarehouse);
    }


    /**
     * Assigns fulfillment nodes based on stock from ItemAvailabilityResponse.
     * The following algorithm is used:
     * - Find warehouses that can fully satisfy the order line using the available quantity and choose the one with
     * the highest available quantity. If none is available go to next step.
     * - In this step the only left warehouses are these that have zero availability.In this case the warehouse with most available quantity
     * should be chosen.
     * - If no warehouse is chosen, no warehouse is returned.
     *
     * @return strategy for warehouse split fulfillment
     */
    @Bean
    public StockPerNodeFulfillment warehouseFulfillment() {
        return new StockPerNodeFulfillment(warehouses, List.of(
                this::satisfyingWarehouseWestFirst,
                this::maxAvailableQuantityAmongWarehousesThatHaveNonZeroAvailability));
    }

    /**
     * Assigns fulfillment nodes based on stock from ItemAvailabilityResponse.
     * The following algorithm is used:
     * - Find warehouses that can fully satisfy the order line using the available quantity and choose the one with
     * the highest available quantity. If none is available go to next step.
     * - Find stores in the country of shipment that can fully satisfy the order line using the available quantity and choose the one with
     * the highest available quantity. If none is available go to next step.
     * If none is available go to next step.
     * - In this step the only left warehouses are these that have zero availability. In this case the warehouse with most available quantity
     * should be chosen.
     * - If no warehouse is chosen, no warehouse is returned.
     *
     * @return strategy for warehouse split fulfillment
     */
    @Bean
    public StockPerNodeFulfillment shipFromStoreWarehousePriorityFulfillment() {
        return new StockPerNodeFulfillment(warehouses, List.of(
                this::satisfyingWarehouseWestFirst,
                this::firstSatisfyingStoreAvailability,
                this::maxAvailableQuantityAmongWarehousesThatHaveNonZeroAvailability));
    }

    /**
     * Assigns fulfillment nodes based on stock from ItemAvailabilityResponse.
     * The following algorithm is used:
     * - Find warehouses that can fully satisfy the order line using the available quantity and choose the one with
     * the highest available quantity. If none is available go to next step.
     * - Find stores in the country of shipment that can fully satisfy the order line using the available quantity and choose the one with
     * the highest available quantity. If none is available go to next step.
     * - In this step the only left warehouses are these that have zero availability. In this case the warehouse with most available quantity
     *  should be chosen.
     * - If no warehouse is chosen, no warehouse is returned.
     *
     * - After selecting the fulfillment centers if the part for a SFS warehouse is bigger than 6 will split this
     * part in 2.
     *
     * @return strategy for warehouse split fulfillment
     */
    @Bean
    public StockPerNodeAndSplitFulfillment stockPerNodeAndSplitFulfillment() {
        return new StockPerNodeAndSplitFulfillment(warehouses, List.of(
                this::satisfyingWarehouseWestFirst,
                this::firstSatisfyingStoreAvailability,
                this::maxAvailableQuantityAmongWarehousesThatHaveNonZeroAvailability));
    }

    /**
     * Assigns fulfillment nodes based on stock from ItemAvailabilityResponse.
     * The following algorithm is used:
     * - Find stores in the country of shipment that can fully satisfy the order line using the available quantity and choose the one with
     * the highest available quantity. If none is available go to next step.
     * - Find warehouses that can fully satisfy the order line using the available quantity and choose the one with
     * the highest available quantity. If none is available go to next step.
     * - In this step the only left warehouses are these that have zero availability. In this case the warehouse with most available quantity
     *  should be chosen.
     * - If no warehouse is chosen, no warehouse is returned.
     *
     * @return strategy for warehouse split fulfillment
     */
    @Bean
    public StockPerNodeFulfillment shipFromStoreStorePriorityFulfillment() {
        return new StockPerNodeFulfillment(warehouses, List.of(
                this::firstSatisfyingStoreAvailability,
                this::satisfyingWarehouseWestFirst,
                this::maxAvailableQuantityAmongWarehousesThatHaveNonZeroAvailability));
    }
}
