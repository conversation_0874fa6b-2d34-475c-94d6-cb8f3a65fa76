package com.bestseller.services.orderrouting.strategy;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailability;
import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.model.OrderModel;

import java.util.List;
import java.util.Optional;

/**
 * Function to be used to determine the best suited fulfillment node for a given order, product availability and order line.
 */
@FunctionalInterface
public interface FulfillmentFilter {
    /**
     * Function to be implemented in Fulfillment Strategies.
     */
    Optional<String> evaluateFulfillmentNode(List<ItemAvailability> availabilities,
                                             OrderLineQuantityModel orderLineQuantityModel,
                                             OrderModel orderModel);
}
