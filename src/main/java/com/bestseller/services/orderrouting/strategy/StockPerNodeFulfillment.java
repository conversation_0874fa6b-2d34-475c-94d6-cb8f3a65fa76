package com.bestseller.services.orderrouting.strategy;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailability;
import com.bestseller.services.orderrouting.configuration.Warehouses;
import com.bestseller.services.orderrouting.feature.toggles.ORSFeatures;
import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.model.OrderModel;
import lombok.RequiredArgsConstructor;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.Comparator.comparingInt;
import static java.util.Map.Entry.comparingByValue;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toMap;

/**
 * Fulfillment strategy that allows splitting of orders into order parts for each assigned fulfillment node.
 * The assignment of fulfillment nodes is determined by a list of fulfillment filters:
 * <ul>
 *     <li>the first filter in the list has the highest priority and the last has the lowest</li>
 *     <li>if a filter does not return fulfillment node then the next filter in the list is evaluated</li>
 *     <li>if a filter returns fulfillment node then evaluation of all the others terminates and the node is assigned</li>
 *     <li>if no filter returns fulfillment node then the strategy fallbacks to the default warehouse</li>
 * </ul>
 */
@RequiredArgsConstructor
public class StockPerNodeFulfillment implements Fulfillment {

    private final Warehouses warehouses;
    private final List<FulfillmentFilter> fulfillmentFilters;

    /**
     * Evaluates fulfillment node for one order line or leaves it unassigned.
     *
     * @param orderLine order line to be evaluated.
     * @param orderModel order model containing the order line to be evaluated.
     * @param eanAvailability additional information to be used for evaluation.
     * @return fulfillment node if assigned
     */
    public Optional<String> findNodeForLine(OrderLineQuantityModel orderLine,
                                            OrderModel orderModel,
                                            Map<String, List<ItemAvailability>> eanAvailability) {
        return Optional.ofNullable(eanAvailability.get(orderLine.getEan()))
                .flatMap(availability -> fulfillmentFilters.stream()
                        .map(filter -> filter.evaluateFulfillmentNode(availability, orderLine, orderModel))
                        .flatMap(Optional::stream)
                        .findFirst());
    }

    @Override
    public Map<String, List<List<OrderLineQuantityModel>>> allocateOrderParts(OrderModel orderModel,
                                                                                 Map<String, List<ItemAvailability>> eanAvailability) {

        // assign some order lines
        Map<Optional<String>, List<OrderLineQuantityModel>> partiallyAssignedLines = orderModel.getOrderLineQuantities()
                .stream()
                .collect(groupingBy(line -> findNodeForLine(line, orderModel, eanAvailability)));

        String fallbackWarehouse;
        if (ORSFeatures.PRIORITIZE_FCW_OVER_FCE.isActive()) {
            // choose the best warehouse to route oversold lines to
            fallbackWarehouse = evaluateFallbackWarehouse(partiallyAssignedLines);
        } else {
            // route oversold lines to FCE, so that it can cancel them
            fallbackWarehouse = warehouses.getFulfillmentCenterEast();
        }

        // assign the rest of the lines
        Map<String, List<OrderLineQuantityModel>> assignedLines = assignUnassignedLines(partiallyAssignedLines, fallbackWarehouse);

        // make 1 order part per warehouse
        return allocateSinglePartPerNode(assignedLines);
    }

    private Map<String, List<List<OrderLineQuantityModel>>> allocateSinglePartPerNode(Map<String, List<OrderLineQuantityModel>> assignableNodes) {
        return assignableNodes.entrySet()
                .stream()
                .collect(toMap(Entry::getKey, entry -> Collections.singletonList(entry.getValue())));
    }

    private String evaluateFallbackWarehouse(Map<Optional<String>, List<OrderLineQuantityModel>> partiallyAssignedLines) {
        Map<String, List<OrderLineQuantityModel>> assignedLines = partiallyAssignedLines.entrySet()
                .stream()
                .filter(entry -> entry.getKey().isPresent())
                .collect(toMap(entry -> entry.getKey().get(), Entry::getValue));

        String priorityWarehouse = warehouses.getFulfillmentCenterWest();

        // no order parts has been formed yet, we have the chance to send all order lines to FCW
        if (assignedLines.isEmpty()) {
            return priorityWarehouse;
        }

        // one order part is already going to FCW, we have the chance to send more order lines to FCW
        if (assignedLines.containsKey(priorityWarehouse)) {
            return priorityWarehouse;
        }

        // we're left with FCE here, but let's imagine there could be others, so we can rank them
        // unassigned order lines will be added to the biggest order part
        return Collections.max(assignedLines.entrySet(), comparingByValue(comparingInt(this::quantitySum))).getKey();
    }

    private int quantitySum(List<OrderLineQuantityModel> lines) {
        return lines.stream()
                .mapToInt(OrderLineQuantityModel::getQuantity)
                .sum();
    }

    private Map<String, List<OrderLineQuantityModel>> assignUnassignedLines(Map<Optional<String>,
                                                                            List<OrderLineQuantityModel>> partiallyAssignedLines,
                                                                            String fallbackWarehouse) {
        return partiallyAssignedLines.entrySet()
                .stream()
                .collect(toMap(
                        entry -> entry.getKey().orElse(fallbackWarehouse),
                        Entry::getValue,
                        this::concatenateLists));
    }

    private <T> List<T> concatenateLists(List<T> left, List<T> right) {
        return Stream.of(left, right)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }
}
