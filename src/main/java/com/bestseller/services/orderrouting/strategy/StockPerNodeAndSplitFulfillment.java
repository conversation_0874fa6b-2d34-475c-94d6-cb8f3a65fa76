package com.bestseller.services.orderrouting.strategy;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailability;
import com.bestseller.services.orderrouting.configuration.Warehouses;
import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.model.OrderModel;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;

import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;

import static java.util.Collections.singletonList;
import static java.util.stream.Collectors.toMap;

/**
 * Stock per node and split fulfillment. It will create new order parts if the warehouse is SFS and order line size
 * is bigger than MAX_SFS_PART_SIZE.
 */
public class StockPerNodeAndSplitFulfillment extends StockPerNodeFulfillment {

    @Value("${rules.shipFromStoreBigOrder.maxSfsSize}")
    @Setter
    private Integer maxSfsPartSize;

    /**
     * Stock per node and split fulfillment constructor.
     * @param fulfillmentFilters fulfillment filters to be applied
     */
    public StockPerNodeAndSplitFulfillment(Warehouses warehouses, List<FulfillmentFilter> fulfillmentFilters) {
        super(warehouses, fulfillmentFilters);
    }

    @Override
    public Map<String, List<List<OrderLineQuantityModel>>> allocateOrderParts(OrderModel orderModel,
                                                                                 Map<String, List<ItemAvailability>> eanAvailability) {
        Map<String, List<List<OrderLineQuantityModel>>> perNodeParts = super.allocateOrderParts(orderModel, eanAvailability);

        // sub-divide order parts
        return perNodeParts
                .entrySet()
                .stream()
                .collect(toMap(Entry::getKey, entry -> entry.getValue()
                        .stream()
                        .flatMap(part -> splitOrderPartFurther(entry.getKey(), part).stream())
                        .collect(Collectors.toList())));
    }

    private List<List<OrderLineQuantityModel>> splitOrderPartFurther(String warehouse, List<OrderLineQuantityModel> orderPart) {
        boolean shouldSplit = warehouse.matches("SHIP_FROM_STORE_[A-Z]{2}") && orderPart.size() > maxSfsPartSize;

        if (shouldSplit) {
            return bisectOrderPart(orderPart);
        } else {
            return singletonList(orderPart);
        }
    }

    private List<List<OrderLineQuantityModel>> bisectOrderPart(List<OrderLineQuantityModel> orderLineModels) {
        int firstPartSize = orderLineModels.size() / 2;

        List<OrderLineQuantityModel> firstPart = orderLineModels.stream()
                .limit(firstPartSize)
                .collect(Collectors.toList());

        List<OrderLineQuantityModel> secondPart = orderLineModels.stream()
                .skip(firstPartSize)
                .collect(Collectors.toList());

        return List.of(firstPart, secondPart);
    }
}
