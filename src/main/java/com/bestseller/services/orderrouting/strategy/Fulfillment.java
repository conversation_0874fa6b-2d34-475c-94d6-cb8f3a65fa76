package com.bestseller.services.orderrouting.strategy;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailability;
import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.model.OrderModel;

import java.util.List;
import java.util.Map;

/**
 * Interface defining template for evaluation and assigning fulfillment nodes to orders.
 */
public interface Fulfillment {

    /**
     * Group order lines in order parts and assign them to warehouses.
     * @param orderModel order attributes and order lines
     * @param eanAvailability which warehouses each EAN is available in
     * @return order lines partitioned into order parts and group by warehouse
     */
    Map<String, List<List<OrderLineQuantityModel>>> allocateOrderParts(OrderModel orderModel,
                                                                       Map<String, List<ItemAvailability>> eanAvailability);
}
