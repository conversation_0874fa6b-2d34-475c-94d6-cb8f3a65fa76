package com.bestseller.services.orderrouting.strategy;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailability;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailabilityResponse;
import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.model.OrderModel;

import javax.annotation.Nonnull;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static java.util.Objects.requireNonNullElseGet;

/**
 * Takes fulfillment nodes from the {@link ItemAvailabilityResponse} and ranks them by a calculated abstract score.
 * Whichever gets the highest score wins.
 * @param <S> type used to store the accumulated score
 */
public abstract class AbstractScoreFulfillment<S> implements Fulfillment {

    @Override
    public Map<String, List<List<OrderLineQuantityModel>>> allocateOrderParts(OrderModel orderModel,
                                                                              Map<String, List<ItemAvailability>> eanAvailability) {
        Optional<String> node = getSingleFulfillmentNodeForEntireOrder(orderModel, eanAvailability);
        if (node.isEmpty()) {
            return Collections.emptyMap();
        }

        return Map.of(node.get(), Collections.singletonList(orderModel.getOrderLineQuantities()));
    }

    @Nonnull
    private Optional<String> getSingleFulfillmentNodeForEntireOrder(OrderModel orderModel, Map<String, List<ItemAvailability>> eanAvailability) {
        Map<String, S> warehouseScores = new HashMap<>();

        String shippingCountry = orderModel.getShippingAddress().getCountry();

        orderModel.getOrderLineQuantities().forEach(line ->
                eanAvailability.getOrDefault(line.getEan(), List.of())
                        .forEach(availability ->
                        warehouseScores.compute(availability.getWarehouse(), (warehouse, score) ->
                                updateScore(requireNonNullElseGet(score, this::initialScore), line.getQuantity(), availability, shippingCountry))));

        return chooseWinnerWarehouse(warehouseScores);
    }

    /**
     * Initial accumulated score to assign to each warehouse before looking into availabilities.
     * @return score value to start with
     */
    protected abstract S initialScore();

    /**
     * Adjust the accumulated warehouse score accordingly given ordered quantity for an EAN and the corresponding
     * availability information.
     * @param accumulatedScore current warehouse score
     * @param quantity requested quantity
     * @param availability available quantities information
     * @param shippingCountry
     * @return new score to assign to the warehouse
     */
    protected abstract S updateScore(S accumulatedScore, int quantity, ItemAvailability availability, String shippingCountry);

    /**
     * Determine the best warehouse based on score.
     * @param warehouseScores final score value per warehouse
     * @return best warehouse or nothing if all are equally good or bad
     */
    @Nonnull
    protected abstract Optional<String> chooseWinnerWarehouse(Map<String, S> warehouseScores);
}
