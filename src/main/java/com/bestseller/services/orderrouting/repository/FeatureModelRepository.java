package com.bestseller.services.orderrouting.repository;

import org.springframework.data.repository.CrudRepository;

import com.bestseller.services.orderrouting.model.FeatureModel;
import org.springframework.stereotype.Repository;

/**
 * {@link CrudRepository} for {@link FeatureModel} type.
 *
 * @see CrudRepository
 * @see FeatureModel
 */
@Repository
public interface FeatureModelRepository extends CrudRepository<FeatureModel, String> {
}
