package com.bestseller.services.orderrouting.repository;

import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

/**
 * {@link CrudRepository} for {@link OrderLineQuantityModel} type.
 *
 * @see CrudRepository
 * @see OrderLineQuantityModel
 */
@Repository
public interface OrderLineQuantityModelRepository extends CrudRepository<OrderLineQuantityModel, OrderLineQuantityModel.PrimaryKey> {

    /**
     * Count lines by order id.
     * @param orderId order id
     * @return count of the order lines for that order id.
     */
    long countByOrderId(String orderId);

}

