package com.bestseller.services.orderrouting.repository;

import com.bestseller.services.orderrouting.model.OrderLineQuantityStatus;
import com.bestseller.services.orderrouting.model.OrderModel;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;

/**
 * {@link CrudRepository} for {@link OrderModel} type.
 *
 * @see CrudRepository
 * @see OrderModel
 */
@Repository
public interface OrderModelRepository extends CrudRepository<OrderModel, String> {

    String FROM_CLAUSE = "from `Order` o                     \n"
            + "where o.createdDate <?1                       \n"
            + "  and true = all (                            \n"
            + "    select                                    \n"
            + "      olqm.status in ('ROUTED', 'CANCELLED')  \n"
            + "    from OrderLineQuantity olqm               \n"
            + "    where olqm.orderId = o.orderId            \n"
            + "  )                                           \n";

    String COUNT_QUERY = "select count(*) " + FROM_CLAUSE;

    // we have to start the deletion from Addresses because
    // they are parents of Orders which are in turn parents of the other entities
    String DELETE_STATEMENT = "delete from Address        \n"
            + "where id in (                              \n"
            + "  select billingAddressId                  \n"
            +    FROM_CLAUSE
            + "  union all                                \n"
            + "  select shippingAddressId                 \n"
            +    FROM_CLAUSE
            // we have to include orphan Addresses here
            // as removing a Billing Address will cascade down to its Order
            // and leave its Shipping Address orphan
            + ") or id not in (                          \n"
            + "  select shippingAddressId from `Order`   \n"
            + "  union all                               \n"
            + "  select billingAddressId from `Order`    \n"
            + ")                                         \n";


    /**
     * Count all the order created before createdDate and having all orderLines
     * in given states.
     *
     * @param createdDate maximum created date to include
     * @return number of such orders
     */
    @Query(value = COUNT_QUERY, nativeQuery = true)
    int countCreatedDateBeforeAndAllOrderLineStatusesRoutedOrCancelled(Instant createdDate);

    /**
     * Delete all the order created before createdDate and having all orderLines
     * in given states.
     *
     * @param createdDate maximum created date to include
     */
    @Query(value = DELETE_STATEMENT, nativeQuery = true)
    @Modifying
    void deleteCreatedDateBeforeAndAllOrderLineStatusesRoutedOrCancelled(Instant createdDate);

    /**
     * Find orders in given status between given dates.
     *
     * @param status status of order line quantity
     * @param from   start date
     * @param to     end date
     * @return list of orders
     */
    @Query("select "
        + "distinct o "
        + "from OrderModel o join OrderLineQuantityModel ol "
        + "on o.orderId = ol.orderId "
        + "where ol.status = :status "
        + "and o.createdDate between :from and :to")
    List<OrderModel> getOrdersInStatus(@Param("status") OrderLineQuantityStatus status,
                                       @Param("from") Instant from,
                                       @Param("to") Instant to);
}
