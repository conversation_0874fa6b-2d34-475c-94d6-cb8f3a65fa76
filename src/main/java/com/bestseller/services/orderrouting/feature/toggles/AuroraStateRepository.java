package com.bestseller.services.orderrouting.feature.toggles;

import org.springframework.core.convert.converter.Converter;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Component;
import org.togglz.core.Feature;
import org.togglz.core.repository.FeatureState;
import org.togglz.core.repository.StateRepository;

import com.bestseller.services.orderrouting.model.FeatureModel;

import lombok.AllArgsConstructor;

import java.util.Optional;

/**
 * AuroraStateRepository.
 */
@Component
@AllArgsConstructor
public class AuroraStateRepository implements StateRepository {

    private CrudRepository<FeatureModel, String> featureModelRepository;
    private Converter<FeatureModel, FeatureState> featureModelConverter;
    private Converter<FeatureState, FeatureModel> featureStateConverter;

    /**
     * Gets FeatureState from Feature.
     * @param feature
     * @return
     */
    @Override
    public FeatureState getFeatureState(final Feature feature) {
        Optional<FeatureModel> featureModel = featureModelRepository.findById(feature.name());
        return featureModel.map(model -> featureModelConverter.convert(model)).orElse(null);
    }

    /**
     * Sets the FeatureState.
     * @param featureState
     */
    @Override
    public void setFeatureState(final FeatureState featureState) {
        FeatureModel featureModel = featureStateConverter.convert(featureState);
        featureModelRepository.save(featureModel);
    }
}
