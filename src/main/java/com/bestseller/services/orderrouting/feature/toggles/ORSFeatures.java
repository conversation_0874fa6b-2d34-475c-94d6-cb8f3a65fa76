package com.bestseller.services.orderrouting.feature.toggles;

import org.togglz.core.Feature;
import org.togglz.core.annotation.EnabledByDefault;
import org.togglz.core.annotation.Label;
import org.togglz.core.context.FeatureContext;
import org.togglz.core.repository.FeatureState;

/**
 * New or existing features in the ORS can be controlled with the so called "feature toggles". Feature toggles have states and by default they are
 * disabled. Their activation can be handled by a database update or by a change in a properties file or by a more complex logic implemented as either
 * Java or ECMAScript code. In order to check if a feature is enabled and execute some logic specific for this feature you can do the following:
 *
 * <pre>
 *     if (ORSFeatures.SOME_FEATURE.isActive()) {
 *         // Execute feature specific logic
 *         ...
 *     }
 * </pre>
 *
 * States of features can be updated in real-time without the need of redeploying ORS.
 *
 * To see how this set of features persist their states see {@link ORSFeatureToggleConfig} To know more on how to control this set of features please
 * read <a href="https://www.togglz.org/documentation/overview.html">Togglz - Feature Flags for the Java platform</a>
 *
 */
public enum ORSFeatures implements Feature {

    /**
     * This feature is responsible for routing of orders to the respective fulfillment nodes (warehouses) based on different business rules.
     */
    @Label("Route orders to fulfillment nodes")
    @EnabledByDefault
    ROUTE_ORDERS,

    /**
     * This feature is responsible for defining if split is allowed for ship from store first orders.
     */
    @Label("SFS Store First Split Allowed")
    @EnabledByDefault
    SFS_STORE_FIRST_SPLIT_ALLOWED,

    /**
     * This feature is responsible for defining if big order rule is applicable for ship from store first orders.
     */
    @Label("SFS Store First big order splitting enabled")
    SFS_STORE_FIRST_BIG_ORDERS_ENABLED,

    /**
     * This feature is responsible for enabling Idempotency Check for orderPartsRouted message.
     */
    @Label("OrderForFulfillment message Idempotency Check required")
    @EnabledByDefault
    ORDER_FOR_FULFILLMENT_IDEMPOTENCY_CHECK_REQUIRED,

    /**
     * Enables FCW routing rules.
     */
    @Label("Fulfillment center west rules enabled")
    PRIORITIZE_FCW_OVER_FCE,

    @EnabledByDefault
    @Label("Ignore FCW availability")
    IGNORE_FCW_AVAILABILITY;

    /**
     * Checks whether a feature is active.
     *
     * @return true if feature is active, false otherwise
     */
    public boolean isActive() {
        return FeatureContext.getFeatureManager().isActive(this);
    }

    /**
     * The method sets the active state of the feature.
     * @param isActive true if feature is active, false otherwise
     */
    public void setActive(boolean isActive) {
        FeatureContext.getFeatureManager().setFeatureState(new FeatureState(this, isActive));
    }
}
