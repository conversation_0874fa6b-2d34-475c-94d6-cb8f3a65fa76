package com.bestseller.services.orderrouting.feature.toggles;

import org.springframework.core.env.Environment;
import org.togglz.core.Feature;
import org.togglz.core.manager.TogglzConfig;
import org.togglz.core.repository.StateRepository;
import org.togglz.core.user.FeatureUser;
import org.togglz.core.user.SimpleFeatureUser;
import org.togglz.core.user.UserProvider;

import com.bestseller.services.orderrouting.configuration.security.SecurityConfig;

import lombok.AllArgsConstructor;

/**
 * Configuration for the {@link ORSFeatures} set of feature toggles. It includes:
 * <ul>
 * <li>Persisting of feature states to a hash field in Redis.</li>
 * <li>Allow only users with authentication authority "ROLE_ADM" to change feature states.</li>
 * </ul>
 * <p>
 * Other configurations not part of this class include:
 * <ul>
 * <li>Enable Togglz Admin Console so that users can enable/disable features and set activation strategies.</li>
 * </ul>
 * <p>
 * Please see web.xml for Togglz configurations. Please read more on activation strategies, state repositories and Admin Console at
 * <a href="https://www.togglz.org/documentation/overview.html">Togglz - Feature Flags for the Java platform</a>
 */
@AllArgsConstructor
public class ORSFeatureToggleConfig implements TogglzConfig {
    private StateRepository stateRepository;
    private Environment env;

    /**
     * Gets Feature Class.
     * @return
     */
    @Override
    public Class<? extends Feature> getFeatureClass() {
        return ORSFeatures.class;
    }

    /**
     * Gets the StateRepository.
     * @return
     */
    @Override
    public StateRepository getStateRepository() {
        return stateRepository;
    }

    /**
     * Gets the UserProvider.
     * @return
     */
    @Override
    public UserProvider getUserProvider() {
        final String userName = env.getProperty(SecurityConfig.USER_ADMINISTRATOR_NAME);
        return new UserProvider() {
            /**
             * Gets the current user.
             * @return
             */
            @Override
            public FeatureUser getCurrentUser() {
                return new SimpleFeatureUser(userName, true);
            }
        };
    }
}
