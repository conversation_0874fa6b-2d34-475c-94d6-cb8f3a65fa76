package com.bestseller.services.orderrouting.messaging.consumer;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailability;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailabilityResponse;
import com.bestseller.services.orderrouting.configuration.SfsConfig;
import com.bestseller.services.orderrouting.configuration.messaging.ItemAvailabilityChannels;
import com.bestseller.services.orderrouting.feature.toggles.ORSFeatures;
import com.bestseller.services.orderrouting.messaging.exceptions.IdempotentMessageException;
import com.bestseller.services.orderrouting.messaging.validation.ValidationException;
import com.bestseller.services.orderrouting.messaging.validation.Validator;
import com.bestseller.services.orderrouting.metric.TimedOperation;
import com.bestseller.services.orderrouting.model.OrderLineQuantityStatus;
import com.bestseller.services.orderrouting.model.OrderModel;
import com.bestseller.services.orderrouting.repository.OrderModelRepository;
import com.bestseller.services.orderrouting.service.OrderService;
import com.bestseller.services.orderrouting.service.RulesService;
import com.bestseller.services.orderrouting.service.idempotency.MessageIdempotencyService;
import datadog.trace.api.Trace;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.BooleanUtils;
import org.jeasy.rules.api.Facts;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.dao.TransientDataAccessException;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import javax.transaction.Transactional;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Locale;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.bestseller.services.orderrouting.metric.CounterOperation.ITEM_AVAILABILITY_RESPONSE_CONSUMED;
import static com.bestseller.services.orderrouting.rules.facts.FactNames.ITEM_AVAILABILITY_RESPONSE_FACT;
import static com.bestseller.services.orderrouting.rules.facts.FactNames.ORDER_MODEL_FACT;

/**
 * Handles ItemAvailabilityResponse message.
 */
@Component
@Validated
@AllArgsConstructor
@Slf4j
public class ItemAvailabilityResponseConsumer {

    private Validator<ItemAvailabilityResponse> itemAvailabilityResponseValidator;
    private MeterRegistry meterRegistry;
    private OrderService orderService;
    private MessageIdempotencyService<ItemAvailabilityResponse> messageIdempotencyService;
    private RulesService rulesService;
    private OrderModelRepository orderModelRepository;
    private SfsConfig sfsConfig;

    /**
     * Handles the ItemAvailabilityResponse and sends out the events for routing or cancelling order lines.
     *
     * @param itemAvailability message response containing stock information for different warehouses.
     * @throws ValidationException if validation errors have occurred during processing the message.
     */
    @Transactional
    @StreamListener(ItemAvailabilityChannels.ITEM_AVAILABILITY_RESPONSE)
    @Trace(operationName = ItemAvailabilityChannels.ITEM_AVAILABILITY_RESPONSE)
    public void receiveItemAvailabilityResponse(@NotNull @Valid ItemAvailabilityResponse itemAvailability) throws ValidationException {
        try {
            if (!orderModelRepository.existsById(itemAvailability.getCorrelationId())) {
                log.debug("Order with id {} doesn't exist in the database!", itemAvailability.getCorrelationId());
                return;
            }

            OrderModel orderModel = processItemAvailabilityResponse(itemAvailability);

            rulesService.executeOrderRoutingRules(createFacts(itemAvailability, orderModel));
            meterRegistry.counter(ITEM_AVAILABILITY_RESPONSE_CONSUMED).increment();
        } catch (IdempotentMessageException e) {
            log.warn("This ItemAvailabilityResponse message for order with id {} has been processed before!",
                    itemAvailability.getCorrelationId());
        } catch (TransientDataAccessException e) {
            log.warn("Item availability response needs to be reprocessed: {}", itemAvailability.getCorrelationId());
            throw e;
        }
    }

    private Facts createFacts(ItemAvailabilityResponse itemAvailability, OrderModel orderModel) {
        Facts facts = new Facts();
        facts.put(ORDER_MODEL_FACT, orderModel);
        facts.put(ITEM_AVAILABILITY_RESPONSE_FACT, itemAvailability);

        return facts;
    }

    private OrderModel processItemAvailabilityResponse(ItemAvailabilityResponse itemAvailability)
            throws ValidationException, IdempotentMessageException {
        log.debug("Validating an ItemAvailabilityResponse message...");
        itemAvailabilityResponseValidator.validate(itemAvailability);
        log.info("ItemAvailabilityResponse message for order with id {} validated.", itemAvailability.getCorrelationId());

        // Check for the duplicate messages.
        if (messageIdempotencyService.checkDuplicateMessage(itemAvailability)) {
            throw new IdempotentMessageException();
        }

        // enrich the message if necessary
        enrichTheMessageIfNecessary(itemAvailability);

        // Set the availability of FCW to zero if IGNORE_FCW_AVAILABILITY feature is disabled
        setFcwAvailabilityToZeroAndSortNodesIfNecessary(itemAvailability);

        // Get the order and calculate the time required for processing it
        OrderModel orderModel = orderService.findOrderModel(itemAvailability.getCorrelationId());

        // clear fulfillment node and order part number as they might be set in previous ItemAvailabilityResponse
        // for the same order
        orderModel.getOrderLineQuantities().forEach(orderLine -> {
            orderLine.setFulfillmentNode(null);
            orderLine.setOrderPartNumber(null);
        });

        // log the time for processing the inventory request
        long responseTime = Duration.between(orderModel.getCreatedDate(), Instant.now()).toMillis();
        meterRegistry.timer(TimedOperation.INVENTORY_RESPONSE).record(responseTime, TimeUnit.MILLISECONDS);
        log.debug("Routing an ItemAvailabilityResponse message...");

        if (BooleanUtils.isTrue(orderModel.getIsOrderAdvice())) {
            orderModel.getOrderLineQuantities().forEach(orderLine -> orderLine.setStatus(OrderLineQuantityStatus.ADVISED));
        }

        orderService.saveOrderLines(orderModel.getOrderLineQuantities());

        return orderModel;
    }

    /**
     * Enriches the message with the missing warehouses and SFS countries.
     *
     * @param itemAvailability the message to be enriched.
     */
    protected void enrichTheMessageIfNecessary(ItemAvailabilityResponse itemAvailability) {
        final List<String> warehouseName = Warehouse.getWarehouseNames();
        final Set<String> sfsCountries = sfsConfig.getAllCountries();
        itemAvailability.getItems()
                .forEach(item -> {
                    List<Warehouse> missedWarehouses = new ArrayList<>();
                    List<String> missedSfsCountries = new ArrayList<>();
                    var currentFulfilmentNodes = new ArrayList<>(item.getItemAvailability());
                    warehouseName.forEach(fulfilmentNode -> {
                        if (currentFulfilmentNodes.stream()
                                .noneMatch(availability ->
                                        availability.getWarehouse().equalsIgnoreCase(fulfilmentNode))) {
                            missedWarehouses.add(Warehouse.valueOf(fulfilmentNode));
                        }
                    });
                    sfsCountries.forEach(sfsCountry -> {
                        if (currentFulfilmentNodes.stream()
                                .noneMatch(availability ->
                                        availability.getCountry().equalsIgnoreCase(sfsCountry))) {
                            missedSfsCountries.add(sfsCountry);
                        }
                    });
                    missedSfsCountries
                        .forEach(sfsCountry ->
                                currentFulfilmentNodes.add(new ItemAvailability()
                                        .withCountry(sfsCountry)
                                        .withAvailableQuantity(0)
                                        .withWarehouse("SHIP_FROM_STORE_" + sfsCountry.toUpperCase(Locale.ROOT))
                                        .withType(ItemAvailability.Type.STORE)));
                    missedWarehouses
                        .forEach(fulfilmentNode ->
                                currentFulfilmentNodes.add(new ItemAvailability()
                                        .withWarehouse(fulfilmentNode.getName())
                                        .withCountry(fulfilmentNode.getCountry())
                                        .withAvailableQuantity(0)
                                        .withType(ItemAvailability.Type.WAREHOUSE)));
                    item.setItemAvailability(currentFulfilmentNodes);
        });
    }

    /**
     * Sets the availability of FCW to zero and sorts the nodes if the IGNORE_FCW_AVAILABILITY feature is disabled.
     *
     * @param itemAvailability the message to be enriched.
     */
    protected void setFcwAvailabilityToZeroAndSortNodesIfNecessary(ItemAvailabilityResponse itemAvailability) {
        if (!ORSFeatures.IGNORE_FCW_AVAILABILITY.isActive()) {
            return;
        }
        itemAvailability.getItems().forEach(item -> {
            item.getItemAvailability().stream()
                .filter(availability -> availability.getWarehouse()
                    .equalsIgnoreCase(Warehouse.INGRAM_MICRO_NL.name()))
                .forEach(availability -> availability.setAvailableQuantity(0));
        });

        Comparator<ItemAvailability> comparator = Comparator.comparing(ItemAvailability::getAvailableQuantity)
            .thenComparing(ItemAvailability::getWarehouse);
        itemAvailability.getItems().forEach(item -> {
            item.setItemAvailability(
                item.getItemAvailability().stream().sorted(comparator).collect(Collectors.toList())
            );
        });
    }
}
