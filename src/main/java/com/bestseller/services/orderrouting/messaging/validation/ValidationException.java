package com.bestseller.services.orderrouting.messaging.validation;

import java.text.MessageFormat;

/**
 * Exception used in validating data abundance and correctness.
 */
public class ValidationException extends Exception {
    public static final String NULL_OR_EMPTY_FIELD_TEMPLATE = "\"{0}\" must not be empty or null!";
    public static final String NON_POSITIVE_FIELD_TEMPLATE = "\"{0}\" must be more than zero!";
    public static final String ONE_OF_THE_FIELDS_MUST_BE_SET_TEMPLATE = "at least one of \"{0}\" or \"{1}\" must be set!";
    public static final String FIELD_DEPENDENCY_TEMPLATE = "\"{0}\" must not be empty or null when \"{1}\" is set!";
    public static final String NULL_OR_EMPTY_FIELD_FOR_ORDER_ID_TEMPLATE = "\"{0}\" must not be empty or null for orderId \"{1}\"";

    private static final long serialVersionUID = -8241397859380643177L;

    /**
     * Default constructor.
     *
     * @param message for the exception.
     */
    public ValidationException(String message) {
        super(message);
    }

    /**
     *
     * Constructor for template messages.
     *
     * @param messageTemplate string template for {@link MessageFormat}.
     * @param args arguments to be populated in the template.
     *
     * @see MessageFormat
     */
    public ValidationException(String messageTemplate, Object... args) {
        super(MessageFormat.format(messageTemplate, args));
    }
}
