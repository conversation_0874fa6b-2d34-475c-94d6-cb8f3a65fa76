package com.bestseller.services.orderrouting.messaging.validation;

/**
 * Interface for custom object validation.
 *
 * @param <T> object type which will be validated.
 */
public interface Validator<T> {

    /**
     * Validates given objects of type {@link T}.
     *
     * @param obj object to be validated
     *
     * @throws ValidationException object is not valid.
     */
    void validate(T obj) throws ValidationException;
}
