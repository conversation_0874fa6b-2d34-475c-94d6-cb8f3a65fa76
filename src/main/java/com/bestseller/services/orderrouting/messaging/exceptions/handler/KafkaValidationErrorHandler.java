package com.bestseller.services.orderrouting.messaging.exceptions.handler;

import lombok.extern.slf4j.Slf4j;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessagingException;
import org.springframework.messaging.handler.annotation.support.MethodArgumentNotValidException;
import org.springframework.stereotype.Service;

/**
 * Handler for validation errors that occur during the processing of Kafka messages.
 * This service listens to the error channel and processes validation errors accordingly.
 */
@Service
@Slf4j
public class KafkaValidationErrorHandler {

    /**
     * Handles validation errors that occur during the processing of Kafka messages.
     * This method is invoked when a validation error occurs, allowing for custom error handling logic.
     *
     * @param message the message containing the validation error
     */
    @ServiceActivator(inputChannel = "errorChannel")
    public void handleError(Message<?> message) {
        Object rawPayload = message.getPayload();
        Throwable payload;

        if (rawPayload instanceof MessagingException) {
            MessagingException messagingException = (MessagingException) rawPayload;
            Throwable cause = messagingException.getCause();

            payload = cause == null ? messagingException : cause;
        } else if (rawPayload instanceof Throwable) {
            payload = (Throwable) rawPayload;
        } else {
            log.error("Unknown error payload type: {}", rawPayload.getClass().getName());
            return;
        }

        if (payload instanceof MethodArgumentNotValidException) {
            var ex = (MethodArgumentNotValidException) payload;
            log.error("Validation error in Kafka message: {}", ex.getMessage());
        } else {
            log.error("Unhandled error in Kafka consumer: {}", payload.getMessage(), payload);
        }
    }
}
