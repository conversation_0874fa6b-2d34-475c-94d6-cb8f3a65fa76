package com.bestseller.services.orderrouting.messaging.validation;

import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.model.OrderModel;
import com.bestseller.services.orderrouting.repository.OrderModelRepository;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Validator of an incoming messages from IMS which reports stock availability for a given order line items.
 */
@AllArgsConstructor
public abstract class AbstractOrderPartsValidator<T> implements Validator<T> {

    private static final String NO_ORDER_DETAILS_TEMPLATE =
            "No order details found in cache for \"{0}\" reported by a {1} message";

    @Getter
    private OrderModelRepository orderModelRepository;
    private Class<T> messageClass;

    /**
     * Validates message EANs for given orderId.
     * List of the validation rules:
     * <ul>
     *     <li>No duplicate EANs are in the message list.</li>
     *     <li>Order exist in cache for the given orderId.</li>
     *     <li>All given EANs are in order lines for the order found.</li>
     * </ul>
     *
     * @param orderModel the orderModel to validate with
     * @param messageEans a list of EANs to check for availability in the order record
     *
     * @throws ValidationException in case there is a validation error
     */
    protected final void validateMessageEans(final OrderModel orderModel, final Set<String> messageEans)
            throws ValidationException {
        // Check if EANs are in this order.
        checkIfEansAreInOrder(orderModel, messageEans);
    }

    private void checkIfEansAreInOrder(final OrderModel orderModel, final Set<String> messageEans)
            throws ValidationException {
        // Convert cached line entities to a distinct set of EANs.
        Set<String> cachedEans = orderModel.getOrderLineQuantities()
                .stream()
                .map(OrderLineQuantityModel::getEan)
                .collect(Collectors.toSet());

        if (!cachedEans.containsAll(messageEans)) {
            Set<String> extraneousEans = new HashSet<>(messageEans);
            extraneousEans.removeAll(cachedEans);
            throw new ValidationException("EANs from message {0} not found in cache for order {1}: {2}",
                    messageClass.getSimpleName(), orderModel.getOrderId(), extraneousEans);
        }
    }

    /**
     * Finds the order model based upon the order ID. If it cannot find the order, it will throw an exception.
     * @param orderId
     * @return the order model.
     * @throws ValidationException
     */
    protected OrderModel findOrderModel(final String orderId) throws ValidationException {
        return orderModelRepository.findById(orderId).orElseThrow(() ->
                new ValidationException(NO_ORDER_DETAILS_TEMPLATE, orderId, messageClass.getSimpleName()));
    }
}
