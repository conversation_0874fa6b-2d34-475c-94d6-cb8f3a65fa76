package com.bestseller.services.orderrouting.messaging.exceptions;

import org.springframework.messaging.MessagingException;

/**
 * Exception happening during processing of the messages.
 */
public class ProcessingException extends Exception {

    private static final long serialVersionUID = 6974630752321118L;

    /**
     * Constructor with route cause and reason message.
     *
     * @param reason for this exception to occur.
     * @param e cause exception.
     */
    public ProcessingException(final String reason, final MessagingException e) {
        super(reason, e);
    }
}
