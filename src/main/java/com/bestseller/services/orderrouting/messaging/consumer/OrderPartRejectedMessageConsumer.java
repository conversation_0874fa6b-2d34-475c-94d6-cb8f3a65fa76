package com.bestseller.services.orderrouting.messaging.consumer;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartRejected.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartRejected.OrderPartRejected;
import com.bestseller.services.orderrouting.configuration.messaging.OrderCancellingChannels;
import com.bestseller.services.orderrouting.converter.OrderConverter;
import com.bestseller.services.orderrouting.messaging.validation.OrderPartRejectedMessageValidator;
import com.bestseller.services.orderrouting.messaging.validation.ValidationException;
import com.bestseller.services.orderrouting.metric.CounterOperation;
import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.model.OrderModel;
import com.bestseller.services.orderrouting.rules.facts.FactNames;
import com.bestseller.services.orderrouting.service.OrderService;
import com.bestseller.services.orderrouting.service.RulesService;
import com.bestseller.services.orderrouting.service.idempotency.MessageIdempotencyService;
import com.bestseller.services.orderrouting.util.OrderValueCalculator;
import datadog.trace.api.Trace;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeasy.rules.api.Facts;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import javax.transaction.Transactional;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Order parts cancelled message consumer.
 * Consumes {@link OrderPartRejected} from "OrderPartRejected" Kafka topic.
 */
@Component
@Validated
@AllArgsConstructor
@Slf4j
public class OrderPartRejectedMessageConsumer {
    private final OrderService orderService;
    private final RulesService rulesService;
    private final MeterRegistry meterRegistry;
    private final OrderPartRejectedMessageValidator orderPartRejectedMessageValidator;
    private final MessageIdempotencyService<OrderPartRejected> orderPartRejectedIdempotency;
    private final OrderValueCalculator orderValueCalculator;
    private OrderConverter orderConverter;

    /**
     * Default consumer method linked to the OrderRoutingChannels.ORDER_PART_REJECTED.
     *
     * @param orderPartRejected message to process.
     */
    @Transactional
    @StreamListener(OrderCancellingChannels.ORDER_PART_REJECTED)
    @Trace(operationName = OrderCancellingChannels.ORDER_PART_REJECTED)
    public void receiveOrderPartRejected(@NotNull @Valid OrderPartRejected orderPartRejected) throws ValidationException {
        reportMetrics(orderPartRejected.getWarehouse(), orderPartRejected.getOrderLines());

        // Validate the message.
        orderPartRejectedMessageValidator.validate(orderPartRejected);

        if (orderPartRejectedIdempotency.checkDuplicateMessage(orderPartRejected)) {
            return;
        }

        OrderModel orderModel = orderService.findOrderModel(orderPartRejected.getOrderId());
        OrderModel deepCopyOfOrder = orderConverter.deepCopyOrderModel(orderModel);

        meterRegistry.counter(CounterOperation.TOTAL_ORDER_CANCELLED_VALUE,  List.of(Tag.of("warehouse", orderPartRejected.getWarehouse())))
                .increment(getOrderCancelledValue(orderModel, orderPartRejected).doubleValue());

        try {
            Facts facts = new Facts();
            facts.put(FactNames.ORDER_MODEL_FACT, orderModel);
            facts.put(FactNames.ORDER_PART_REJECTED_FACT, orderPartRejected);

            rulesService.executeOrderPartRejectedRules(facts);
        } catch (Exception e) {
            log.error("Thrown error! Reverting order {} to original state.", orderPartRejected.getOrderId());

            // Save original order before any modification to keep
            orderService.saveOrderModel(deepCopyOfOrder);

            throw e;
        }
    }

    private void reportMetrics(String warehouse, Collection<OrderLine> orderLines) {
        String cancellationReason = orderLines.stream()
                .findFirst()
                .map(OrderLine::getCancelReason)
                .orElse(StringUtils.EMPTY);

        var tags = List.of(Tag.of("originWarehouse", warehouse),
                Tag.of("totalOrderLines", String.valueOf(orderLines.size())),
                Tag.of("cancellationReason", cancellationReason));
        meterRegistry.counter(CounterOperation.ORDER_PART_REJECTED_CONSUMED, tags).increment();
    }

    private BigDecimal getOrderCancelledValue(OrderModel orderModel, OrderPartRejected orderPartRejected) {
        Map<String, Integer> cancelledEans = orderPartRejected.getOrderLines()
                .stream()
                .collect(Collectors.toMap(OrderLine::getEan, OrderLine::getQuantity));

        Collection<OrderLineQuantityModel> orderLineQuantityModels = orderModel
                .getOrderLineQuantities()
                .stream()
                .filter(orderLineQuantityModel -> cancelledEans.containsKey(orderLineQuantityModel.getEan()))
                // update cancelled quantity
                .map(orderLineQuantityModel -> withQuantityAdjusted(orderLineQuantityModel, cancelledEans.get(orderLineQuantityModel.getEan())))
                .collect(Collectors.toList());

        return orderValueCalculator.totalOrderCost(orderLineQuantityModels);
    }

    private OrderLineQuantityModel withQuantityAdjusted(OrderLineQuantityModel qtyModel, int newQty) {
        OrderLineQuantityModel clone = orderConverter.deepCopyOrderQuantityModel(qtyModel);
        clone.setQuantity(newQty);
        return clone;
    }
}

