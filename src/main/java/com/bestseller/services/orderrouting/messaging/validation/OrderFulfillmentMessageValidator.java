package com.bestseller.services.orderrouting.messaging.validation;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderForFulfillment.OrderForFulfillment;
import org.springframework.stereotype.Component;

import static com.bestseller.services.orderrouting.messaging.validation.ValidationException.NULL_OR_EMPTY_FIELD_FOR_ORDER_ID_TEMPLATE;
import static com.bestseller.services.orderrouting.messaging.validation.ValidationException.ONE_OF_THE_FIELDS_MUST_BE_SET_TEMPLATE;

/**
 * Performs validations on the data in the {@link OrderForFulfillment} message.
 */
@Component
public class OrderFulfillmentMessageValidator implements Validator<OrderForFulfillment> {

    private static final String BILLING_COUNTRY = "billingCountry";
    private static final String SHIPPING_COUNTRY = "shippingCountry";

    /**
     * {@inheritDoc}
     */
    @Override
    public void validate(OrderForFulfillment orderForFulfillment) throws ValidationException {
        String billingCountry = orderForFulfillment.getCustomerInformation().getBillingAddress().getCountry();
        String shippingCountry = orderForFulfillment.getShippingInformation().getShippingAddress().getCountry();

        if (billingCountry == null && shippingCountry == null) {
            throw new ValidationException(ONE_OF_THE_FIELDS_MUST_BE_SET_TEMPLATE, BILLING_COUNTRY, SHIPPING_COUNTRY);
        }

        for (OrderLine orderLine : orderForFulfillment.getOrderLines()) {
            if (orderLine.getRetailPrice() == null) {
                throw new ValidationException(NULL_OR_EMPTY_FIELD_FOR_ORDER_ID_TEMPLATE, "retailPrice", orderForFulfillment.getOrderId());
            }

            if (orderLine.getDiscountValue() == null) {
                throw new ValidationException(NULL_OR_EMPTY_FIELD_FOR_ORDER_ID_TEMPLATE, "discountValue", orderForFulfillment.getOrderId());
            }
        }
    }
}
