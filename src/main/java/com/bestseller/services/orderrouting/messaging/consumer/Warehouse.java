package com.bestseller.services.orderrouting.messaging.consumer;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;


/**
 * Warehouse enum.
 */
@Getter
@AllArgsConstructor
public enum Warehouse {

    INGRAM_MICRO("INGRAM_MICRO", "PL"),
    INGRAM_MICRO_NL("INGRAM_MICRO_NL", "NL");


    private final String name;
    private final String country;

    /**
     * Returns the warehouse name.
     *
     * @return the warehouse name.
     */
    public static List<String> getWarehouseNames() {
        return Arrays.stream(Warehouse.values())
                .map(Warehouse::getName)
                .collect(Collectors.toList());
    }
}
