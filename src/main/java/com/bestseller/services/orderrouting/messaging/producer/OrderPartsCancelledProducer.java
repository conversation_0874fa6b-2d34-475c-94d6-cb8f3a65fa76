package com.bestseller.services.orderrouting.messaging.producer;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartsCancelled.OrderPartsCancelled;
import com.bestseller.services.orderrouting.configuration.messaging.OrderCancellingChannels;
import datadog.trace.api.Trace;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

/**
 * Produces {@link OrderPartsCancelled} message to the OrderPartsCancelled topic.
 */
@Component
@AllArgsConstructor
@Slf4j
public class OrderPartsCancelledProducer implements Producer<OrderPartsCancelled> {
    private OrderCancellingChannels orderCancellingChannels;

    /**
     * {@inheritDoc}
     */
    @Override
    @Trace(operationName = OrderCancellingChannels.ORDER_PARTS_CANCELLED)
    public void produce(OrderPartsCancelled orderPartsCancelled) {
        orderCancellingChannels.orderPartsCancelled().send(MessageBuilder.withPayload(orderPartsCancelled).build());

        log.info("OrderPartsCancelled message send with id: {}", orderPartsCancelled.getOrderId());

    }
}
