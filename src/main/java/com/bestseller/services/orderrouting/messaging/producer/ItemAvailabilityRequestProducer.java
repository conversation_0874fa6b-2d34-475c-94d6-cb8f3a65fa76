package com.bestseller.services.orderrouting.messaging.producer;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailabilityRequest;
import com.bestseller.services.orderrouting.configuration.messaging.ItemAvailabilityChannels;
import datadog.trace.api.Trace;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

import static com.bestseller.services.orderrouting.metric.CounterOperation.ITEM_AVAILABILITY_REQUEST_PRODUCED;

/**
 * Produces ItemAvailabilityRequest message based upon the OrderForFulfillment message.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ItemAvailabilityRequestProducer implements Producer<ItemAvailabilityRequest> {

    private final ItemAvailabilityChannels itemAvailabilityChannels;
    private final MeterRegistry meterRegistry;

    /**
     * {@inheritDoc}
     */
    @Trace(operationName = ItemAvailabilityChannels.ITEM_AVAILABILITY_REQUEST)
    @Override
    public void produce(final ItemAvailabilityRequest itemAvailabilityRequest) {
        itemAvailabilityChannels.itemAvailabilityRequest()
                                .send(MessageBuilder.withPayload(itemAvailabilityRequest).build());
        log.info("ItemAvailabilityRequest message send with id: {}", itemAvailabilityRequest.getCorrelationId());
        meterRegistry.counter(ITEM_AVAILABILITY_REQUEST_PRODUCED).increment();
    }
}
