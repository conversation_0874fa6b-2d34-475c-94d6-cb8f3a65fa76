package com.bestseller.services.orderrouting.messaging.validation;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.Item;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailability;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailabilityResponse;
import com.bestseller.services.orderrouting.model.OrderModel;
import com.bestseller.services.orderrouting.repository.OrderModelRepository;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

import static java.util.stream.Collectors.counting;
import static java.util.stream.Collectors.groupingBy;

/**
 * Validator of an incoming messages from IMS which reports stock availability for a given order line items.
 */
@Component
public class ItemAvailabilityResponseValidator extends AbstractOrderPartsValidator<ItemAvailabilityResponse> {

    private static final String ITEM_AVAILABILITY_WAREHOUSE_PROPERTY = "itemAvailability.warehouse";
    private static final String ITEM_AVAILABILITY_AVAILABLE_QUANTITY_PROPERTY = "itemAvailability.availableQuantity";
    private static final String ITEM_AVAILABILITY_BACKORDER_QUANTITY_PROPERTY = "itemAvailability.backorderQuantity";
    private static final String ITEM_AVAILABILITY_EARLIEST_AVAILABILITY_PROPERTY = "itemAvailability.earliestAvailabilityDate";
    private static final String INVALID_FULFILLMENT_NODE_BY_TYPE = "invalid availability, no such a fulfillment node type";
    private static final String INVALID_FULFILLMENT_NODE_BY_COUNTRY_NAME = "invalid availability, country name should be at least 2 characters";
    private static final String INVALID_AVAILABILITY_STORE_PER_COUNTRY = "invalid availability, two stores per country";

    /**
     * ItemAvailabilityResponseValidator constructor.
     *
     * @param orderModelRepository
     */
    public ItemAvailabilityResponseValidator(OrderModelRepository orderModelRepository) {
        super(orderModelRepository, ItemAvailabilityResponse.class);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void validate(ItemAvailabilityResponse message) throws ValidationException {
        if (StringUtils.isBlank(message.getCorrelationId())) {
            throw new ValidationException(ValidationException.NULL_OR_EMPTY_FIELD_TEMPLATE, "correlationId");
        }

        if (Objects.isNull(message.getItems()) || message.getItems().isEmpty()) {
            throw new ValidationException(ValidationException.NULL_OR_EMPTY_FIELD_TEMPLATE, "items");
        }

        Set<String> availabilityEans = new HashSet<>();

        for (Item item : message.getItems()) {
            validateItem(item);

            for (ItemAvailability availability : item.getItemAvailability()) {
                validateItemAvailability(availability);
            }

            availabilityEans.add(item.getEan());
        }

        OrderModel orderModel = findOrderModel(message.getCorrelationId());
        validateMessageEans(orderModel, availabilityEans);
    }

    /**
     * Checks for correctness of EAN and availability items.
     * <ul>
     * <li>EAN cannot be null, "" or " "</li>
     * <li>availability items cannot be null or an empty list</li>
     * </ul>
     *
     * @param item
     * @throws ValidationException
     */
    private void validateItem(Item item) throws ValidationException {
        if (StringUtils.isBlank(item.getEan())) {
            throw new ValidationException(ValidationException.NULL_OR_EMPTY_FIELD_TEMPLATE, "item.ean");
        }

        if (Objects.isNull(item.getItemAvailability()) || item.getItemAvailability().isEmpty()) {
            throw new ValidationException(ValidationException.NULL_OR_EMPTY_FIELD_TEMPLATE, "item.itemAvailability");
        }

        boolean isValidNodeByType = item.getItemAvailability().stream()
                .map(ItemAvailability::getType)
                .allMatch(Objects::nonNull);

        if (!isValidNodeByType) {
            throw new ValidationException(INVALID_FULFILLMENT_NODE_BY_TYPE);
        }

        boolean isValidNodeByCountry = item.getItemAvailability()
                .stream()
                .map(ItemAvailability::getCountry)
                .allMatch(this::isCountryValid);

        if (!isValidNodeByCountry) {
            throw new ValidationException(INVALID_FULFILLMENT_NODE_BY_COUNTRY_NAME);
        }

        long storesPerCountry = item.getItemAvailability().stream()
                .filter(itemAvailability -> ItemAvailability.Type.STORE == itemAvailability.getType())
                .collect(groupingBy(ItemAvailability::getCountry, counting()))
                .values()
                .stream()
                .mapToLong(Long::intValue)
                .max()
                .orElse(0);

        if (storesPerCountry > NumberUtils.INTEGER_ONE) {
            throw new ValidationException(INVALID_AVAILABILITY_STORE_PER_COUNTRY);
        }
    }

    private boolean isCountryValid(String country) {
        return country != null && country.length() >= 2;
    }

    /**
     * Checks for correctness of warehouse identifier and available quantity.
     * <ul>
     * <li>warehouse identifier cannot be null, "" or " "</li>
     * <li>available quantity cannot be null or a negative number</li>
     * </ul>
     *
     * @param availability
     * @throws ValidationException
     */
    private void validateItemAvailability(ItemAvailability availability) throws ValidationException {
        if (StringUtils.isBlank(availability.getWarehouse())) {
            throw new ValidationException(ValidationException.NULL_OR_EMPTY_FIELD_TEMPLATE, ITEM_AVAILABILITY_WAREHOUSE_PROPERTY);
        }

        if (Objects.isNull(availability.getAvailableQuantity())) {
            throw new ValidationException(ValidationException.NULL_OR_EMPTY_FIELD_TEMPLATE, ITEM_AVAILABILITY_AVAILABLE_QUANTITY_PROPERTY);
        }

        if (availability.getAvailableQuantity() < 0) {
            throw new ValidationException(ValidationException.NON_POSITIVE_FIELD_TEMPLATE, ITEM_AVAILABILITY_AVAILABLE_QUANTITY_PROPERTY);
        }
    }
}
