package com.bestseller.services.orderrouting.messaging.validation;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartRejected.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartRejected.OrderPartRejected;
import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.model.OrderLineQuantityStatus;
import com.bestseller.services.orderrouting.model.OrderModel;
import com.bestseller.services.orderrouting.repository.OrderModelRepository;
import com.bestseller.services.orderrouting.service.CancellationReason;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import static com.bestseller.services.orderrouting.messaging.validation.ValidationException.NON_POSITIVE_FIELD_TEMPLATE;
import static com.bestseller.services.orderrouting.messaging.validation.ValidationException.NULL_OR_EMPTY_FIELD_TEMPLATE;
import static java.util.Arrays.asList;

/**
 * Validator of incoming messages from warehouses which reports OrderParts cancelled.
 */
@Component
@Slf4j
public class OrderPartRejectedMessageValidator extends AbstractOrderPartsValidator<OrderPartRejected> {

    private static final String ORDER_PART_REJECTED_ORDER_LINES_QUANTITY_PROPERTY = "OrderPartRejected.orderLines.quantity";

    /**
     * OrderPartRejectedMessageValidator constructor.
     *
     * @param orderModelRepository reference to {@link OrderModelRepository}.
     */
    public OrderPartRejectedMessageValidator(OrderModelRepository orderModelRepository) {
        super(orderModelRepository, OrderPartRejected.class);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void validate(OrderPartRejected message) throws ValidationException {
        log.debug("OrderPartRejected message received. Starting validation...");

        if (Objects.isNull(message.getOrderLines()) || message.getOrderLines().isEmpty()) {
            throw new ValidationException(NULL_OR_EMPTY_FIELD_TEMPLATE, "OrderPartRejected.orderLines");
        }

        if (StringUtils.isBlank(message.getWarehouse())) {
            throw new ValidationException(NULL_OR_EMPTY_FIELD_TEMPLATE, "OrderPartRejected.warehouse");
        }

        long totalCancelReasons = message.getOrderLines().stream().map(OrderLine::getCancelReason).distinct().count();
        if (totalCancelReasons > NumberUtils.INTEGER_ONE) {
            throw new ValidationException("Order {0} has mixed cancellation reasons.", message.getOrderId());
        }

        Set<String> orderLineEans = new HashSet<>();

        for (OrderLine orderLine : message.getOrderLines()) {
            validateOrderLine(orderLine);

            orderLineEans.add(orderLine.getEan());
        }

        OrderModel orderModel = findOrderModel(message.getOrderId());
        validateMessageEans(orderModel, orderLineEans);
        validateOrderLineState(orderModel);

        log.info("OrderPartRejected message for order with id {} validated.", message.getOrderId());
    }

    /**
     * Validates if the order line quantities in the repository are in the ROUTED state.
     *
     * @param orderModel order model
     * @throws ValidationException validation exception
     */
    private void validateOrderLineState(OrderModel orderModel) throws ValidationException {
        List<OrderLineQuantityStatus> allowedOrderLineStates = asList(OrderLineQuantityStatus.ROUTED, OrderLineQuantityStatus.CANCELLED);

        boolean allOrderLinesAreInRouting = orderModel.getOrderLineQuantities().stream()
                .map(OrderLineQuantityModel::getStatus)
                .allMatch(allowedOrderLineStates::contains);

        if (!allOrderLinesAreInRouting) {
            throw new ValidationException("Illegal cancellation for order {0}: Some order line states are neither of {1}.",
                    orderModel.getOrderId(), allowedOrderLineStates);
        }
    }

    /**
     * Checks for correctness of OrderLine. Check is done as follows:
     * <ul>
     *     <li>available quantity cannot be null or a non-positive number</li>
     *     <li>available EAN cannot be null or empty string</li>
     *     <li>available cancel reason cannot be null or empty string</li>
     *     <li>available line number cannot be null</li>
     * </ul>
     *
     * @param orderLine order line
     *
     * @throws ValidationException validation exception
     */
    private void validateOrderLine(final OrderLine orderLine) throws ValidationException {
        if (Objects.isNull(orderLine.getQuantity())) {
            throw new ValidationException(NULL_OR_EMPTY_FIELD_TEMPLATE,
                                          ORDER_PART_REJECTED_ORDER_LINES_QUANTITY_PROPERTY);
        }

        if (orderLine.getQuantity() < 0) {
            throw new ValidationException(NON_POSITIVE_FIELD_TEMPLATE,
                                          ORDER_PART_REJECTED_ORDER_LINES_QUANTITY_PROPERTY);
        }

        if (StringUtils.isBlank(orderLine.getEan())) {
            throw new ValidationException(NULL_OR_EMPTY_FIELD_TEMPLATE, "OrderPartRejected.orderLines.ean");
        }

        if (StringUtils.isBlank(orderLine.getCancelReason())) {
            throw new ValidationException(NULL_OR_EMPTY_FIELD_TEMPLATE, "OrderPartRejected.orderLines.cancelReason");
        }

        String cancelReason = orderLine.getCancelReason();

        if (!CancellationReason.REASONS.contains(cancelReason)) {
            throw new ValidationException("Unknown cancellation reason: {0}.", cancelReason);
        }

        if (orderLine.getLineNumber() == null) {
            throw new ValidationException(NULL_OR_EMPTY_FIELD_TEMPLATE, "OrderPartRejected.orderLines.lineNumber");
        }
    }
}
