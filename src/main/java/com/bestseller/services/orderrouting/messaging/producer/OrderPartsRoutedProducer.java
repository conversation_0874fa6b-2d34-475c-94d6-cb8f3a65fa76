package com.bestseller.services.orderrouting.messaging.producer;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsRouted.OrderPartsRouted;
import com.bestseller.services.orderrouting.configuration.messaging.OrderChannels;
import com.bestseller.services.orderrouting.converter.OrderConverter;
import com.bestseller.services.orderrouting.metric.CounterOperation;
import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.util.OrderValueCalculator;
import datadog.trace.api.Trace;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/**
 * Access point to "OrderPartsRouted" topic.
 */
@Component
@AllArgsConstructor
@Slf4j
public class OrderPartsRoutedProducer implements Producer<OrderPartsRouted> {
    private OrderChannels orderChannels;
    private OrderValueCalculator orderValueCalculator;
    private OrderConverter orderConverter;
    private MeterRegistry meterRegistry;

    /**
     * Using {@link OrderChannels} to send messages for OrderPartsRouted Kafka topic.
     *
     * @param orderPartsRouted message to be sent.
     */
    @Override
    @Trace(operationName = OrderChannels.ORDER_PARTS_ROUTED)
    public void produce(final OrderPartsRouted orderPartsRouted) {
        log.info("OrderPartsRoutedProducer: Producing message");
        var tags = List.of(Tag.of("warehouse", orderPartsRouted.getFulfillmentNode()));
        meterRegistry.counter(CounterOperation.ORDER_PARTS_ROUTED_PRODUCED, tags).increment();
        meterRegistry.counter(CounterOperation.ORDER_PARTS_ROUTED_LINES, tags).increment(orderPartsRouted.getOrderLines().size());
        meterRegistry.counter(CounterOperation.ORDER_PARTS_ROUTED_VALUE, tags).increment(getOrderValue(orderPartsRouted).doubleValue());

        orderChannels.orderPartsRouted().send(MessageBuilder.withPayload(orderPartsRouted).build());
        log.info("OrderPartsRouted message is sent to OrderPartsRouted topic for order with id {} and "
                        + "order part number {} to {}", orderPartsRouted.getOrderId(),
                orderPartsRouted.getOrderPartNumber(), orderPartsRouted.getFulfillmentNode());
    }

    private BigDecimal getOrderValue(OrderPartsRouted orderPartsRouted) {
        List<OrderLineQuantityModel> orderLineQuantityModels = orderConverter.convertRoutedLines(orderPartsRouted.getOrderLines());
        return orderValueCalculator.totalOrderCost(orderLineQuantityModels);
    }
}
