package com.bestseller.services.orderrouting.messaging.producer;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailabilityReservation;
import com.bestseller.services.orderrouting.configuration.messaging.ItemAvailabilityChannels;
import datadog.trace.api.Trace;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;


/**
 * Produces ItemAvailabilityReservation message based upon the
 * OrderForFulfillment and ItemAvailabilityResponse messages.
 */
@Component
@AllArgsConstructor
@Slf4j
public class ItemAvailabilityReservationProducer implements Producer<ItemAvailabilityReservation> {

    private ItemAvailabilityChannels itemAvailabilityChannels;

    /**
     * {@inheritDoc}
     */
    @Trace(operationName = ItemAvailabilityChannels.ITEM_AVAILABILITY_RESERVATION)
    @Override
    public void produce(final ItemAvailabilityReservation itemAvailabilityReservation) {
        itemAvailabilityChannels.itemAvailabilityReservation()
                .send(MessageBuilder.withPayload(itemAvailabilityReservation).build());
        log.info("ItemAvailabilityReservation message send with id: {}",
                itemAvailabilityReservation.getCorrelationId());

    }
}
