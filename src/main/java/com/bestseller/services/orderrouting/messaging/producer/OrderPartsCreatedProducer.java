package com.bestseller.services.orderrouting.messaging.producer;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsCreated.OrderPartsCreated;
import com.bestseller.services.orderrouting.configuration.messaging.OrderChannels;
import datadog.trace.api.Trace;
import lombok.AllArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

/**
 * Access point to "OrderPartsRouted" topic.
 */
@Log4j2
@Component
@AllArgsConstructor
public class OrderPartsCreatedProducer implements Producer<OrderPartsCreated> {
    private OrderChannels orderChannels;

    /**
     * Using {@link OrderChannels} to send messages for OrderPartsCreated Kafka topic.
     *
     * @param orderPartsCreated message to be sent.
     */
    @Override
    @Trace(operationName = OrderChannels.ORDER_PARTS_CREATED)
    public void produce(final OrderPartsCreated orderPartsCreated) {
        orderChannels.orderPartsCreated().send(MessageBuilder.withPayload(orderPartsCreated).build());

        log.info("OrderPartsCreated message is sent to OrderPartsCreated topic for order with id {} to {}", orderPartsCreated.getOrderId(),
                orderPartsCreated.getFulfillmentNode());
    }
}
