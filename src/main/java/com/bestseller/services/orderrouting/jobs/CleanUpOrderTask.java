package com.bestseller.services.orderrouting.jobs;

import com.bestseller.services.orderrouting.metric.KafkaMessageOperation;
import com.bestseller.services.orderrouting.repository.OrderModelRepository;
import datadog.trace.api.Trace;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.transaction.Transactional;
import java.time.Clock;
import java.time.Instant;
import java.time.temporal.ChronoUnit;


/**
 * This class presents the job for removing records after defined cleanupPeriodInDays.
 */

@Component
@RequiredArgsConstructor
@Slf4j
public class CleanUpOrderTask {

    @Value("${order.cleanup.period.days}")
    private long cleanupPeriodInDays;
    private final OrderModelRepository orderModelRepository;
    private final MeterRegistry meterRegistry;
    private final Clock utcClock;

    /**
     * Scheduled task is executed based on predefined interval of time and remove eligible orders.
     */
    @Transactional
    @Trace
    @SchedulerLock(name = "cleanUpOrderTask")
    @Scheduled(cron = "${order.cleanup.job.schedule.cron}")
    public void removeOldRecords() {
        Instant fromCleanUpDate = utcClock.instant().minus(cleanupPeriodInDays, ChronoUnit.DAYS);

        int ordersToCleanUp = orderModelRepository.countCreatedDateBeforeAndAllOrderLineStatusesRoutedOrCancelled(fromCleanUpDate);

        if (ordersToCleanUp > 0) {
            orderModelRepository.deleteCreatedDateBeforeAndAllOrderLineStatusesRoutedOrCancelled(fromCleanUpDate);
            log.info("{} orders cleaned up", ordersToCleanUp);
            logMetrics(ordersToCleanUp);
        }

    }

    private void logMetrics(final long orderCleanUpCount) {
        meterRegistry.counter(KafkaMessageOperation.ORDER_CLEANUP_COUNT.name()).increment(orderCleanUpCount);
    }

}
