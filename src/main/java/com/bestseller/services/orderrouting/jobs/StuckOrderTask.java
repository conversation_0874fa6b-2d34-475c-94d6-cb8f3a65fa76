package com.bestseller.services.orderrouting.jobs;

import com.bestseller.services.orderrouting.model.OrderLineQuantityStatus;
import com.bestseller.services.orderrouting.repository.OrderModelRepository;
import com.bestseller.services.orderrouting.rules.facts.FactNames;
import com.bestseller.services.orderrouting.service.RulesService;
import datadog.trace.api.Trace;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.jeasy.rules.api.Facts;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.transaction.Transactional;
import java.time.Clock;
import java.time.Duration;
import java.time.Instant;

/**
 * Job to fix orders stuck waiting for a response from IMS.
 */
@Component
@Slf4j
public class StuckOrderTask {

    private final OrderModelRepository orderModelRepository;

    private final RulesService rulesService;
    private final Clock utcClock;
    private final Duration fromDuration;
    private final Duration toDuration;

    /**
     * Constructor.
     *
     * @param orderModelRepository order model repository
     * @param rulesService rules service
     * @param utcClock clock
     * @param fromDuration from duration
     * @param toDuration to duration
     */
    public StuckOrderTask(
        OrderModelRepository orderModelRepository,
        RulesService rulesService,
        Clock utcClock,
        @Value("${order.stuck.job.from.duration}") Duration fromDuration,
        @Value("${order.stuck.job.to.duration}") Duration toDuration) {
        this.orderModelRepository = orderModelRepository;
        this.rulesService = rulesService;
        this.utcClock = utcClock;
        this.fromDuration = fromDuration;
        this.toDuration = toDuration;
    }

    /**
     * Find orders waiting for item availability response and request it again.
     */
    @Trace
    @Transactional
    @SchedulerLock(name = "stuckOrderTask")
    @Scheduled(cron = "${order.stuck.job.schedule.cron}")
    public void requestItemAvailabilityAgain() {

        Instant from = utcClock.instant().minus(fromDuration);
        Instant to = utcClock.instant().minus(toDuration);

        log.info("Checking orders stuck in routing from {} to {}", from, to);
        orderModelRepository.getOrdersInStatus(OrderLineQuantityStatus.PLACED, from, to)
            .forEach(orderModel -> {
                var facts = new Facts();
                facts.put(FactNames.ORDER_MODEL_FACT, orderModel);

                log.warn("Reprocessing stuck order {}", orderModel.getOrderId());

                rulesService.executeOrderPlacingRules(facts);
        });
    }

}
