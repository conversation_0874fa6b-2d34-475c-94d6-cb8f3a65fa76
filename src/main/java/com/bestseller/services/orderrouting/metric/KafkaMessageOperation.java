package com.bestseller.services.orderrouting.metric;

import lombok.AllArgsConstructor;

/**
 * These operation types are the various operations used throughout the application.
 */
@AllArgsConstructor
public enum KafkaMessageOperation {

    DUPLICATE_ITEM_AVAILABILITY_RESPONSE_MESSAGE("DuplicateItemAvailabilityResponseMessage"),

    DUPLICATE_ORDER_PART_REJECTED_MESSAGE("DuplicateOrderPartRejectedMessage"),

    DUPLICATE_ORDER_FULFILLMENT_MESSAGE("DuplicateOrderForFulfillmentMessage"),

    ORDER_CLEANUP_COUNT("OrdersCleanUpCount");

    private String name;

    @Override
    public String toString() {
        return name;
    }
}
