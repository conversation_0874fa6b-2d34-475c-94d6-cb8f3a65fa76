package com.bestseller.services.orderrouting.metric;

/**
 * Counter operations.
 */
public class CounterOperation {

    public static final String ORDER_PART_REJECTED_CONSUMED = "order.part.rejected.consumed";

    public static final String ORDER_PARTS_ROUTED_PRODUCED = "order.parts.routed.produced";

    public static final String REROUTE_ORDER_PART = "rerouteOrderPart";
    public static final String ORDER_PARTS_ROUTED_LINES = "orderPartsRoutedLines";
    public static final String ORDER_PARTS_ROUTED_VALUE = "orderPartsRoutedValue";
    public static final String TOTAL_ORDER_CANCELLED_VALUE = "totalOrderCancelledValue";

    public static final String ORDER_FOR_FULFILLMENT_CONSUMED = "order.for.fulfillment.consumed";

    public static final String ITEM_AVAILABILITY_REQUEST_PRODUCED = "item.availability.request.produced";

    public static final String ITEM_AVAILABILITY_RESPONSE_CONSUMED = "item.availability.response.consumed";

}
