<configuration>

    <appender name="JSON" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="net.logstash.logback.encoder.LogstashEncoder"/>
    </appender>

    <springProfile name="dev,integration">
        <include resource="org/springframework/boot/logging/logback/base.xml" />
        <logger level="DEBUG" name="org.jeasy.rules"/>
        <root level="INFO" >
            <appender-ref ref="CONSOLE" />
        </root>>
    </springProfile>

    <springProfile name="acc,prod,default">
        <root level="INFO">
            <appender-ref ref="JSON"/>
        </root>
    </springProfile>
</configuration>
