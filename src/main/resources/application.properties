spring.application.name=order-routing-service
management.server.servlet.context-path=/actuator
management.endpoints.web.exposure.include=health,info,bindings

logging.level.root=${LOG_LEVEL:info}

# Environment specific credentials for accessing the endpoints
user.monitor.name=${MON<PERSON>OR_ENDPOINT_USER}
user.monitor.password=${MONITOR_ENDPOINT_PASSWORD}

user.administrator.name=${ADMINISTRATOR_ENDPOINT_USER:administrator}
user.administrator.password=${ADMINISTRATOR_ENDPOINT_PASSWORD:9ZkdyfKG}

# Environment specific Spring Cloud Stream configurations
spring.cloud.stream.kafka.binder.brokers=${KAFKA_BROKER_LIST}
spring.cloud.stream.kafka.binder.defaultBrokerPort=${KAFKA_BROKER_PORT:}
spring.cloud.stream.kafka.binder.configuration.security.protocol=${KAFKA_BROKER_SECURITY_PROTOCOL}
spring.cloud.stream.kafka.binder.configuration.ssl.truststore.location=${KAFKA_SSL_TRUSTSTORE_LOCATION:}
spring.cloud.stream.kafka.binder.configuration.ssl.truststore.password=${KAFKA_SSL_TRUSTSTORE_PASSWORD:}

# Default Spring Cloud Stream configurations
spring.cloud.stream.schemaRegistryClient.enabled=false
spring.cloud.stream.kafka.default.producer.headerPatterns=!*
spring.cloud.stream.default.contentType=application/json
spring.cloud.stream.default.group=${CONSUMER_GROUP}

# NOTE: Please enable auto commit on error for each inbound channel
#
# spring.cloud.stream.kafka.bindings.<channelName>.consumer.autoCommitOnError=true
#
# NOTE: Please enable retrying of message publishing for each outbound channel
#
# spring.cloud.stream.kafka.bindings.<channelName>.producer.configuration.retries=10
#
# NOTE: Please define the message content type to "application/json" for each outbound channel
#
# spring.cloud.stream.kafka.bindings.<channelName>.producer.contentType=application/json
spring.cloud.stream.bindings.orderForFulfillment.destination=${KAFKA_TOPIC_PREFIX}OrderForFulfillment
spring.cloud.stream.bindings.orderForFulfillment.content-type=application/json
spring.cloud.stream.bindings.orderForFulfillment.consumer.maxAttempts=10
spring.cloud.stream.bindings.itemAvailabilityResponse.consumer.backoffPeriod=15000
spring.cloud.stream.bindings.orderForFulfillment.consumer.default-retryable=false
spring.cloud.stream.bindings.orderForFulfillment.consumer.retryable-exceptions.org.springframework.dao.TransientDataAccessException=true
spring.cloud.stream.kafka.bindings.orderForFulfillment.consumer.autoCommitOnError=true
spring.cloud.stream.kafka.bindings.orderForFulfillment.consumer.autoCommitOffset=true

# ItemAvailabilityRequest
spring.cloud.stream.bindings.itemAvailabilityRequest.destination=${KAFKA_TOPIC_PREFIX}ItemAvailabilityRequest
spring.cloud.stream.bindings.itemAvailabilityRequest.contentType=application/json
spring.cloud.stream.kafka.bindings.itemAvailabilityRequest.producer.sync=true
spring.cloud.stream.kafka.bindings.itemAvailabilityRequest.producer.configuration.retries=10

# ItemAvailabilityResponse
spring.cloud.stream.bindings.itemAvailabilityResponse.destination=${KAFKA_TOPIC_PREFIX}ItemAvailabilityResponse
spring.cloud.stream.bindings.itemAvailabilityResponse.consumer.maxAttempts=2
spring.cloud.stream.kafka.bindings.itemAvailabilityResponse.consumer.autoCommitOnError=true
spring.cloud.stream.kafka.bindings.itemAvailabilityResponse.consumer.autoCommitOffset=true

# OrderPartsRouted
spring.cloud.stream.bindings.orderPartsRouted.destination=${KAFKA_TOPIC_PREFIX}OrderPartsRouted
spring.cloud.stream.bindings.orderPartsRouted.contentType=application/json
spring.cloud.stream.kafka.bindings.orderPartsRouted.producer.sync=true
spring.cloud.stream.kafka.bindings.orderPartsRouted.producer.configuration.retries=10

# OrderPartsCreated
spring.cloud.stream.bindings.orderPartsCreated.destination=${KAFKA_TOPIC_PREFIX}OrderPartsCreated
spring.cloud.stream.bindings.orderPartsCreated.contentType=application/json
spring.cloud.stream.kafka.bindings.orderPartsCreated.producer.sync=true
spring.cloud.stream.kafka.bindings.orderPartsCreated.producer.configuration.retries=10

# OrderPartRejected
spring.cloud.stream.bindings.orderPartRejected.destination=${KAFKA_TOPIC_PREFIX}OrderPartRejected
spring.cloud.stream.bindings.orderPartRejected.consumer.maxAttempts=1
spring.cloud.stream.kafka.bindings.orderPartRejected.consumer.autoCommitOnError=true
spring.cloud.stream.kafka.bindings.orderPartRejected.consumer.autoCommitOffset=true

# OrderPartsCancelled
spring.cloud.stream.bindings.orderPartsCancelled.destination=${KAFKA_TOPIC_PREFIX}OrderPartsCancelled
spring.cloud.stream.bindings.orderPartsCancelled.contentType=application/json
spring.cloud.stream.kafka.bindings.orderPartsCancelled.producer.sync=true
spring.cloud.stream.kafka.bindings.orderPartsCancelled.producer.configuration.retries=10

# ItemAvailabilityReservation
spring.cloud.stream.bindings.itemAvailabilityReservation.destination=${KAFKA_TOPIC_PREFIX}ItemAvailabilityReservation
spring.cloud.stream.bindings.itemAvailabilityReservation.contentType=application/json
spring.cloud.stream.kafka.bindings.itemAvailabilityReservation.producer.sync=true
spring.cloud.stream.kafka.bindings.itemAvailabilityReservation.producer.configuration.retries=10

togglz.console.use-management-port=false
togglz.console.enabled=true
togglz.console.path=/features

# Rules properties
rules.shipFromStoreFirst.excludedCarrierVariants=
rules.shipFromStoreSoldOut.excludedCarrierVariants=
rules.crossDockOrders.marketPlaces=otde,ayb2b,ab2bch
rules.shipFromStoreBigOrder.maxSfsSize=${SFS_MAX_PART_SIZE:6}
rules.shipFromStoreBigOrder.threshold=${SFS_BIG_ORDER_THRESHOLD:10}

# Predefined warehouses
warehouse.fulfillment-center-east=INGRAM_MICRO
warehouse.fulfillment-center-west=INGRAM_MICRO_NL

# MySQL datasource
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.url=${SPRING_DATASOURCE_URL}
spring.datasource.username=${SPRING_DATASOURCE_USERNAME}
spring.datasource.password=${SPRING_DATASOURCE_PASSWORD}
spring.datasource.tomcat.max-active=10
spring.datasource.tomcat.max-idle=10

# Name physical database tables exactly as in javax.persistence.Table.name
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl

# Custom banner
spring.banner.location=banner.txt

# Configuration for jobs
# 4:02:30 AM local time
order.cleanup.job.schedule.cron=${ORDER_CLEANUP_JOB_SCHEDULE_CRON:30 2 2 * * ?}
order.cleanup.period.days=${ORDER_CLEANUP_PERIOD_DAYS:30}

order.stuck.job.from.duration=PT120h
order.stuck.job.to.duration=PT1h
order.stuck.job.schedule.cron=0 0/20 * * * *

# Datadog
management.metrics.export.datadog.api-key=${dd.api.key}
management.metrics.export.datadog.application-key=${dd.application.key}
management.metrics.export.datadog.enabled=${DD_ENABLED:false}

# Datadog tags
management.metrics.tags.service=Order Routing Service
management.metrics.tags.service.abbreviation=ORS
management.metrics.tags.service.owner=Ship From Store

