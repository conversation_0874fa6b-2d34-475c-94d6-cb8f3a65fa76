# Environment specific credentials for accessing the endpoints
MONITOR_ENDPOINT_USER=monitoringuser
MONITOR_ENDPOINT_PASSWORD=Ushr68%KY&e26Hus32Hn

# Environment specific configurations for Spring Cloud Stream running with local Apache <PERSON>f<PERSON>
KAFKA_BROKER_LIST=localhost
KAFKA_BROKER_PORT=9092
KAFKA_BROKER_SECURITY_PROTOCOL=PLAINTEXT
KAFKA_TOPIC_PREFIX=
CONSUMER_GROUP=OrderRoutingService

# Amount of time each order and quantity persists in the cache before expiring (infinity).
ORDER_TIME_TO_LIVE_SECONDS=-1

# Enable Ship From Store First (with store priority) in Norway on local environments
RULES_SFS_FIRST_COUNTRIES=NO

# Enable Ship From Store Sold Out Solution (with warehouse priority) on local environments
RULES_SFS_SOS_COUNTRIES=NL

# Spring data source.
SPRING_DATASOURCE_URL=*******************************
SPRING_DATASOURCE_USERNAME=orsuser
SPRING_DATASOURCE_PASSWORD=S3D5EjZb25MS6ZYM

shipfromstoreconfig[0].country=nl
shipfromstoreconfig[0].type=SOLD_OUT
shipfromstoreconfig[0].onlineFlag=PRODUCTION
shipfromstoreconfig[1].country=no
shipfromstoreconfig[1].type=STORES_FIRST
shipfromstoreconfig[1].onlineFlag=PRODUCTION
shipfromstoreconfig[2].country=de
shipfromstoreconfig[2].type=SOLD_OUT
shipfromstoreconfig[2].onlineFlag=PRODUCTION

# Order cleanup job
# Every 30 seconds
ORDER_CLEANUP_JOB_SCHEDULE_CRON=0/30 * * * * ?
ORDER_CLEANUP_PERIOD_DAYS=1

# Datadog
management.metrics.tags.env=dev
