CREATE TABLE Address (
    id                            INT            NOT NULL AUTO_INCREMENT,
    storeId                       VARCHAR(45),
    addressLine1                  VARCHAR(32)    NOT NULL,
    addressLine2                  VARCHAR(32),
    addressLine3                  VARCHAR(32),
    city                          VARCHAR(24)    NOT NULL,
    country                       VARCHAR(4),
    firstName                     VARCHAR(32),
    houseNumber                   VARCHAR(32),
    houseNumberExtended           VARCHAR(32),
    lastName                      VARCHAR(26)    NOT NULL,
    phoneNumber                   VARCHAR(20),
    title                         VARCHAR(10),
    zipcode                       VARCHAR(10),

    PRIMARY KEY (id)
) ENGINE = InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE `Order` (
    orderId                       VARCHAR(50)    NOT NULL,
    placedDate                    DATETIME(3)    NOT NULL,
    isTest                        BIT(1)         NOT NULL DEFAULT 0,
    isOrderAdvice                 BIT(1)         NOT NULL DEFAULT 0,
    holdFromRouting               BIT(1)         NOT NULL DEFAULT 0,
    holdFromRoutingNode           VARCHAR(25),
    customerEmail                 VARCHAR(256),
    externalCustomerNumber        VARCHAR(32),
    billingAddressId              INT            NOT NULL,
    shippingAddressId             INT            NOT NULL,
    orderValue                    double,
    shippingFees                  double,
    shippingFeesTaxPercentage     double,
    orderType                     VARCHAR(50),
    shippingMethod                VARCHAR(20),
    externalOrderNumber           VARCHAR(32),
    carrier                       VARCHAR(50),
    carrierVariant                VARCHAR(25),
    parcelShopType                VARCHAR(15),
    orderCreationDate             DATETIME(3),
    customerId                    VARCHAR(20),
    parcelShop                    BIT(1),
    paymentFees                   double,
    shippingFeesCancelled         BIT(1),
    marketPlace                   VARCHAR(20),
    store                         VARCHAR(20),
    channel                       VARCHAR(50)    NOT NULL,
    brand                         VARCHAR(255),
    actionCode                    VARCHAR(32),
    createdDate                   TIMESTAMP(3)   NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    PRIMARY KEY (orderId),
    FOREIGN KEY (billingAddressId)  REFERENCES Address (id),
    FOREIGN KEY (shippingAddressId) REFERENCES Address (id)
) ENGINE = InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE OrderLineQuantity (
    orderId                       VARCHAR(50)    NOT NULL,
    ean                           VARCHAR(13)    NOT NULL,
    productName                   VARCHAR(255),
    lineNumber                    INT(11)        NOT NULL,
    quantity                      INT            NOT NULL,
    status                        VARCHAR(25)    NOT NULL,
    cancelReason                  VARCHAR(30),
    fulfillmentNode               VARCHAR(25),
    orderPartNumber               INT(11),
    totalOrderParts               INT(11),
    retailPrice                   double        NOT NULL,
    discountValue                 double,
    taxPercentage                 double        NOT NULL,
    isGiftItem                    BIT(1),
    partnerReference              LONGTEXT,
    brand                         VARCHAR(63),

    PRIMARY KEY (orderId, lineNumber),
    FOREIGN KEY (orderId) REFERENCES `Order` (orderId)
) ENGINE = InnoDB DEFAULT CHARSET = utf8;

CREATE TABLE Payment (
    id                            INT            NOT NULL AUTO_INCREMENT,
    name                          VARCHAR(45)    NOT NULL,
    orderId                       VARCHAR(50),
    subMethod                     VARCHAR(20),
    PRIMARY KEY (id),
    FOREIGN KEY (orderId) REFERENCES `Order` (orderId)
) ENGINE = InnoDB DEFAULT CHARSET = utf8;
