ALTER TABLE OrderLineQuantity
  DROP FOREIGN KEY OrderLineQuantity_ibfk_1,
  ADD CONSTRAINT OrderLineQuantity_ibfk_2
    FOREIGN KEY (orderId)
    REFERENCES `Order` (orderId)
    ON DELETE CASCADE;

ALTER TABLE Payment
  DROP FOREIGN KEY Payment_ibfk_1,
  ADD CONSTRAINT Payment_ibfk_2
    FOREIGN KEY (orderId)
    REFERENCES `Order` (orderId)
    ON DELETE CASCADE;

ALTER TABLE `Order`
  DROP FOREIGN KEY Order_ibfk_1,
  DROP FOREIGN KEY Order_ibfk_2,
  ADD CONSTRAINT Order_ibfk_11
    FOREIGN KEY (billingAddressId)
    REFERENCES Address(id)
    ON DELETE CASCADE,
  ADD CONSTRAINT Order_ibfk_22
    FOREIGN KEY (shippingAddressId)
    REFERENCES Address(id)
    ON DELETE CASCADE;

