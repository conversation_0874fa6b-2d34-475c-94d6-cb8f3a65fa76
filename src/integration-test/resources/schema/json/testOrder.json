{"$schema": "http://json-schema.org/draft-04/schema#", "definitions": {}, "id": "http://bestseller.com/testOrder.json", "name": "TestOrder", "title": "An order structure for test purposes only", "properties": {"orderId": {"id": "/properties/properties", "description": "Order identifier", "type": "string"}, "orderDate": {"id": "/properties/orderDate", "description": "The date and time when the order has been placed", "type": "string", "format": "date-time"}, "shippingMethod": {"id": "/properties/shippingMethod", "description": "Identifier of the carrier", "type": "string"}, "totalAmount": {"id": "/properties/totalAmount", "description": "Total amount of items (excl. VAT)", "type": "number"}}, "required": ["orderDate", "totalAmount"], "type": "object"}