package com.bestseller.services.orderrouting.web.controller;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.web.context.WebApplicationContext;

import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.httpBasic;
import static org.springframework.security.test.web.servlet.setup.SecurityMockMvcConfigurers.springSecurity;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import static org.springframework.test.web.servlet.setup.MockMvcBuilders.webAppContextSetup;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("dev")
public class ActuatorSecurityIntegrationTest {
    private static final String TEST_INVALID_USER = "testInvalidUser";
    private static final String TEST_INVALID_PASS = "testInvalidPass";
    private static final String ACTUATOR_INFO_ENDPOINT = "/actuator/info";
    private static final String ACTUATOR_HEALTH_ENDPOINT = "/actuator/health";
    private static final String FEATURES_ENDPOINT = "/features/index";

    @Autowired
    private WebApplicationContext wac;

    @Value("${user.monitor.name}")
    private String monitorEndpointUser;

    @Value("${user.monitor.password}")
    private String monitorEndpointPassword;

    private MockMvc mockMvc;

    @Before
    public void setUp() throws Exception {
        mockMvc = webAppContextSetup(wac).apply(springSecurity()).build();
    }

    @Test
    public void pingActuatorInfoEndpoint_withoutAuthentication_returnsOkHttpStatus() throws Exception {
        //act
        mockMvc.perform(get(ACTUATOR_INFO_ENDPOINT)).andExpect(status().isOk());
    }

    @Test
    public void pingActuatorInfoEndpoint_validAuthentication_returnsOkHttpStatus() throws Exception {
        //act
        mockMvc.perform(get(ACTUATOR_INFO_ENDPOINT).with(httpBasic(monitorEndpointUser, monitorEndpointPassword)))
               .andExpect(status().isOk());
    }

    @Test
    public void pingActuatorLogsEndpoint_validAuthentication_returnsOkHttpStatus() throws Exception {
        //act
        mockMvc.perform(get(ACTUATOR_HEALTH_ENDPOINT).with(httpBasic(monitorEndpointUser, monitorEndpointPassword)))
               .andExpect(status().isOk());
    }

    @Test
    public void pingActuatorLogsEndpoint_invalidAuthentication_returnsUnauthorizedHttpStatus() throws Exception {
        //act
        mockMvc.perform(get(ACTUATOR_HEALTH_ENDPOINT).with(httpBasic(TEST_INVALID_USER, TEST_INVALID_PASS)))
               .andExpect(status().isUnauthorized());
    }

    @Test
    public void pingFeaturesEndpoint_invalidAuthentication_returnsUnauthorizedHttpStatus() throws Exception {
        //act
        mockMvc.perform(get(FEATURES_ENDPOINT).with(httpBasic(TEST_INVALID_USER, TEST_INVALID_PASS)))
               .andExpect(status().isUnauthorized());
    }
}
