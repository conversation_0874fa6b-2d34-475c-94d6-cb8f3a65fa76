package com.bestseller.services.orderrouting.jobs;

import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.model.OrderLineQuantityStatus;
import com.bestseller.services.orderrouting.model.OrderModel;
import com.bestseller.services.orderrouting.repository.OrderModelRepository;
import com.bestseller.services.orderrouting.utils.generator.OrderModelGenerator;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.hasItem;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.not;


/**
 * Class used for testing Scheduled tasks.
 */
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.MOCK)
@ActiveProfiles("dev")
public class CleanUpOrderTaskIntegrationTest {

    @Autowired
    private OrderModelRepository orderModelRepository;

    @Autowired
    private CleanUpOrderTask cleanUpOrderTask;

    private OrderModel notSoOldOrder;

    @Before
    public void setUp() {
        notSoOldOrder = OrderModelGenerator.createTestOrderModel();
        notSoOldOrder.setOrderLineQuantities(OrderModelGenerator.createThreeTestOrderLines(notSoOldOrder));
        notSoOldOrder.setPayments(OrderModelGenerator.createTwoPayments(notSoOldOrder));
        notSoOldOrder = orderModelRepository.save(notSoOldOrder);
    }

    @Test
    public void removeOldRecords_givenOneOldOrderAndOneRecentWithAllStateMatched_orderIsRemoved() {
        // arrange
        var oldOrder = OrderModelGenerator.createTestOrderModel();

        //All orderLines are either in ROUTED or CANCELLED state
        List<OrderLineQuantityModel> orderLineQuantityModelList = OrderModelGenerator.createThreeTestOrderLines(oldOrder);
        orderLineQuantityModelList.get(0).setStatus(OrderLineQuantityStatus.ROUTED);
        orderLineQuantityModelList.get(1).setStatus(OrderLineQuantityStatus.ROUTED);
        orderLineQuantityModelList.get(2).setStatus(OrderLineQuantityStatus.CANCELLED);

        oldOrder.setOrderLineQuantities(orderLineQuantityModelList);
        oldOrder.setPayments(OrderModelGenerator.createTwoPayments(oldOrder));
        oldOrder = orderModelRepository.save(oldOrder);

        // Set creation date to a date that it should be removed
        oldOrder.setCreatedDate(Instant.now().minus(365, ChronoUnit.DAYS));
        oldOrder = orderModelRepository.save(oldOrder);

        var ordersBeforeRemovalCount = orderModelRepository.count();

        // act
        cleanUpOrderTask.removeOldRecords();

        // assert
        var ordersAfterRemovalCount = orderModelRepository.count();
        var ordersAfterRemoval = orderModelRepository.findAll();

        assertThat(String.format("%d - 1 Order should be present after removal task execution", ordersBeforeRemovalCount),
            ordersAfterRemovalCount, is(equalTo(ordersBeforeRemovalCount - 1)));

        List<String> orderIdsAfterExecution = StreamSupport.stream(ordersAfterRemoval.spliterator(), false)
                .map(OrderModel::getOrderId)
                .collect(Collectors.toList());

        assertThat("Recent order id is present on the orders after task execution", orderIdsAfterExecution,
                hasItem(notSoOldOrder.getOrderId()));
        assertThat("Old order id is not present on the orders after task execution", orderIdsAfterExecution,
                not(hasItem(oldOrder.getOrderId())));
    }

    @Test
    public void removeOldRecords_givenOneOldOrderAndOneRecentWithNotAllStateMatched_orderIsNotRemoved() {
        // arrange
        var oldOrder = OrderModelGenerator.createTestOrderModel();

        //One orderLine is in PLACED state
        List<OrderLineQuantityModel> orderLineQuantityModelList = OrderModelGenerator.createThreeTestOrderLines(oldOrder);
        orderLineQuantityModelList.get(0).setStatus(OrderLineQuantityStatus.ROUTED);
        orderLineQuantityModelList.get(1).setStatus(OrderLineQuantityStatus.ROUTED);
        orderLineQuantityModelList.get(2).setStatus(OrderLineQuantityStatus.PLACED);

        oldOrder.setOrderLineQuantities(orderLineQuantityModelList);
        oldOrder.setPayments(OrderModelGenerator.createTwoPayments(oldOrder));
        oldOrder = orderModelRepository.save(oldOrder);

        // Set creation date to a date that it should be removed
        oldOrder.setCreatedDate(Instant.now().minus(365, ChronoUnit.DAYS));
        oldOrder = orderModelRepository.save(oldOrder);

        var ordersBeforeRemovalCount = orderModelRepository.count();

        // act
        cleanUpOrderTask.removeOldRecords();

        // assert
        var ordersAfterRemoval = orderModelRepository.findAll();
        var ordersAfterRemovalCount = orderModelRepository.count();

        assertThat("Orders should be equal before and after removal task execution", ordersBeforeRemovalCount,
                is(equalTo(ordersAfterRemovalCount)));

        List<String> orderIdsAfterExecution = StreamSupport.stream(ordersAfterRemoval.spliterator(), false)
                .map(OrderModel::getOrderId)
                .collect(Collectors.toList());

        assertThat("Recent order id is present on the orders after task execution", orderIdsAfterExecution,
                hasItem(notSoOldOrder.getOrderId()));
        assertThat("Old order id is present on the orders after task execution", orderIdsAfterExecution,
                (hasItem(oldOrder.getOrderId())));
    }
}
