package com.bestseller.services.orderrouting.repository;

import com.bestseller.services.orderrouting.model.AddressModel;
import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.model.OrderModel;
import com.bestseller.services.orderrouting.model.PaymentModel;
import com.bestseller.services.orderrouting.service.CancellationReason;
import com.bestseller.services.orderrouting.utils.generator.OrderModelGenerator;
import org.assertj.core.api.Assertions;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.CollectionUtils;

import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static com.bestseller.services.orderrouting.constants.FulfillmentNodeConstants.FULFILLMENT_CENTER_EAST;
import static com.bestseller.services.orderrouting.constants.FulfillmentNodeConstants.SHIP_FROM_STORE_NL;
import static java.util.Arrays.asList;
import static org.hamcrest.Matchers.containsInAnyOrder;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.in;
import static org.hamcrest.Matchers.not;
import static org.junit.Assert.assertThat;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("dev")
public class OrderModelRepositoryIntegrationTest {

    @Autowired
    private OrderModelRepository orderModelRepository;

    @Test
    @Transactional
    public void findById_orderSaved_orderFound() {
        // arrange
        OrderModel input = createMaximalOrder();

        // act
        OrderModel saved = saveOrderModel(input);
        OrderModel found = findOrderModel(input).orElseThrow();

        // assert
        Assertions.assertThat(found).usingRecursiveComparison()
            .isEqualTo(saved);
        assertThat(found.toString(), equalTo(saved.toString()));

        input.getBillingAddress().setId(saved.getBillingAddress().getId());
        input.getShippingAddress().setId(saved.getShippingAddress().getId());
        input.setPayments(saved.getPayments());
        input.getAdditionalOrderInformation().forEach(item -> item.setOrderId(input.getOrderId()));
        Assertions.assertThat(found)
            .usingRecursiveComparison()
            .ignoringActualNullFields()
            .ignoringFields("additionalOrderInformation")
            .isEqualTo(input);

        Assertions.assertThat(found.getAdditionalOrderInformation())
            .usingElementComparatorIgnoringFields("id")
            .containsExactlyElementsOf(found.getAdditionalOrderInformation());
    }

    @Test
    @Transactional
    public void findById_orderLineSaved_orderLineFound() {
        // arrange
        OrderModel input = createMaximalOrder();
        OrderLineQuantityModel line = createMaximalOrderLine(input);
        input.setOrderLineQuantities(Collections.singletonList(line));

        // act
        OrderModel saved = saveOrderModel(input);
        OrderModel found = findOrderModel(input).get();


        // assert
        stripTrailingZeros(saved);
        stripTrailingZeros(input);
        stripTrailingZeros(found);

        assertThat(found, not(equalTo(Optional.empty())));
        assertThat(found.getOrderLineQuantities(), containsInAnyOrder(saved.getOrderLineQuantities().toArray()));
        assertThat(found.getOrderLineQuantities(), containsInAnyOrder(input.getOrderLineQuantities().toArray()));
    }

    @Test
    @Transactional
    public void findById_manyOrderLinesSaved_orderLinesFound() {
        // arrange
        OrderModel input = createMinimalOrder();
        input.setOrderLineQuantities(asList(
                createMinimalOrderLine(input,-10),
                createMinimalOrderLine(input,1000),
                createMinimalOrderLine(input,42)
        ));

        // act
        OrderModel saved = saveOrderModel(input);
        Optional<OrderModel> found = findOrderModel(input);

        // assert
        stripTrailingZeros(saved);
        stripTrailingZeros(input);
        stripTrailingZeros(found.get());

        assertThat(found, not(equalTo(Optional.empty())));
        assertThat(found.get().getOrderLineQuantities(), containsInAnyOrder(saved.getOrderLineQuantities().toArray()));
        assertThat(found.get().getOrderLineQuantities(), containsInAnyOrder(input.getOrderLineQuantities().toArray()));
    }

    private void updateOrderLines(final OrderModel orderModel) {
        orderModel.getOrderLineQuantities().forEach(ol -> ol.setOrderId(orderModel.getOrderId()));
    }

    private OrderLineQuantityModel createMaximalOrderLine(OrderModel orderModel) {
        OrderLineQuantityModel line = createMinimalOrderLine(orderModel);
        line.setCancelReason(CancellationReason.STORE_REJECTION);
        line.setOrderPartNumber(-1000000);
        line.setTotalOrderParts(-1);
        line.setIsGiftItem(true);
        line.setPartnerReference("TEST_REFERENCE");
        line.setBrand("NameIt");
        line.setOrderId(orderModel.getOrderId());
        return line;
    }

    private OrderLineQuantityModel createMinimalOrderLine(OrderModel orderModel, int lineNumber) {
        OrderLineQuantityModel line = createMinimalOrderLine(orderModel);
        line.setLineNumber(lineNumber);
        return line;
    }

    private OrderLineQuantityModel createMinimalOrderLine(OrderModel orderModel) {
        OrderLineQuantityModel minimalOrderLine = OrderModelGenerator.createOneTestOrderLine(orderModel);
        minimalOrderLine.setRetailPrice(new BigDecimal("19.99"));
        minimalOrderLine.setFulfillmentNode(SHIP_FROM_STORE_NL);
        return minimalOrderLine;
    }

    private OrderModel createMaximalOrder() {
        OrderModel maximalOrder = createMinimalOrder();
        maximalOrder.setPayments(createMinimalPayment(maximalOrder));
        maximalOrder.setIsTest(true);
        maximalOrder.setIsOrderAdvice(true);
        maximalOrder.setHoldFromRouting(true);
        maximalOrder.setHoldFromRoutingNode(FULFILLMENT_CENTER_EAST);
        return maximalOrder;
    }

    private OrderModel createMinimalOrder() {
        OrderModel minimalOrder = OrderModelGenerator.createTestOrderModel();
        for (AddressModel address : asList(minimalOrder.getBillingAddress(), minimalOrder.getShippingAddress())) {
            address.setCountry("NL");
            address.setPhoneNumber("+***********");
            address.setTitle("TEST_TITLE");
            address.setZipcode("1058XB");
            address.setState("");
        }
        minimalOrder.setCustomerId("690cec2937144b44acfaa9e3bd19986e");
        updateOrderLines(minimalOrder);
        return minimalOrder;
    }

    private List<PaymentModel> createMinimalPayment(OrderModel orderModel){
       return OrderModelGenerator.createTwoPayments(orderModel);
    }


    private void stripTrailingZeros(OrderModel orderModel){

        orderModel.setOrderValue(orderModel.getOrderValue().stripTrailingZeros());
        orderModel.setShippingFees(orderModel.getShippingFees().stripTrailingZeros());
        orderModel.setShippingFeesTaxPercentage(orderModel.getShippingFeesTaxPercentage().stripTrailingZeros());

        if (!CollectionUtils.isEmpty(orderModel.getOrderLineQuantities())) {

            for (OrderLineQuantityModel orderLineQuantityModel : orderModel.getOrderLineQuantities()) {
                orderLineQuantityModel.setDiscountValue(orderLineQuantityModel.getDiscountValue().stripTrailingZeros());
                orderLineQuantityModel.setRetailPrice(orderLineQuantityModel.getRetailPrice().stripTrailingZeros());
                orderLineQuantityModel.setTaxPercentage(orderLineQuantityModel.getTaxPercentage().stripTrailingZeros());
            }
        }
    }


    public OrderModel saveOrderModel(OrderModel input) {
        return orderModelRepository.save(input);
    }

    public Optional<OrderModel> findOrderModel(OrderModel input) {
        return orderModelRepository.findById(input.getOrderId());
    }
}
