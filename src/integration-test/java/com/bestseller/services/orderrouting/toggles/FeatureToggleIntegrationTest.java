package com.bestseller.services.orderrouting.toggles;

import com.bestseller.services.orderrouting.feature.toggles.ORSFeatures;
import com.bestseller.services.orderrouting.model.FeatureModel;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.data.repository.CrudRepository;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.togglz.core.context.FeatureContext;
import org.togglz.core.repository.FeatureState;
import org.togglz.core.repository.StateRepository;
import org.togglz.spring.util.ContextClassLoaderApplicationContextHolder;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

/**
 * Class used for testing messaging with Spring Cloud Stream.
 */
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("dev")
public class FeatureToggleIntegrationTest implements ApplicationContextAware {
    private static final String ACTIVATION_STRATEGY_ID = "strategy";

    @Autowired
    private CrudRepository<FeatureModel, String> featureModelRepository;

    @Autowired
    private StateRepository stateRepository;

    @Before
    public void setUp() throws Exception {
        // clean up the feature
        featureModelRepository.deleteById(ORSFeatures.ROUTE_ORDERS.name());
    }

    @Test
    public void isActive_featureNotSet_returnsDefaultValue() throws Exception {
        // arrange

        // act

        // assert
        assertTrue("Should set the feature enabled by default", ORSFeatures.ROUTE_ORDERS.isActive());
    }

    @Test
    public void isActive_featureSetToFalse_returnsFalse() throws Exception {
        // arrange
        stateRepository.setFeatureState(new FeatureState(ORSFeatures.ROUTE_ORDERS, false));

        // act

        // assert
        assertFalse("Should return the feature active state as persisted in Aurora", ORSFeatures.ROUTE_ORDERS.isActive());
    }

    @Test
    public void isActive_featureSetToTrue_returnsTrue() {
        stateRepository.setFeatureState(new FeatureState(ORSFeatures.ROUTE_ORDERS, true));

        // act

        // assert
        assertTrue("Should return the feature active state as persisted in Aurora", ORSFeatures.ROUTE_ORDERS.isActive());
    }

    @Test
    public void getActivationStrategyId_activationStrategyIdSetSet_returnsActivationStrategyId() {
        stateRepository.setFeatureState(new FeatureState(ORSFeatures.ROUTE_ORDERS, true).setStrategyId(ACTIVATION_STRATEGY_ID));

        // act

        // assert
        assertEquals("Should return the activation strategy ID value",
                FeatureContext.getFeatureManager().getFeatureState(ORSFeatures.ROUTE_ORDERS).getStrategyId(), ACTIVATION_STRATEGY_ID);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        try {
            ContextClassLoaderApplicationContextHolder.bind(applicationContext);
        } catch (IllegalStateException e) {

        }
    }
}
