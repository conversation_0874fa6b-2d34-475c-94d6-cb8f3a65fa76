package com.bestseller.services.orderrouting.utils;

import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.model.OrderLineQuantityStatus;
import com.bestseller.services.orderrouting.model.OrderModel;
import com.bestseller.services.orderrouting.repository.OrderLineQuantityModelRepository;
import com.bestseller.services.orderrouting.repository.OrderModelRepository;
import com.github.npathai.hamcrestopt.OptionalMatchers;
import lombok.AllArgsConstructor;

import java.text.MessageFormat;
import java.time.Duration;

import static org.assertj.core.api.Assertions.assertThat;
import static org.awaitility.Awaitility.await;
import static org.hamcrest.Matchers.equalTo;

/**
 * This class uses the Awaitility classes to ensure an order is updated in the ORS cache.
 * The reason is that the cache is asynchronous and updating it is not immediately reflected.
 * This may lead to flaky tests.
 *
 */
@AllArgsConstructor
public class CacheSupervisor {
    private static final Duration DURATION = Duration.ofSeconds(30);

    private OrderModelRepository orderModelRepository;
    private OrderLineQuantityModelRepository orderLineQuantityModelRepository;

    /**
     * The method waits until the number of order lines in the order record becomes the expected number.
     * @param orderId the order ID to search in the cache
     * @param count the size of order lines
     */
    public long waitUntilLineSizeEquals(String orderId, long count) {
        return await(MessageFormat.format("Number of order lines for order {0} do not match {1}", orderId, count))
                .atMost(DURATION).until(() -> orderLineQuantityModelRepository.countByOrderId(orderId), equalTo(count));
    }

    /**
     * The method waits until the order is found in the ORS cache.
     * @param orderId the order ID to wait for
     */
    public OrderModel waitUntilOrderFound(String orderId) {
        return await(MessageFormat.format("Order {0} should be present in the cache", orderId))
                .atMost(DURATION).until(() -> orderModelRepository.findById(orderId), OptionalMatchers.isPresent()).get();
    }

    /**
     * The method waits until the order line quantity at the specified order line is reduced.
     * @param orderId the order ID to wait for
     * @param orderLineId the line ID to monitor
     * @param quantity the expected quantity
     */
    public void waitUntilLineQuantityEquals(String orderId, OrderLineQuantityModel.PrimaryKey orderLineId, int quantity) {
        await(MessageFormat.format("Order {0} at line ID {1} should have quantity of {2}", orderId, orderLineId, quantity))
                .atMost(DURATION).untilAsserted(() -> assertThat(findOrderLine(orderLineId)).hasFieldOrPropertyWithValue("quantity", quantity));
    }

    /**
     * Waits until the given order line is updated with the expected status, fulfillmentNode and orderPartNumber.
     * @param orderLineId
     * @param expectedStatus
     * @param expectedWarehouse
     */
    public void waitUntilOrderLineUpdated(OrderLineQuantityModel.PrimaryKey orderLineId,
                                          OrderLineQuantityStatus expectedStatus,
                                          String expectedWarehouse) {
        await(MessageFormat.format("Order line {0} should have value for status = {1}", orderLineId, expectedStatus))
                .atMost(DURATION)
                .untilAsserted(() -> assertThat(findOrderLine(orderLineId)).hasFieldOrPropertyWithValue("status", expectedStatus));

        await(MessageFormat.format("Order line {0} should have value for fulfillmentNode = {1}", orderLineId, expectedWarehouse))
                .atMost(DURATION)
                .untilAsserted(() -> assertThat(findOrderLine(orderLineId)).hasFieldOrPropertyWithValue("fulfillmentNode", expectedWarehouse));
    }

    /**
     * Waits until the given order line is updated with the expected status, fulfillmentNode and orderPartNumber.
     *
     * @param orderLineId
     * @param expectedStatus
     */
    public void waitUntilOrderLineUpdated(OrderLineQuantityModel.PrimaryKey orderLineId, OrderLineQuantityStatus expectedStatus) {
        waitUntilOrderLineUpdated(orderLineId, expectedStatus, null);
    }

    /**
     * Waits until it verifies that the given order`s attribute has been updated with the expected value.
     * @param orderId
     * @param attributeName
     * @param expectedAttributeValue
     */
    public void waitUntilOrderUpdated(String orderId, String attributeName, Object expectedAttributeValue) {
        await(MessageFormat.format("Order {0} should have value for attribute {1} = {2}", orderId, attributeName,
                                   expectedAttributeValue))
                .atMost(DURATION)
                .untilAsserted(() -> assertThat(findOrder(orderId)).hasFieldOrPropertyWithValue(attributeName, expectedAttributeValue));
    }

    /**
     * Find order
     * @param orderId order id
     * @return order
     */
    public OrderModel findOrder(String orderId) {
        return orderModelRepository.findById(orderId).get();
    }

    public OrderLineQuantityModel findOrderLine(OrderLineQuantityModel.PrimaryKey orderLineId) {
        return orderLineQuantityModelRepository.findById(orderLineId).get();
    }
}
