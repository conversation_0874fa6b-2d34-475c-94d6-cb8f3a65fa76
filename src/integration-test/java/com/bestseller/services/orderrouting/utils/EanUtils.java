package com.bestseller.services.orderrouting.utils;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderForFulfillment.OrderForFulfillment;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsCreated.OrderPart;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsCreated.OrderPartsCreated;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsRouted.OrderPartsRouted;
import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.model.OrderModel;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

public class EanUtils {
    public static List<String> eans(OrderPartsRouted... orderPartsRouted) {
        return Arrays.stream(orderPartsRouted)
                .map(OrderPartsRouted::getOrderLines)
                .flatMap(List::stream)
                .map(OrderLine::getEan)
                .collect(Collectors.toList());

    }

    public static List<String> eans(OrderPartsCreated... orderPartsCreated) {
        return Arrays.stream(orderPartsCreated)
                .map(OrderPartsCreated::getOrderParts)
                .flatMap(Collection::stream)
                .map(OrderPart::getOrderLines)
                .flatMap(Collection::stream)
                .map(OrderLine::getEan)
                .collect(Collectors.toList());

    }

    public static List<String> eans(List<OrderLineQuantityModel> orderLineQuantityModels) {
        return orderLineQuantityModels.stream()
                .map(OrderLineQuantityModel::getEan)
                .collect(Collectors.toList());
    }

    public static List<String> eans(OrderModel orderModel) {
        return eans(orderModel.getOrderLineQuantities());
    }

    public static List<String> eans(OrderForFulfillment orderForFulfillment) {
        return orderForFulfillment.getOrderLines()
                .stream()
                .map(OrderLine::getEan)
                .collect(Collectors.toList());
    }
}
