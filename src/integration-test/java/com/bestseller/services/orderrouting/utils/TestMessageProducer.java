package com.bestseller.services.orderrouting.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.Producer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.apache.kafka.common.serialization.LongSerializer;
import org.apache.kafka.common.serialization.StringSerializer;
import org.junit.rules.ExternalResource;

import java.text.MessageFormat;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

/**
 * A generic producer that would post messages to a topic with a given name. Should be used as a JUnit Rule like this:
 *
 * <p>
 *     {@literal @}Rule
 *     public TestMessageProducer messageProducer = new TestMessageProducer();
 *
 *     messageProducer.publishMessage(ORDER_FOR_FULFILLMENT, message);
 * </p>
 */
@Slf4j
public class TestMessageProducer extends ExternalResource {
    private static final String CLIENT_ID = "{0}-TESTS-{1}";
    private static final String BOOTSTRAP_SERVERS_CONFIG = "{0}:{1}";
    private static final int CLOSE_TIMEOUT_MS = 5000;
    private static final String CONSUMER_GROUP = "OrderRoutingService";
    private static final String DEFAULT_KAFKA_BROKER_LIST = "localhost";
    private static final String DEFAULT_KAFKA_BROKER_PORT = "9092";

    private String broker;
    private String port;
    private String group;
    private Producer<Long, String> producer;
    private Map<String, Object> producerProperties = new HashMap<>();
    private ObjectMapper objectMapper = new ObjectMapper();

    /**
     * Test message producer.
     */
    public TestMessageProducer() {
        super();
        this.broker = Optional.ofNullable(System.getenv("KAFKA_BROKER_LIST")).orElse(DEFAULT_KAFKA_BROKER_LIST);
        this.port = Optional.ofNullable(System.getenv("KAFKA_BROKER_PORT")).orElse(DEFAULT_KAFKA_BROKER_PORT);
        this.group = CONSUMER_GROUP;
        log.info("TestMessageProducer found these properties: broker={}, port={}, group={}", broker, port, group);
    }

    /**
     * Posts a text message to the topic.
     * @param topic where to publish the message
     * @param message the message payload
     */
    public <T> void publishMessage(String topic, T message) throws Exception {
        String payload = objectMapper.writeValueAsString(message);
        log.info("Publishing of message {} to topic {}", payload, topic);
        Future<RecordMetadata> recordMetadataFuture = producer.send(new ProducerRecord<>(topic, payload));
        producer.flush();
        RecordMetadata recordMetadata = recordMetadataFuture.get();
        log.info("Sending of message returned {}", recordMetadata.toString());
    }

    /**
     * Posts a bulk set of messages to the topic.
     * @param topic where to publish the message
     * @param messages the messages payloads
     */
    public <T> void publishMessages(String topic, Iterable<T> messages) throws Exception {
        for (final T message : messages) {
            String payload = objectMapper.writeValueAsString(message);
            log.info("Publishing of message {} to topic {}", payload, topic);
            Future<RecordMetadata> recordMetadataFuture = producer.send(new ProducerRecord<>(topic, payload));
            RecordMetadata recordMetadata = recordMetadataFuture.get();
            log.info("Sending of message returned {}", recordMetadata.toString());
        }

        producer.flush();
    }

    @Override
    protected void before() throws Throwable {
        log.info("Initializing KafkaProducer for testing purposes...");
        // configure the Apache Kafka Producer
        producerProperties.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG,
                MessageFormat.format(BOOTSTRAP_SERVERS_CONFIG, broker, port));
        producerProperties.put(ProducerConfig.CLIENT_ID_CONFIG,
                MessageFormat.format(CLIENT_ID, group, Thread.currentThread().getName()));
        producerProperties.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, LongSerializer.class.getName());
        producerProperties.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());

        // create Apache Kafka Producer
        producer = new KafkaProducer<>(producerProperties);

        // configure Jackson Object Mapper
        objectMapper.findAndRegisterModules();
    }

    @Override
    protected void after() {
        log.info("Destroying the test KafkaProducer...");
        producer.close(CLOSE_TIMEOUT_MS, TimeUnit.MILLISECONDS);
        log.info("Test KafkaProducer successfully destroyed");
    }
}
