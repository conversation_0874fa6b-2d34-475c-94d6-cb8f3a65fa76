package com.bestseller.services.orderrouting.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.common.TopicPartition;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.kafka.support.serializer.JsonDeserializer;

import java.time.Duration;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;
import java.util.stream.StreamSupport;

/**
 * Common steps related to consuming Kafka messages used across different tests.
 */
@Slf4j
public class TestConsumer<M> {

    private static final int PARTITIONS = 1;

    private final KafkaConsumer<String, M> consumer;
    private final List<TopicPartition> partitions;

    public TestConsumer(Class<M> messageClass, String topic) {
        String brokerList = Optional.ofNullable(System.getenv("KAFKA_BROKER_LIST")).orElse("localhost") + ":9092";
        consumer = new KafkaConsumer<>(Map.of(
                ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, brokerList,
                ConsumerConfig.GROUP_ID_CONFIG, TestConsumer.class.toString()
        ), new StringDeserializer(), new JsonDeserializer<>(messageClass));
        partitions = IntStream.range(0, PARTITIONS)
                .mapToObj(i -> new TopicPartition(topic, i))
                .collect(Collectors.toList());
    }

    public Iterator<M> consumeFromHere() {
        consumer.assign(partitions);
        magicallyEnsureConsumptionHasOfficiallyStarted(consumer);
        return asStream(() -> consumer.poll(Duration.ofMillis(Long.MAX_VALUE))).iterator();
    }

    /**
     * For unknown reasons, calling this methods makes partition assignment take effect.
     */
    private static <K, M> void magicallyEnsureConsumptionHasOfficiallyStarted(KafkaConsumer<K, M> consumer) {
        Map<TopicPartition, Long> offsets = consumer.assignment()
                .stream()
                .collect(Collectors.toMap(Function.identity(), consumer::position));
        log.info("Consuming since offsets {}", offsets);
    }

    /**
     * Consume messages from a topic as an infinite stream.
     */
    private static <K, M> Stream<M> asStream(Supplier<ConsumerRecords<K, M>> poll) {
        return Stream.generate(poll)
                .peek(records -> log.info("Fetched {} messages", records.count()))
                .map(ConsumerRecords::spliterator)
                .flatMap(spliterator -> StreamSupport.stream(spliterator, false))
                .map(ConsumerRecord::value)
                .peek(record -> log.info("Fetched message {}", record));
    }
}

