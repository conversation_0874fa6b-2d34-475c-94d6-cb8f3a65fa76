package com.bestseller.services.orderrouting.messaging.validation;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderForFulfillment.OrderForFulfillment;
import com.bestseller.services.orderrouting.messaging.consumer.OrderForFulfillmentMessageConsumer;
import com.bestseller.services.orderrouting.messaging.exceptions.ProcessingException;
import com.bestseller.services.orderrouting.utils.ExpectedExceptionUtils;
import com.bestseller.services.orderrouting.utils.generator.OrderForFulfillmentGenerator;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.validation.ConstraintViolationException;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.MOCK)
@ActiveProfiles("dev")
public class OrderForFulfillmentAddressValidationIntegrationTest extends ValidationBase {
    private static final String CUSTOMER_INFORMATION_PATH =
        "receiveOrderForFulfillment.orderForFulfillment.customerInformation";
    private static final String BILLING_ADDRESS_PATH =
        "receiveOrderForFulfillment.orderForFulfillment.customerInformation.billingAddress";
    private static final String BILLING_ADDRESS_LINE1_PATH =
        "receiveOrderForFulfillment.orderForFulfillment.customerInformation.billingAddress.addressLine1";
    private static final String BILLING_ADDRESS_LAST_NAME_PATH =
        "receiveOrderForFulfillment.orderForFulfillment.customerInformation.billingAddress.lastName";
    private static final String BILLING_ADDRESS_CITY_PATH =
        "receiveOrderForFulfillment.orderForFulfillment.customerInformation.billingAddress.city";
    private static final String SHIPPING_INFORMATION_PATH =
        "receiveOrderForFulfillment.orderForFulfillment.shippingInformation";
    private static final String SHIPPING_ADDRESS_PATH =
        "receiveOrderForFulfillment.orderForFulfillment.shippingInformation.shippingAddress";
    private static final String SHIPPING_ADDRESS_LINE1_PATH =
        "receiveOrderForFulfillment.orderForFulfillment.shippingInformation.shippingAddress.addressLine1";
    private static final String SHIPPING_ADDRESS_LAST_NAME_PATH =
        "receiveOrderForFulfillment.orderForFulfillment.shippingInformation.shippingAddress.lastName";
    private static final String SHIPPING_ADDRESS_CITY_PATH =
        "receiveOrderForFulfillment.orderForFulfillment.shippingInformation.shippingAddress.city";

    @Rule
    public ExpectedException thrown = ExpectedException.none();

    @Autowired
    private OrderForFulfillmentMessageConsumer orderForFulfillmentMessageConsumer;

    private OrderForFulfillment message;

    @Before
    public void setUp() {
        message = OrderForFulfillmentGenerator.createFullOrderForFulfillmentMessage();
    }

    @Test
    public void consume_nullCustomerInformation_exceptionThrown() throws ValidationException, ProcessingException {
        // arrange
        message.setCustomerInformation(null);

        // assert
        ExpectedExceptionUtils
            .expect(thrown, ConstraintViolationException.class, MUST_NOT_BE_NULL, CUSTOMER_INFORMATION_PATH);

        // act
        orderForFulfillmentMessageConsumer.receiveOrderForFulfillment(message);
    }

    @Test
    public void consume_nullBillingAddress_exceptionThrown() throws ValidationException, ProcessingException {
        // arrange
        message.getCustomerInformation().setBillingAddress(null);

        // assert
        ExpectedExceptionUtils.expect(thrown, ConstraintViolationException.class, MUST_NOT_BE_NULL, BILLING_ADDRESS_PATH);

        // act
        orderForFulfillmentMessageConsumer.receiveOrderForFulfillment(message);
    }

    @Test
    public void consume_nullBillingAddressLine1_exceptionThrown() throws ValidationException, ProcessingException {
        // arrange
        message.getCustomerInformation().getBillingAddress().setAddressLine1(null);

        // assert
        ExpectedExceptionUtils
            .expect(thrown, ConstraintViolationException.class, MUST_NOT_BE_NULL, BILLING_ADDRESS_LINE1_PATH);

        // act
        orderForFulfillmentMessageConsumer.receiveOrderForFulfillment(message);
    }

    @Test
    public void consume_emptyBillingAddressLine1_exceptionThrown() throws ValidationException, ProcessingException {
        // arrange
        message.getCustomerInformation().getBillingAddress().setAddressLine1(EMPTY_STRING);

        // assert
        ExpectedExceptionUtils.expect(thrown, ConstraintViolationException.class, SIZE_DOES_NOT_MATCH,
            BILLING_ADDRESS_LINE1_PATH, String.valueOf(Integer.MAX_VALUE));

        // act
        orderForFulfillmentMessageConsumer.receiveOrderForFulfillment(message);
    }

    @Test
    public void consume_blankBillingAddressLine1_exceptionThrown() throws ValidationException, ProcessingException {
        // arrange
        message.getCustomerInformation().getBillingAddress().setAddressLine1(BLANK_STRING);

        // assert
        ExpectedExceptionUtils
            .expect(thrown, ConstraintViolationException.class, CONTENT_NOT_ALLOWED, BILLING_ADDRESS_LINE1_PATH);

        // act
        orderForFulfillmentMessageConsumer.receiveOrderForFulfillment(message);
    }

    @Test
    public void consume_nullBillingAddressLastName_exceptionThrown() throws ValidationException, ProcessingException {
        // arrange
        message.getCustomerInformation().getBillingAddress().setLastName(null);

        // assert
        ExpectedExceptionUtils.expect(thrown, ConstraintViolationException.class, MUST_NOT_BE_NULL,
            BILLING_ADDRESS_LAST_NAME_PATH);

        // act
        orderForFulfillmentMessageConsumer.receiveOrderForFulfillment(message);
    }

    @Test
    public void consume_emptyBillingAddressLastName_exceptionThrown() throws ValidationException, ProcessingException {
        // arrange
        message.getCustomerInformation().getBillingAddress().setLastName(EMPTY_STRING);

        // assert
        ExpectedExceptionUtils.expect(thrown, ConstraintViolationException.class, SIZE_DOES_NOT_MATCH,
            BILLING_ADDRESS_LAST_NAME_PATH, String.valueOf(Integer.MAX_VALUE));

        // act
        orderForFulfillmentMessageConsumer.receiveOrderForFulfillment(message);
    }

    @Test
    public void consume_blankBillingAddressLastName_exceptionThrown() throws ValidationException, ProcessingException {
        // arrange
        message.getCustomerInformation().getBillingAddress().setLastName(BLANK_STRING);

        // assert
        ExpectedExceptionUtils.expect(thrown, ConstraintViolationException.class, CONTENT_NOT_ALLOWED,
            BILLING_ADDRESS_LAST_NAME_PATH);

        // act
        orderForFulfillmentMessageConsumer.receiveOrderForFulfillment(message);
    }

    @Test
    public void consume_nullBillingAddressCity_exceptionThrown() throws ValidationException, ProcessingException {
        // arrange
        message.getCustomerInformation().getBillingAddress().setCity(null);

        // assert
        ExpectedExceptionUtils
            .expect(thrown, ConstraintViolationException.class, MUST_NOT_BE_NULL, BILLING_ADDRESS_CITY_PATH);

        // act
        orderForFulfillmentMessageConsumer.receiveOrderForFulfillment(message);
    }

    @Test
    public void consume_emptyBillingAddressCity_exceptionThrown() throws ValidationException, ProcessingException {
        // arrange
        message.getCustomerInformation().getBillingAddress().setCity(EMPTY_STRING);

        // assert
        ExpectedExceptionUtils.expect(thrown, ConstraintViolationException.class, CONTENT_NOT_ALLOWED,
            BILLING_ADDRESS_CITY_PATH);

        // act
        orderForFulfillmentMessageConsumer.receiveOrderForFulfillment(message);
    }

    @Test
    public void consume_blankBillingAddressCity_exceptionThrown() throws ValidationException, ProcessingException {
        // arrange
        message.getCustomerInformation().getBillingAddress().setCity(BLANK_STRING);

        // assert
        ExpectedExceptionUtils
            .expect(thrown, ConstraintViolationException.class, CONTENT_NOT_ALLOWED, BILLING_ADDRESS_CITY_PATH);

        // act
        orderForFulfillmentMessageConsumer.receiveOrderForFulfillment(message);
    }

    @Test
    public void consume_nullShippingInformation_exceptionThrown() throws ValidationException, ProcessingException {
        // arrange
        message.setShippingInformation(null);

        // assert
        ExpectedExceptionUtils
            .expect(thrown, ConstraintViolationException.class, MUST_NOT_BE_NULL, SHIPPING_INFORMATION_PATH);

        // act
        orderForFulfillmentMessageConsumer.receiveOrderForFulfillment(message);
    }

    @Test
    public void consume_nullShippingAddress_exceptionThrown() throws ValidationException, ProcessingException {
        // arrange
        message.getShippingInformation().setShippingAddress(null);

        // assert
        ExpectedExceptionUtils
            .expect(thrown, ConstraintViolationException.class, MUST_NOT_BE_NULL, SHIPPING_ADDRESS_PATH);

        // act
        orderForFulfillmentMessageConsumer.receiveOrderForFulfillment(message);
    }

    @Test
    public void consume_nullShippingAddressLine1_exceptionThrown() throws ValidationException, ProcessingException {
        // arrange
        message.getShippingInformation().getShippingAddress().setAddressLine1(null);

        // assert
        ExpectedExceptionUtils
            .expect(thrown, ConstraintViolationException.class, MUST_NOT_BE_NULL, SHIPPING_ADDRESS_LINE1_PATH);

        // act
        orderForFulfillmentMessageConsumer.receiveOrderForFulfillment(message);
    }

    @Test
    public void consume_emptyShippingAddressLine1_exceptionThrown() throws ValidationException, ProcessingException {
        // arrange
        message.getShippingInformation().getShippingAddress().setAddressLine1(EMPTY_STRING);

        // assert
        ExpectedExceptionUtils.expect(thrown, ConstraintViolationException.class, SIZE_DOES_NOT_MATCH,
            SHIPPING_ADDRESS_LINE1_PATH, String.valueOf(Integer.MAX_VALUE));

        // act
        orderForFulfillmentMessageConsumer.receiveOrderForFulfillment(message);
    }

    @Test
    public void consume_blankShippingAddressLine1_exceptionThrown() throws ValidationException, ProcessingException {
        // arrange
        message.getShippingInformation().getShippingAddress().setAddressLine1(BLANK_STRING);

        // assert
        ExpectedExceptionUtils
            .expect(thrown, ConstraintViolationException.class, CONTENT_NOT_ALLOWED, SHIPPING_ADDRESS_LINE1_PATH);

        // act
        orderForFulfillmentMessageConsumer.receiveOrderForFulfillment(message);
    }

    @Test
    public void consume_nullShippingAddressLastName_exceptionThrown() throws ValidationException, ProcessingException {
        // arrange
        message.getShippingInformation().getShippingAddress().setLastName(null);

        // assert
        ExpectedExceptionUtils
            .expect(thrown, ConstraintViolationException.class, MUST_NOT_BE_NULL, SHIPPING_ADDRESS_LAST_NAME_PATH);

        // act
        orderForFulfillmentMessageConsumer.receiveOrderForFulfillment(message);
    }

    @Test
    public void consume_emptyShippingAddressLastName_exceptionThrown() throws ValidationException, ProcessingException {
        // arrange
        message.getShippingInformation().getShippingAddress().setLastName(EMPTY_STRING);

        // assert
        ExpectedExceptionUtils.expect(thrown, ConstraintViolationException.class, SIZE_DOES_NOT_MATCH,
            SHIPPING_ADDRESS_LAST_NAME_PATH, String.valueOf(Integer.MAX_VALUE));

        // act
        orderForFulfillmentMessageConsumer.receiveOrderForFulfillment(message);
    }

    @Test
    public void consume_blankShippingAddressLastName_exceptionThrown() throws ValidationException, ProcessingException {
        // arrange
        message.getShippingInformation().getShippingAddress().setLastName(BLANK_STRING);

        // assert
        ExpectedExceptionUtils
            .expect(thrown, ConstraintViolationException.class, CONTENT_NOT_ALLOWED, SHIPPING_ADDRESS_LAST_NAME_PATH);

        // act
        orderForFulfillmentMessageConsumer.receiveOrderForFulfillment(message);
    }

    @Test
    public void consume_nullShippingAddressCity_exceptionThrown() throws ValidationException, ProcessingException {
        // arrange
        message.getShippingInformation().getShippingAddress().setCity(null);

        // assert
        ExpectedExceptionUtils
            .expect(thrown, ConstraintViolationException.class, MUST_NOT_BE_NULL, SHIPPING_ADDRESS_CITY_PATH);

        // act
        orderForFulfillmentMessageConsumer.receiveOrderForFulfillment(message);
    }

    @Test
    public void consume_emptyShippingAddressCity_exceptionThrown() throws ValidationException, ProcessingException {
        // arrange
        message.getShippingInformation().getShippingAddress().setCity(EMPTY_STRING);

        // assert
        ExpectedExceptionUtils.expect(thrown, ConstraintViolationException.class, CONTENT_NOT_ALLOWED,
            SHIPPING_ADDRESS_CITY_PATH);

        // act
        orderForFulfillmentMessageConsumer.receiveOrderForFulfillment(message);
    }

    @Test
    public void consume_blankShippingAddressCity_exceptionThrown() throws ValidationException, ProcessingException {
        // arrange
        message.getShippingInformation().getShippingAddress().setCity(BLANK_STRING);

        // assert
        ExpectedExceptionUtils
            .expect(thrown, ConstraintViolationException.class, CONTENT_NOT_ALLOWED, SHIPPING_ADDRESS_CITY_PATH);

        // act
        orderForFulfillmentMessageConsumer.receiveOrderForFulfillment(message);
    }
}
