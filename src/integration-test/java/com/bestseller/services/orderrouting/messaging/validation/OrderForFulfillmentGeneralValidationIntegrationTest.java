package com.bestseller.services.orderrouting.messaging.validation;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderForFulfillment.OrderForFulfillment;
import com.bestseller.services.orderrouting.messaging.consumer.OrderForFulfillmentMessageConsumer;
import com.bestseller.services.orderrouting.messaging.exceptions.ProcessingException;
import com.bestseller.services.orderrouting.utils.ExpectedExceptionUtils;
import com.bestseller.services.orderrouting.utils.generator.OrderForFulfillmentGenerator;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.validation.ConstraintViolationException;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.UUID;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.MOCK)
@ActiveProfiles("dev")
public class OrderForFulfillmentGeneralValidationIntegrationTest extends ValidationBase {

    private static final int MIN_ORDERLINE_TAX_RATE = 0;
    private static final String ORDER_ID_PATH =
            "receiveOrderForFulfillment.orderForFulfillment.orderId";
    private static final String CHANNEL_PATH =
            "receiveOrderForFulfillment.orderForFulfillment.channel";
    private static final String ORDERLINES_PATH =
            "receiveOrderForFulfillment.orderForFulfillment.orderLines";
    private static final String ORDERLINE1_EAN_PATH =
            "receiveOrderForFulfillment.orderForFulfillment.orderLines[0].ean";
    private static final String ORDERLINE1_LINENUMBER_PATH =
            "receiveOrderForFulfillment.orderForFulfillment.orderLines[0].lineNumber";
    private static final String ORDERLINE1_QUANTITY_PATH =
            "receiveOrderForFulfillment.orderForFulfillment.orderLines[0].quantity";
    private static final String ORDERLINE1_TAXRATE_PATH =
            "receiveOrderForFulfillment.orderForFulfillment.orderLines[0].taxPercentage";
    private static final String MESSAGE_ROOT_PATH =
            "receiveOrderForFulfillment.orderForFulfillment";

    @Rule
    public ExpectedException thrown = ExpectedException.none();

    @Autowired
    private OrderForFulfillmentMessageConsumer orderForFulfillmentMessageConsumer;

    private OrderForFulfillment message;
    private OrderLine orderLine1;

    @Before
    public void setUp() {
        message = OrderForFulfillmentGenerator.createFullOrderForFulfillmentMessage();
        orderLine1 = message.getOrderLines().iterator().next();
    }

    @Test
    public void consume_nullMessage_exceptionThrown() throws ValidationException, ProcessingException {
        // arrange

        // assert
        ExpectedExceptionUtils.expect(thrown, ConstraintViolationException.class, MUST_NOT_BE_NULL,
                                      MESSAGE_ROOT_PATH);

        // act
        orderForFulfillmentMessageConsumer.receiveOrderForFulfillment(null);
    }

    @Test
    public void consume_nullOrderId_exceptionThrown() throws ValidationException, ProcessingException {
        // arrange
        message.setOrderId(null);

        // assert
        ExpectedExceptionUtils.expect(thrown, ConstraintViolationException.class, MUST_NOT_BE_NULL,
                                      ORDER_ID_PATH);

        // act
        orderForFulfillmentMessageConsumer.receiveOrderForFulfillment(message);
    }

    @Test
    public void consume_emptyOrderId_exceptionThrown() throws ValidationException, ProcessingException {
        // arrange
        message.setOrderId(EMPTY_STRING);

        // assert
        ExpectedExceptionUtils.expect(thrown, ConstraintViolationException.class, SIZE_DOES_NOT_MATCH,
                                      ORDER_ID_PATH, String.valueOf(Integer.MAX_VALUE));

        // act
        orderForFulfillmentMessageConsumer.receiveOrderForFulfillment(message);
    }

    @Test
    public void consume_blankOrderId_exceptionThrown() throws ValidationException, ProcessingException {
        // arrange
        message.setOrderId(BLANK_STRING);

        // assert
        ExpectedExceptionUtils.expect(thrown, ConstraintViolationException.class, CONTENT_NOT_ALLOWED,
                                      ORDER_ID_PATH, String.valueOf(Integer.MAX_VALUE));

        // act
        orderForFulfillmentMessageConsumer.receiveOrderForFulfillment(message);
    }

    @Test
    public void consume_nullChannel_exceptionThrown() throws ValidationException, ProcessingException {
        // arrange
        message.setChannel(null);

        // assert
        ExpectedExceptionUtils.expect(thrown, ConstraintViolationException.class, MUST_NOT_BE_NULL, CHANNEL_PATH);

        // act
        orderForFulfillmentMessageConsumer.receiveOrderForFulfillment(message);
    }

    @Test
    public void consume_emptyChannel_exceptionThrown() throws ValidationException, ProcessingException {
        // arrange
        message.setChannel(EMPTY_STRING);

        // assert
        ExpectedExceptionUtils.expect(thrown, ConstraintViolationException.class, SIZE_DOES_NOT_MATCH,
                                      CHANNEL_PATH, String.valueOf(Integer.MAX_VALUE));

        // act
        orderForFulfillmentMessageConsumer.receiveOrderForFulfillment(message);
    }

    @Test
    public void consume_blankChannel_exceptionThrown() throws ValidationException, ProcessingException {
        // arrange
        message.setChannel(BLANK_STRING);

        // assert
        ExpectedExceptionUtils.expect(thrown, ConstraintViolationException.class, CONTENT_NOT_ALLOWED, CHANNEL_PATH);

        // act
        orderForFulfillmentMessageConsumer.receiveOrderForFulfillment(message);
    }

    @Test
    public void consume_nullOrderLines_exceptionThrown() throws ValidationException, ProcessingException {
        // arrange
        message.setOrderLines(null);

        // assert
        ExpectedExceptionUtils.expect(thrown, ConstraintViolationException.class, MUST_NOT_BE_NULL, ORDERLINES_PATH);

        // act
        orderForFulfillmentMessageConsumer.receiveOrderForFulfillment(message);
    }

    @Test
    public void consume_emptyOrderLines_exceptionThrown() throws ValidationException, ProcessingException {
        // arrange
        message.setOrderLines(Collections.emptyList());

        // assert
        ExpectedExceptionUtils.expect(thrown, ConstraintViolationException.class, SIZE_DOES_NOT_MATCH,
                                      ORDERLINES_PATH, String.valueOf(Integer.MAX_VALUE));

        // act
        orderForFulfillmentMessageConsumer.receiveOrderForFulfillment(message);
    }

    @Test
    public void consume_nullOrderLineEan_exceptionThrown() throws ValidationException, ProcessingException {
        // arrange
        orderLine1.setEan(null);

        // assert
        ExpectedExceptionUtils.expect(thrown, ConstraintViolationException.class, MUST_NOT_BE_NULL, ORDERLINE1_EAN_PATH);

        // act
        orderForFulfillmentMessageConsumer.receiveOrderForFulfillment(message);
    }

    @Test
    public void consume_emptyOrderLineEan_exceptionThrown() throws ValidationException, ProcessingException {
        // arrange
        orderLine1.setEan(EMPTY_STRING);

        // assert
        ExpectedExceptionUtils.expect(thrown, ConstraintViolationException.class, SIZE_DOES_NOT_MATCH,
                                      ORDERLINE1_EAN_PATH, String.valueOf(Integer.MAX_VALUE));

        // act
        orderForFulfillmentMessageConsumer.receiveOrderForFulfillment(message);
    }

    @Test
    public void consume_blankOrderLineEan_exceptionThrown() throws ValidationException, ProcessingException {
        // arrange
        orderLine1.setEan(BLANK_STRING);

        // assert
        ExpectedExceptionUtils.expect(thrown, ConstraintViolationException.class, CONTENT_NOT_ALLOWED,
                                      ORDERLINE1_EAN_PATH, String.valueOf(Integer.MAX_VALUE));

        // act
        orderForFulfillmentMessageConsumer.receiveOrderForFulfillment(message);
    }

    @Test
    public void consume_nullOrderLineLineNumber_exceptionThrown() throws ValidationException, ProcessingException {
        // arrange
        orderLine1.setLineNumber(null);

        // assert
        ExpectedExceptionUtils
                .expect(thrown, ConstraintViolationException.class, MUST_NOT_BE_NULL, ORDERLINE1_LINENUMBER_PATH);

        // act
        orderForFulfillmentMessageConsumer.receiveOrderForFulfillment(message);
    }

    @Test
    public void consume_zeroOrderLineLineNumber_exceptionThrown() throws ValidationException, ProcessingException {
        // arrange
        orderLine1.setLineNumber(0);

        // assert
        ExpectedExceptionUtils.expect(thrown, ConstraintViolationException.class, MUST_BE_GREATER_OR_EQUAL_TO,
                                      ORDERLINE1_LINENUMBER_PATH, MIN_ORDERLINE_LINENUMBER);

        // act
        orderForFulfillmentMessageConsumer.receiveOrderForFulfillment(message);
    }

    @Test
    public void consume_negativeOrderLineLineNumber_exceptionThrown() throws ValidationException, ProcessingException {
        // arrange
        orderLine1.setLineNumber(-1);

        // assert
        ExpectedExceptionUtils.expect(thrown, ConstraintViolationException.class, MUST_BE_GREATER_OR_EQUAL_TO,
                                      ORDERLINE1_LINENUMBER_PATH, MIN_ORDERLINE_LINENUMBER);

        // act
        orderForFulfillmentMessageConsumer.receiveOrderForFulfillment(message);
    }

    @Test
    public void consume_nullOrderLineQuantity_exceptionThrown() throws ValidationException, ProcessingException {
        // arrange
        orderLine1.setQuantity(null);

        // assert
        ExpectedExceptionUtils
                .expect(thrown, ConstraintViolationException.class, MUST_NOT_BE_NULL, ORDERLINE1_QUANTITY_PATH);

        // act
        orderForFulfillmentMessageConsumer.receiveOrderForFulfillment(message);
    }

    @Test
    public void consume_zeroOrderLineQuantity_exceptionThrown() throws ValidationException, ProcessingException {
        // arrange
        orderLine1.setQuantity(0);

        // assert
        ExpectedExceptionUtils.expect(thrown, ConstraintViolationException.class, MUST_BE_GREATER_OR_EQUAL_TO,
                                      ORDERLINE1_QUANTITY_PATH, MIN_ORDERLINE_QUANTITY);

        // act
        orderForFulfillmentMessageConsumer.receiveOrderForFulfillment(message);
    }

    @Test
    public void consume_negativeOrderLineQuantity_exceptionThrown() throws ValidationException, ProcessingException {
        // arrange
        orderLine1.setQuantity(-1);

        // assert
        ExpectedExceptionUtils.expect(thrown, ConstraintViolationException.class, MUST_BE_GREATER_OR_EQUAL_TO,
                                      ORDERLINE1_QUANTITY_PATH, MIN_ORDERLINE_QUANTITY);

        // act
        orderForFulfillmentMessageConsumer.receiveOrderForFulfillment(message);
    }

    @Test
    public void consume_nullOrderLineTaxRate_exceptionThrown() throws ValidationException, ProcessingException {
        // arrange
        orderLine1.setTaxPercentage(null);

        // assert
        ExpectedExceptionUtils
                .expect(thrown, ConstraintViolationException.class, MUST_NOT_BE_NULL, ORDERLINE1_TAXRATE_PATH);

        // act
        orderForFulfillmentMessageConsumer.receiveOrderForFulfillment(message);
    }

    @Test
    public void consume_negativeOrderLineTaxRate_exceptionThrown() throws ValidationException, ProcessingException {
        // arrange
        orderLine1.setTaxPercentage(BigDecimal.valueOf(-1));

        // assert
        ExpectedExceptionUtils.expect(thrown, ConstraintViolationException.class, MUST_BE_GREATER_OR_EQUAL_TO,
                                      ORDERLINE1_TAXRATE_PATH, MIN_ORDERLINE_TAX_RATE);

        // act
        orderForFulfillmentMessageConsumer.receiveOrderForFulfillment(message);
    }

    @Test
    public void consume_bareMinimumMandatoryData_noExceptionThrown() throws ValidationException, ProcessingException {
        // arrange
        message = OrderForFulfillmentGenerator.createMinValidOrderForFulfillmentMessage();

        // since this test will insert an actual order in the cache, it should be with random ID or the
        // second time it is executed will fail with duplicate order
        message.setOrderId(UUID.randomUUID().toString());

        // act
        orderForFulfillmentMessageConsumer.receiveOrderForFulfillment(message);

        // assert - no exceptions thrown
    }
}
