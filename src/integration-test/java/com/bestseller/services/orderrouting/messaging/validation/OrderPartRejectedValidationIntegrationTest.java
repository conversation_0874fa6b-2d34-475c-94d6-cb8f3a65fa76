package com.bestseller.services.orderrouting.messaging.validation;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartRejected.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartRejected.OrderPartRejected;
import com.bestseller.services.orderrouting.messaging.consumer.OrderPartRejectedMessageConsumer;
import com.bestseller.services.orderrouting.utils.ExpectedExceptionUtils;
import com.bestseller.services.orderrouting.utils.generator.OrderPartRejectedGenerator;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.validation.ConstraintViolationException;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.MOCK)
@ActiveProfiles("dev")
public class OrderPartRejectedValidationIntegrationTest extends ValidationBase {

    private static final String MESSAGE_ROOT_PATH =
            "receiveOrderPartRejected.orderPartRejected";
    private static final String ORDER_ID_PATH =
            "receiveOrderPartRejected.orderPartRejected.orderId";
    private static final String ORDERLINE1_EAN_PATH =
            "receiveOrderPartRejected.orderPartRejected.orderLines[0].ean";
    private static final String ORDERLINE1_LINENUMBER_PATH =
            "receiveOrderPartRejected.orderPartRejected.orderLines[0].lineNumber";
    private static final String ORDERLINE1_QUANTITY_PATH =
            "receiveOrderPartRejected.orderPartRejected.orderLines[0].quantity";

    @Autowired
    private OrderPartRejectedMessageConsumer orderPartRejectedMessageConsumer;

    @Rule
    public ExpectedException thrown = ExpectedException.none();

    private OrderPartRejected orderPartRejected;
    private OrderLine orderLine1;

    @Before
    public void setUp() {
        orderPartRejected = OrderPartRejectedGenerator.createFullOrderPartRejectedMessage();
        orderLine1 = orderPartRejected.getOrderLines().iterator().next();
    }

    @Test
    public void consume_nullMessage_exceptionThrown() throws ValidationException {
        // arrange

        // assert
        ExpectedExceptionUtils.expect(thrown, ConstraintViolationException.class, MUST_NOT_BE_NULL,
                                      MESSAGE_ROOT_PATH);

        // act
        orderPartRejectedMessageConsumer.receiveOrderPartRejected(null);
    }

    @Test
    public void consume_nullOrderId_exceptionThrown() throws ValidationException {
        // arrange
        orderPartRejected.setOrderId(null);

        // assert
        ExpectedExceptionUtils.expect(thrown, ConstraintViolationException.class, MUST_NOT_BE_NULL,
                                      ORDER_ID_PATH);

        // act
        orderPartRejectedMessageConsumer.receiveOrderPartRejected(orderPartRejected);
    }

    @Test
    public void consume_emptyOrderId_exceptionThrown() throws ValidationException {
        // arrange
        orderPartRejected.setOrderId(EMPTY_STRING);

        // assert
        ExpectedExceptionUtils.expect(thrown, ConstraintViolationException.class, SIZE_DOES_NOT_MATCH,
                                      ORDER_ID_PATH, String.valueOf(Integer.MAX_VALUE));

        // act
        orderPartRejectedMessageConsumer.receiveOrderPartRejected(orderPartRejected);
    }

    @Test
    public void consume_blankOrderId_exceptionThrown() throws ValidationException {
        // arrange
        orderPartRejected.setOrderId(BLANK_STRING);

        // assert
        ExpectedExceptionUtils.expect(thrown, ConstraintViolationException.class, CONTENT_NOT_ALLOWED,
                                      ORDER_ID_PATH, String.valueOf(Integer.MAX_VALUE));

        // act
        orderPartRejectedMessageConsumer.receiveOrderPartRejected(orderPartRejected);
    }

    @Test
    public void consume_nullOrderLineEan_exceptionThrown() throws ValidationException {
        // arrange
        orderLine1.setEan(null);

        // assert
        ExpectedExceptionUtils.expect(thrown, ConstraintViolationException.class, MUST_NOT_BE_NULL, ORDERLINE1_EAN_PATH);

        // act
        orderPartRejectedMessageConsumer.receiveOrderPartRejected(orderPartRejected);
    }

    @Test
    public void consume_emptyOrderLineEan_exceptionThrown() throws ValidationException {
        // arrange
        orderLine1.setEan(EMPTY_STRING);

        // assert
        ExpectedExceptionUtils.expect(thrown, ConstraintViolationException.class, SIZE_DOES_NOT_MATCH,
                                      ORDERLINE1_EAN_PATH, String.valueOf(Integer.MAX_VALUE));

        // act
        orderPartRejectedMessageConsumer.receiveOrderPartRejected(orderPartRejected);
    }

    @Test
    public void consume_blankOrderLineEan_exceptionThrown() throws ValidationException {
        // arrange
        orderLine1.setEan(BLANK_STRING);

        // assert
        ExpectedExceptionUtils.expect(thrown, ConstraintViolationException.class, CONTENT_NOT_ALLOWED,
                                      ORDERLINE1_EAN_PATH, String.valueOf(Integer.MAX_VALUE));

        // act
        orderPartRejectedMessageConsumer.receiveOrderPartRejected(orderPartRejected);
    }

    @Test
    public void consume_nullOrderLineLineNumber_exceptionThrown() throws ValidationException {
        // arrange
        orderLine1.setLineNumber(null);

        // assert
        ExpectedExceptionUtils
                .expect(thrown, ConstraintViolationException.class, MUST_NOT_BE_NULL, ORDERLINE1_LINENUMBER_PATH);

        // act
        orderPartRejectedMessageConsumer.receiveOrderPartRejected(orderPartRejected);
    }

    @Test
    public void consume_zeroOrderLineLineNumber_exceptionThrown() throws ValidationException {
        // arrange
        orderLine1.setLineNumber(0);

        // assert
        ExpectedExceptionUtils.expect(thrown, ConstraintViolationException.class, MUST_BE_GREATER_OR_EQUAL_TO,
                                      ORDERLINE1_LINENUMBER_PATH, MIN_ORDERLINE_LINENUMBER);

        // act
        orderPartRejectedMessageConsumer.receiveOrderPartRejected(orderPartRejected);
    }

    @Test
    public void consume_negativeOrderLineLineNumber_exceptionThrown() throws ValidationException {
        // arrange
        orderLine1.setLineNumber(-1);

        // assert
        ExpectedExceptionUtils.expect(thrown, ConstraintViolationException.class, MUST_BE_GREATER_OR_EQUAL_TO,
                                      ORDERLINE1_LINENUMBER_PATH, MIN_ORDERLINE_LINENUMBER);

        // act
        orderPartRejectedMessageConsumer.receiveOrderPartRejected(orderPartRejected);
    }

    @Test
    public void consume_nullOrderLineQuantity_exceptionThrown() throws ValidationException {
        // arrange
        orderLine1.setQuantity(null);

        // assert
        ExpectedExceptionUtils
                .expect(thrown, ConstraintViolationException.class, MUST_NOT_BE_NULL, ORDERLINE1_QUANTITY_PATH);

        // act
        orderPartRejectedMessageConsumer.receiveOrderPartRejected(orderPartRejected);
    }

    @Test
    public void consume_zeroOrderLineQuantity_exceptionThrown() throws ValidationException {
        // arrange
        orderLine1.setQuantity(0);

        // assert
        ExpectedExceptionUtils.expect(thrown, ConstraintViolationException.class, MUST_BE_GREATER_OR_EQUAL_TO,
                                      ORDERLINE1_QUANTITY_PATH, MIN_ORDERLINE_QUANTITY);

        // act
        orderPartRejectedMessageConsumer.receiveOrderPartRejected(orderPartRejected);
    }

    @Test
    public void consume_negativeOrderLineQuantity_exceptionThrown() throws ValidationException {
        // arrange
        orderLine1.setQuantity(-1);

        // assert
        ExpectedExceptionUtils.expect(thrown, ConstraintViolationException.class, MUST_BE_GREATER_OR_EQUAL_TO,
                                      ORDERLINE1_QUANTITY_PATH, MIN_ORDERLINE_QUANTITY);

        // act
        orderPartRejectedMessageConsumer.receiveOrderPartRejected(orderPartRejected);
    }
}
