package com.bestseller.services.orderrouting.messaging.validation;

public abstract class ValidationBase {
    protected static final String MUST_NOT_BE_NULL = "{0}: must not be null";
    protected static final String SIZE_DOES_NOT_MATCH = "{0}: size must be between 1 and {1}";
    protected static final String CONTENT_NOT_ALLOWED = "{0}: must match \"^(?!\\s*$).+\"";
    protected static final String COUNTRY_NOT_ALLOWED = "{0}: must match \"^[A-Z]{2}$\"";
    protected static final String MUST_BE_GREATER_OR_EQUAL_TO = "{0}: must be greater than or equal to {1}";
    protected static final String EMPTY_STRING = "";
    protected static final String BLANK_STRING = " ";
    protected static final int MIN_ORDERLINE_QUANTITY = 1;
    protected static final int MIN_ORDERLINE_LINENUMBER = 1;


}
