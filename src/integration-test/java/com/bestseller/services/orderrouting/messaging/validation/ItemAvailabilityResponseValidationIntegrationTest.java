package com.bestseller.services.orderrouting.messaging.validation;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailability;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailabilityResponse;
import com.bestseller.services.orderrouting.messaging.consumer.ItemAvailabilityResponseConsumer;
import com.bestseller.services.orderrouting.utils.ExpectedExceptionUtils;
import com.bestseller.services.orderrouting.utils.generator.ItemAvailabilityGenerator;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.validation.ConstraintViolationException;
import java.util.Collections;
import java.util.UUID;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.MOCK)
@ActiveProfiles("dev")
public class ItemAvailabilityResponseValidationIntegrationTest extends ValidationBase {

    private static final String ARMING_COIR_WAREHOUSE = "ARMING_COIR_WAREHOUSE";
    private static final String NETHERLANDS = "NL";
    private static final String EAN = "*************";

    private static final String MESSAGE_ROOT_PATH =
            "receiveItemAvailabilityResponse.itemAvailability";
    private static final String CORRELATION_ID_PATH =
            "receiveItemAvailabilityResponse.itemAvailability.correlationId";
    private static final String ITEMS_PATH =
            "receiveItemAvailabilityResponse.itemAvailability.items";
    private static final String ITEMS_EAN_PATH =
            "receiveItemAvailabilityResponse.itemAvailability.items[0].ean";
    private static final String ITEMS_ITEM_AVAILABILITY_PATH =
            "receiveItemAvailabilityResponse.itemAvailability.items[0].itemAvailability";
    private static final String ITEMS_ITEM_AVAILABILITY_AVAILABLE_QUANTITY_PATH =
            "receiveItemAvailabilityResponse.itemAvailability.items[0].itemAvailability[0].availableQuantity";
    private static final String ITEMS_ITEM_AVAILABILITY_COUNTRY_PATH =
            "receiveItemAvailabilityResponse.itemAvailability.items[0].itemAvailability[0].country";
    private static final String ITEMS_ITEM_AVAILABILITY_TYPE_PATH =
            "receiveItemAvailabilityResponse.itemAvailability.items[0].itemAvailability[0].type";
    private static final String ITEMS_ITEM_AVAILABILITY_WAREHOUSE_PATH =
            "receiveItemAvailabilityResponse.itemAvailability.items[0].itemAvailability[0].warehouse";

    @Autowired
    private ItemAvailabilityResponseConsumer itemAvailabilityResponseConsumer;

    @Rule
    public ExpectedException thrown = ExpectedException.none();

    private ItemAvailabilityResponse itemAvailabilityResponse;

    @Before
    public void setUp() {
        ItemAvailability itemAvailability =
                ItemAvailabilityGenerator.createItemAvailability(ARMING_COIR_WAREHOUSE,
                                                                 10,
                                                                 ItemAvailability.Type.WAREHOUSE,
                                                                 NETHERLANDS);
        itemAvailabilityResponse =
                new ItemAvailabilityResponse()
                        .withItems(Collections.singletonList(
                                ItemAvailabilityGenerator.createItem(EAN, itemAvailability)))
                        .withCorrelationId(UUID.randomUUID().toString());

    }


    @Test
    public void consume_nullMessage_exceptionThrown() throws ValidationException {
        // arrange

        // assert
        ExpectedExceptionUtils.expect(thrown, ConstraintViolationException.class, MUST_NOT_BE_NULL,
                                      MESSAGE_ROOT_PATH);

        // act
        itemAvailabilityResponseConsumer.receiveItemAvailabilityResponse(null);
    }

    @Test
    public void consume_nullCorrelationId_exceptionThrown() throws ValidationException {
        // arrange
        itemAvailabilityResponse.setCorrelationId(null);

        // assert
        ExpectedExceptionUtils.expect(thrown, ConstraintViolationException.class, MUST_NOT_BE_NULL,
                                      CORRELATION_ID_PATH, String.valueOf(Integer.MAX_VALUE));

        // act
        itemAvailabilityResponseConsumer.receiveItemAvailabilityResponse(itemAvailabilityResponse);
    }

    @Test
    public void consume_nullOrderLines_exceptionThrown() throws ValidationException {
        // arrange
        itemAvailabilityResponse.setItems(null);

        // assert
        ExpectedExceptionUtils.expect(thrown, ConstraintViolationException.class, MUST_NOT_BE_NULL, ITEMS_PATH);

        // act
        itemAvailabilityResponseConsumer.receiveItemAvailabilityResponse(itemAvailabilityResponse);
    }

    @Test
    public void consume_nullItemsEan_exceptionThrown() throws ValidationException {
        // arrange
        itemAvailabilityResponse.getItems().get(0).setEan(null);

        // assert
        ExpectedExceptionUtils.expect(thrown, ConstraintViolationException.class, MUST_NOT_BE_NULL, ITEMS_EAN_PATH);

        // act
        itemAvailabilityResponseConsumer.receiveItemAvailabilityResponse(itemAvailabilityResponse);
    }

    @Test
    public void consume_nullItemsItemAvailability_exceptionThrown() throws ValidationException {
        // arrange
        itemAvailabilityResponse.getItems().get(0).setItemAvailability(null);

        // assert
        ExpectedExceptionUtils.expect(thrown, ConstraintViolationException.class, MUST_NOT_BE_NULL, ITEMS_ITEM_AVAILABILITY_PATH);

        // act
        itemAvailabilityResponseConsumer.receiveItemAvailabilityResponse(itemAvailabilityResponse);
    }


    @Test
    public void consume_nullItemsItemAvailabilityAvailableQuantity_exceptionThrown() throws ValidationException {
        // arrange
        itemAvailabilityResponse.getItems().get(0).getItemAvailability().get(0).setAvailableQuantity(null);

        // assert
        ExpectedExceptionUtils.expect(thrown, ConstraintViolationException.class, MUST_NOT_BE_NULL, ITEMS_ITEM_AVAILABILITY_AVAILABLE_QUANTITY_PATH);

        // act
        itemAvailabilityResponseConsumer.receiveItemAvailabilityResponse(itemAvailabilityResponse);
    }

    @Test
    public void consume_nullItemsItemAvailabilityCountry_exceptionThrown() throws ValidationException {
        // arrange
        itemAvailabilityResponse.getItems().get(0).getItemAvailability().get(0).setCountry(null);

        // assert
        ExpectedExceptionUtils.expect(thrown, ConstraintViolationException.class, MUST_NOT_BE_NULL,
                                      ITEMS_ITEM_AVAILABILITY_COUNTRY_PATH);

        // act
        itemAvailabilityResponseConsumer.receiveItemAvailabilityResponse(itemAvailabilityResponse);
    }

    @Test
    public void consume_invalidItemsItemAvailabilityCountry_exceptionThrown() throws ValidationException {
        // arrange
        itemAvailabilityResponse.getItems().get(0).getItemAvailability().get(0).setCountry("INVALID_COUNTRY");

        // assert
        ExpectedExceptionUtils.expect(thrown, ConstraintViolationException.class, COUNTRY_NOT_ALLOWED, ITEMS_ITEM_AVAILABILITY_COUNTRY_PATH);

        // act
        itemAvailabilityResponseConsumer.receiveItemAvailabilityResponse(itemAvailabilityResponse);
    }

    @Test
    public void consume_nullItemsItemAvailabilityType_exceptionThrown() throws ValidationException {
        // arrange
        itemAvailabilityResponse.getItems().get(0).getItemAvailability().get(0).setType(null);

        // assert
        ExpectedExceptionUtils.expect(thrown, ConstraintViolationException.class, MUST_NOT_BE_NULL,
                                      ITEMS_ITEM_AVAILABILITY_TYPE_PATH);

        // act
        itemAvailabilityResponseConsumer.receiveItemAvailabilityResponse(itemAvailabilityResponse);
    }

    @Test
    public void consume_nullItemsItemAvailabilityWarehouse_exceptionThrown() throws ValidationException {
        // arrange
        itemAvailabilityResponse.getItems().get(0).getItemAvailability().get(0).setWarehouse(null);

        // assert
        ExpectedExceptionUtils.expect(thrown, ConstraintViolationException.class, MUST_NOT_BE_NULL,
                                      ITEMS_ITEM_AVAILABILITY_WAREHOUSE_PATH);

        // act
        itemAvailabilityResponseConsumer.receiveItemAvailabilityResponse(itemAvailabilityResponse);
    }

}
