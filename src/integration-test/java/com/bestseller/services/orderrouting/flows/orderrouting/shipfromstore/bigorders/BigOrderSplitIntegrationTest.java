package com.bestseller.services.orderrouting.flows.orderrouting.shipfromstore.bigorders;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.Item;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailabilityResponse;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsCreated.OrderPartsCreated;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsRouted.OrderPartsRouted;
import com.bestseller.services.orderrouting.constants.CountryConstants;
import com.bestseller.services.orderrouting.flows.orderrouting.RoutingBase;
import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.utils.generator.OrderModelGenerator;
import org.hamcrest.Matchers;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.bestseller.services.orderrouting.constants.FulfillmentNodeConstants.FULFILLMENT_CENTER_WEST;
import static com.bestseller.services.orderrouting.constants.FulfillmentNodeConstants.FULFILLMENT_CENTER_EAST;
import static com.bestseller.services.orderrouting.constants.FulfillmentNodeConstants.SHIP_FROM_STORE_NL;
import static com.bestseller.services.orderrouting.utils.EanUtils.eans;
import static com.bestseller.services.orderrouting.utils.generator.ItemAvailabilityGenerator.FULL_AVAILABILITY;
import static com.bestseller.services.orderrouting.utils.generator.ItemAvailabilityGenerator.ZERO_AVAILABILITY;
import static com.bestseller.services.orderrouting.utils.generator.ItemAvailabilityGenerator.createItem;
import static com.bestseller.services.orderrouting.utils.generator.ItemAvailabilityGenerator.createItemAvailability;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.hasSize;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(
    webEnvironment = SpringBootTest.WebEnvironment.MOCK,
    properties = "KAFKA_TOPIC_PREFIX=BigOrderSplitIntegrationTest")
public class BigOrderSplitIntegrationTest extends RoutingBase {

    @Before
    public void setUp() {
        orderModel = OrderModelGenerator.createTestOrderModel();

        orderModel.setOrderLineQuantities(OrderModelGenerator.createNOrderLines(orderModel.getOrderId(), 14));
        orderModel.getOrderLineQuantities().forEach(orderLine -> orderLine.setBrand("jack-jones"));
        orderModel.getShippingAddress().setCountry(CountryConstants.NETHERLANDS);

        orderModel = orderService.saveOrderModel(orderModel);
    }

    /**
     * Scenario: A big order with more 14 items has availability FCE.
     *
     * <h1>Given</h1>
     * <ol>
     *   <li>An order with 14 order lines</li>
     *   <li>Order goes to NL</li>
     *   <li>All availability in FCE, some of items also have availability in SFS NL</li>
     * </ol>
     *
     * <h1>When</h1>
     * <ol>
     *   <li>ItemAvailabilityResponse is received</li>
     * </ol>
     *
     * <h1>Then</h1>
     * <ol>
     *   <li>OrderPartsRouted is created</li>
     *   <li>All order lines are routed to FCE</li>
     *   <li>All order lines are present per order part</li>
     * </ol>
     */
    @Test
    public void executeOrderRoutingRules_allAvailabilityInFce_orderRoutedToFce() {
        // arrange
        List<OrderLineQuantityModel> expectedOrderLinesQuantitiesInPart = orderModel.getOrderLineQuantities();

        List<Item> items = generateAvailabilityItems(expectedOrderLinesQuantitiesInPart, ean -> createItem(ean,
                createItemAvailability(FULFILLMENT_CENTER_EAST, FULL_AVAILABILITY),
                createItemAvailability(FULFILLMENT_CENTER_WEST, ZERO_AVAILABILITY),
                createItemAvailability(SHIP_FROM_STORE_NL, ZERO_AVAILABILITY)));

        itemAvailabilityResponse = new ItemAvailabilityResponse()
                .withCorrelationId(orderModel.getOrderId())
                .withItems(items);

        // set the first three order lines to have availability in SHIP_FROM_STORE_NL
        itemAvailabilityResponse.getItems().get(0).getItemAvailability().get(2).setAvailableQuantity(FULL_AVAILABILITY);
        itemAvailabilityResponse.getItems().get(1).getItemAvailability().get(2).setAvailableQuantity(FULL_AVAILABILITY);
        itemAvailabilityResponse.getItems().get(2).getItemAvailability().get(2).setAvailableQuantity(FULL_AVAILABILITY);

        facts = getOrderRoutingRulesFacts(itemAvailabilityResponse, orderModel);

        // act
        rulesService.executeOrderRoutingRules(facts);

        // assert
        verify(orderPartsRoutedProducer).produce(orderPartsRoutedArgumentCaptor.capture());
        verify(orderPartsCreatedProducer).produce(orderPartsCreatedArgumentCaptor.capture());

        assertOrderPart(FULFILLMENT_CENTER_EAST, expectedOrderLinesQuantitiesInPart);
    }

    /**
     * Scenario: A big order with more 14 items has both availability in NL and FCE
     *
     * <h1>Given</h1>
     * <ol>
     *   <li>An order with 14 order lines</li>
     *   <li>Order goes to NL</li>
     *   <li>Availability is spread: some availability in FCE, some of items have availability in SFS NL</li>
     * </ol>
     *
     * <h1>When</h1>
     * <ol>
     *   <li>ItemAvailabilityResponse is received</li>
     * </ol>
     *
     * <h1>Then</h1>
     * <ol>
     *   <li>OrderPartsRouted is created</li>
     *   <li>All order lines are routed to FCE</li>
     *   <li>All order lines are present per order part</li>
     * </ol>
     */
    @Test
    public void executeOrderRoutingRules_availabilitySpreadBetweenWarehouses_orderSplit() {
        // arrange
        List<OrderLineQuantityModel> expectedOrderLinesQuantitiesInShipFromStore = orderModel.getOrderLineQuantities().subList(0, 3);
        List<OrderLineQuantityModel> expectedOrderLinesQuantitiesInEast = orderModel.getOrderLineQuantities().subList(3, 14);

        List<Item> availability = generateAvailabilityItems(expectedOrderLinesQuantitiesInEast, ean -> createItem(ean,
                createItemAvailability(FULFILLMENT_CENTER_EAST, FULL_AVAILABILITY),
                createItemAvailability(FULFILLMENT_CENTER_WEST, ZERO_AVAILABILITY),
                createItemAvailability(SHIP_FROM_STORE_NL, ZERO_AVAILABILITY)));

        // three items are only in the SHIP_FROM_STORE_NL
        List<Item> shipFromStoreAvailability = generateAvailabilityItems(expectedOrderLinesQuantitiesInShipFromStore, ean -> createItem(ean,
                createItemAvailability(FULFILLMENT_CENTER_EAST, ZERO_AVAILABILITY),
                createItemAvailability(FULFILLMENT_CENTER_WEST, ZERO_AVAILABILITY),
                createItemAvailability(SHIP_FROM_STORE_NL, FULL_AVAILABILITY)));

        availability.addAll(shipFromStoreAvailability);

        itemAvailabilityResponse = new ItemAvailabilityResponse()
                .withCorrelationId(orderModel.getOrderId())
                .withItems(availability);

        facts = getOrderRoutingRulesFacts(itemAvailabilityResponse, orderModel);

        // act
        rulesService.executeOrderRoutingRules(facts);

        // assert
        verify(orderPartsRoutedProducer, times(TWO_ORDER_PARTS)).produce(orderPartsRoutedArgumentCaptor.capture());
        verify(orderPartsCreatedProducer, times(TWO_ORDER_PARTS)).produce(orderPartsCreatedArgumentCaptor.capture());

        assertOrderPart(FULFILLMENT_CENTER_EAST, expectedOrderLinesQuantitiesInEast);
        assertOrderPart(SHIP_FROM_STORE_NL, expectedOrderLinesQuantitiesInShipFromStore);
    }


    /**
     * Scenario: A big order with more 16 items has both availability in NL and FCE
     *
     * <h1>Given</h1>
     * <ol>
     *   <li>An order with 16 order lines</li>
     *   <li>Order goes to NL</li>
     *   <li>Availability is spread: order has 9 items in FCE, 7 in SFS</li>
     * </ol>
     *
     * <h1>When</h1>
     * <ol>
     *   <li>ItemAvailabilityResponse is received</li>
     * </ol>
     *
     * <h1>Then</h1>
     * <ol>
     *   <li>OrderPartsRouted is created</li>
     *   <li>One part routed to FCE and two parts to SHIP_FROM_STORE_NL</li>
     *   <li>All order lines are present per order part</li>
     * </ol>
     */
    @Test
    public void executeOrderRoutingRules_availabilitySpreadBetweenWarehouses_orderSplitAndIntoTwoParts() {
        // arrange
        orderModel.setOrderLineQuantities(OrderModelGenerator.createNOrderLines(orderModel.getOrderId(), 16));
        orderModel.getOrderLineQuantities().forEach(orderLine -> orderLine.setBrand("jack-jones"));

        orderService.saveOrderModel(orderModel);

        List<OrderLineQuantityModel> expectedOrderLinesQuantitiesInShipFromStore = orderModel.getOrderLineQuantities().subList(0, 7);
        List<OrderLineQuantityModel> expectedOrderLinesQuantitiesInEast = orderModel.getOrderLineQuantities().subList(7, 16);

        List<Item> availability = generateAvailabilityItems(expectedOrderLinesQuantitiesInEast, ean -> createItem(ean,
                createItemAvailability(FULFILLMENT_CENTER_EAST, FULL_AVAILABILITY),
                createItemAvailability(FULFILLMENT_CENTER_WEST, ZERO_AVAILABILITY),
                createItemAvailability(SHIP_FROM_STORE_NL, ZERO_AVAILABILITY)));

        // seven items are only in the SHIP_FROM_STORE_NL
        List<Item> shipFromStoreAvailability = generateAvailabilityItems(expectedOrderLinesQuantitiesInShipFromStore, ean -> createItem(ean,
                createItemAvailability(FULFILLMENT_CENTER_EAST, ZERO_AVAILABILITY),
                createItemAvailability(FULFILLMENT_CENTER_WEST, ZERO_AVAILABILITY),
                createItemAvailability(SHIP_FROM_STORE_NL, FULL_AVAILABILITY)));

        availability.addAll(shipFromStoreAvailability);

        itemAvailabilityResponse = new ItemAvailabilityResponse()
                .withCorrelationId(orderModel.getOrderId())
                .withItems(availability);

        facts = getOrderRoutingRulesFacts(itemAvailabilityResponse, orderModel);

        // act
        rulesService.executeOrderRoutingRules(facts);

        // assert
        verify(orderPartsRoutedProducer, times(THREE_ORDER_PARTS)).produce(orderPartsRoutedArgumentCaptor.capture());
        verify(orderPartsCreatedProducer, times(TWO_ORDER_PARTS)).produce(orderPartsCreatedArgumentCaptor.capture());

        assertOrderPart(FULFILLMENT_CENTER_EAST, expectedOrderLinesQuantitiesInEast);

        // assert split order parts for Ship From Store
        List<OrderPartsRouted> orderPartsRoutedFound = orderPartsRoutedArgumentCaptor.getAllValues().stream().filter(o -> o.getFulfillmentNode().equals(SHIP_FROM_STORE_NL)).collect(Collectors.toList());
        Optional<OrderPartsCreated> orderPartCreatedFound = orderPartsCreatedArgumentCaptor.getAllValues().stream().filter(o -> o.getFulfillmentNode().equals(SHIP_FROM_STORE_NL)).findAny();

        assertThat("There should be two order parts for SHIP_FROM_STORE_NL.", orderPartsRoutedFound, hasSize(TWO_ORDER_PARTS));
        assertTrue("Order part created should be present with the given warehouse name.", orderPartCreatedFound.isPresent());

        List<String> eans = eans(orderPartsRoutedFound.get(0), orderPartsRoutedFound.get(1));

        assertThat("The amount of order lines should be equal", eans, hasSize(expectedOrderLinesQuantitiesInShipFromStore.size()));
        assertThat(eans, Matchers.equalTo(eans(expectedOrderLinesQuantitiesInShipFromStore)));

        OrderPartsCreated orderPartsCreated = orderPartCreatedFound.get();
        assertThat("There should be two order parts", orderPartsCreated.getOrderParts(), hasSize(2));

        List<OrderLineQuantityModel> orderPartOneToShipFromStore = expectedOrderLinesQuantitiesInShipFromStore.subList(0, 3);
        List<OrderLineQuantityModel> orderPartTwoToShipFromStore = expectedOrderLinesQuantitiesInShipFromStore.subList(3, 7);
        assertThat("The amount of order lines should be equal in the order part 1", orderPartsCreated.getOrderParts().get(0).getOrderLines(), hasSize(orderPartOneToShipFromStore.size()));
        assertThat("The amount of order lines should be equal in the order part 2", orderPartsCreated.getOrderParts().get(1).getOrderLines(), hasSize(orderPartTwoToShipFromStore.size()));

        assertThat(eans(orderPartsCreated), Matchers.equalTo(eans(expectedOrderLinesQuantitiesInShipFromStore)));
    }

    /**
     * Scenario: A big order with more 16 items has both availability in NL and FCE and coming from TB channel.
     *
     * <h1>Given</h1>
     * <ol>
     *   <li>An order with 16 order lines</li>
     *   <li>TB in orderId</li>
     *   <li>Order goes to FCE</li>
     *   <li>Availability is spread: order has 9 items in FCE, 7 in SFS</li>
     * </ol>
     *
     * <h1>When</h1>
     * <ol>
     *   <li>ItemAvailabilityResponse is received</li>
     * </ol>
     *
     * <h1>Then</h1>
     * <ol>
     *   <li>OrderPartsRouted is created</li>
     *   <li>All order lines are routed to FCE</li>
     *   <li>All order lines are present on one order part</li>
     * </ol>
     */
    @Test
    // FIXME: Check also non-standard shipment is calling
    public void executeOrderRoutingRules_availabilitySpreadBetweenSfsWarehousesAndTradeByteOrder_noOrderSplit() {
        // arrange
        orderModel.setOrderLineQuantities(OrderModelGenerator.createNOrderLines(orderModel.getOrderId(), 16));
        orderModel.getOrderLineQuantities().forEach(orderLine -> orderLine.setBrand("jack-jones"));
        orderModel.setOrderId("TB" + orderModel.getOrderId());

        orderService.saveOrderModel(orderModel);

        List<OrderLineQuantityModel> expectedOrderLinesAvailabilityInShipFromStore = orderModel.getOrderLineQuantities().subList(0, 7);
        List<OrderLineQuantityModel> expectedOrderLinesAvailabilityInEast = orderModel.getOrderLineQuantities().subList(7, 16);

        List<Item> availability = generateAvailabilityItems(expectedOrderLinesAvailabilityInEast, ean -> createItem(ean,
                createItemAvailability(FULFILLMENT_CENTER_EAST, FULL_AVAILABILITY),
                createItemAvailability(FULFILLMENT_CENTER_WEST, ZERO_AVAILABILITY),
                createItemAvailability(SHIP_FROM_STORE_NL, ZERO_AVAILABILITY)));

        // seven items are only in the SHIP_FROM_STORE_NL
        List<Item> shipFromStoreAvailability = generateAvailabilityItems(expectedOrderLinesAvailabilityInShipFromStore, ean -> createItem(ean,
                createItemAvailability(FULFILLMENT_CENTER_EAST, ZERO_AVAILABILITY),
                createItemAvailability(FULFILLMENT_CENTER_WEST, ZERO_AVAILABILITY),
                createItemAvailability(SHIP_FROM_STORE_NL, FULL_AVAILABILITY)));

        availability.addAll(shipFromStoreAvailability);

        itemAvailabilityResponse = new ItemAvailabilityResponse()
                .withCorrelationId(orderModel.getOrderId())
                .withItems(availability);

        facts = getOrderRoutingRulesFacts(itemAvailabilityResponse, orderModel);

        // act
        rulesService.executeOrderRoutingRules(facts);

        // assert
        verify(orderPartsRoutedProducer, times(ONE_ORDER_PART)).produce(orderPartsRoutedArgumentCaptor.capture());
        verify(orderPartsCreatedProducer, times(ONE_ORDER_PART)).produce(orderPartsCreatedArgumentCaptor.capture());

        assertOrderPart(FULFILLMENT_CENTER_EAST, orderModel.getOrderLineQuantities());

        // assert split order parts for Ship From Store
        List<OrderPartsRouted> orderPartsRoutedFound =
                orderPartsRoutedArgumentCaptor.getAllValues().stream()
                                              .filter(o -> o.getFulfillmentNode().equals(FULFILLMENT_CENTER_EAST))
                                              .collect(Collectors.toList());

        Optional<OrderPartsCreated> orderPartCreatedFound =
                orderPartsCreatedArgumentCaptor.getAllValues().stream()
                                               .filter(o -> o.getFulfillmentNode().equals(FULFILLMENT_CENTER_EAST))
                                               .findAny();

        assertThat("There should be one order part for FCE.", orderPartsRoutedFound, hasSize(ONE_ORDER_PART));
        assertTrue("Order part created should be present with the given warehouse name.", orderPartCreatedFound.isPresent());

        List<String> eans = eans(orderPartsRoutedFound.get(0));

        assertThat("The amount of order lines should be equal", eans, hasSize(orderModel.getOrderLineQuantities().size()));
        assertThat(eans, Matchers.equalTo(eans(orderModel)));

        OrderPartsCreated orderPartsCreated = orderPartCreatedFound.get();
        assertThat("There should be one order part", orderPartsCreated.getOrderParts(), hasSize(1));
    }
}
