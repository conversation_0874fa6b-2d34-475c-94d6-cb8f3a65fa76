package com.bestseller.services.orderrouting.flows.orderrouting.normalrouting;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.Item;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailabilityResponse;
import com.bestseller.services.orderrouting.feature.toggles.ORSFeatures;
import com.bestseller.services.orderrouting.flows.orderrouting.RoutingBase;
import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.utils.generator.OrderModelGenerator;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

import static com.bestseller.services.orderrouting.constants.FulfillmentNodeConstants.DEFAULT_WAREHOUSE;
import static com.bestseller.services.orderrouting.constants.FulfillmentNodeConstants.FULFILLMENT_CENTER_WEST;
import static com.bestseller.services.orderrouting.constants.FulfillmentNodeConstants.FULFILLMENT_CENTER_EAST;
import static com.bestseller.services.orderrouting.constants.FulfillmentNodeConstants.SHIP_FROM_STORE_DE;
import static com.bestseller.services.orderrouting.constants.FulfillmentNodeConstants.SHIP_FROM_STORE_NL;
import static com.bestseller.services.orderrouting.utils.generator.ItemAvailabilityGenerator.FULL_AVAILABILITY;
import static com.bestseller.services.orderrouting.utils.generator.ItemAvailabilityGenerator.ZERO_AVAILABILITY;
import static com.bestseller.services.orderrouting.utils.generator.ItemAvailabilityGenerator.createItem;
import static com.bestseller.services.orderrouting.utils.generator.ItemAvailabilityGenerator.createItemAvailability;
import static org.mockito.Mockito.verify;

@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(
    webEnvironment = SpringBootTest.WebEnvironment.MOCK,
    properties = "KAFKA_TOPIC_PREFIX=NoAvailabilityIntegrationTest")
public class NoAvailabilityIntegrationTest extends RoutingBase {
    @Before
    public void setUp() {
        ORSFeatures.PRIORITIZE_FCW_OVER_FCE.setActive(true);
        orderModel = OrderModelGenerator.createTestOrderModel();
        orderModel.setOrderLineQuantities(OrderModelGenerator.createThreeTestOrderLines(orderModel));

        orderService.saveOrderModel(orderModel);
    }

    @After
    public void tearDown() {
        ORSFeatures.PRIORITIZE_FCW_OVER_FCE.setActive(false);
    }

    /**
     * Scenario: Some order lines have no availability, order is send to default warehouse.
     *
     * <h1>Given</h1>
     * <ol>
     *   <li>An order with 3 order lines</li>
     *   <li>Availability is in Fulfillment Center West warehouse, but one order line is not in any warehouse</li>
     * </ol>
     *
     * <h1>When</h1>
     * <ol>
     *   <li>ItemAvailabilityResponse is received</li>
     * </ol>
     *
     * <h1>Then</h1>
     * <ol>
     *   <li>OrderPartsRouted is created</li>
     *   <li>All order lines are routed to DEFAULT_WAREHOUSE</li>
     *   <li>All order lines are present per order part</li>
     * </ol>
     */
    @Test
    public void executeOrderRoutingRules_availabilityMissingForOneOrderLine_orderRoutedToDefaultWarehouse() {
        // arrange
        var orderLineQuantityModels = orderModel.getOrderLineQuantities();

        var items = generateAvailabilityItems(orderLineQuantityModels, ean -> createItem(ean,
                createItemAvailability(FULFILLMENT_CENTER_EAST, ZERO_AVAILABILITY),
                createItemAvailability(FULFILLMENT_CENTER_WEST, FULL_AVAILABILITY),
                createItemAvailability(SHIP_FROM_STORE_NL, ZERO_AVAILABILITY)));
        items.get(0).getItemAvailability().get(1).setAvailableQuantity(ZERO_AVAILABILITY);

        itemAvailabilityResponse = new ItemAvailabilityResponse()
                .withCorrelationId(orderModel.getOrderId())
                .withItems(items);

        facts = getOrderRoutingRulesFacts(itemAvailabilityResponse, orderModel);

        // act
        rulesService.executeOrderRoutingRules(facts);

        // assert
        verify(orderPartsRoutedProducer).produce(orderPartsRoutedArgumentCaptor.capture());
        verify(orderPartsCreatedProducer).produce(orderPartsCreatedArgumentCaptor.capture());

        assertOrderPart(DEFAULT_WAREHOUSE, orderLineQuantityModels);
    }

    /**
     * Scenario: All order lines do not have availability, order is send to default warehouse.
     *
     * <h1>Given</h1>
     * <ol>
     *   <li>An order with 3 order lines</li>
     *   <li>All order lines are not in any warehouse</li>
     * </ol>
     *
     * <h1>When</h1>
     * <ol>
     *   <li>ItemAvailabilityResponse is received</li>
     * </ol>
     *
     * <h1>Then</h1>
     * <ol>
     *   <li>OrderPartsRouted is created</li>
     *   <li>All order lines are routed to Default Warehouse</li>
     *   <li>All order lines are present per order part</li>
     * </ol>
     */
    @Test
    public void executeOrderRoutingRules_availabilityMissingForAllOrderLines_orderRoutedToDefaultWarehouse() {
        // arrange
        List<OrderLineQuantityModel> orderLineQuantityModels = orderModel.getOrderLineQuantities();

        List<Item> items = generateAvailabilityItems(orderLineQuantityModels, ean -> createItem(ean,
                createItemAvailability(FULFILLMENT_CENTER_EAST, ZERO_AVAILABILITY),
                createItemAvailability(FULFILLMENT_CENTER_WEST, ZERO_AVAILABILITY),
                createItemAvailability(SHIP_FROM_STORE_NL, ZERO_AVAILABILITY)));

        itemAvailabilityResponse = new ItemAvailabilityResponse()
                .withCorrelationId(orderModel.getOrderId())
                .withItems(items);

        facts = getOrderRoutingRulesFacts(itemAvailabilityResponse, orderModel);

        // act
        rulesService.executeOrderRoutingRules(facts);

        // assert
        verify(orderPartsRoutedProducer).produce(orderPartsRoutedArgumentCaptor.capture());
        verify(orderPartsCreatedProducer).produce(orderPartsCreatedArgumentCaptor.capture());

        assertOrderPart(DEFAULT_WAREHOUSE, orderLineQuantityModels);
    }

    /**
     * Scenario: All order lines do not have availability in FCE, but they have in SFS for this country. Though, SFS is not enabled.
     *
     * <h1>Given</h1>
     * <ol>
     *   <li>An order with 3 order lines</li>
     *   <li>All order lines are not in any warehouse</li>
     *   <li>Order lines are in SFS DE</li>
     *   <li>SFS DE is not enabled</li>
     * </ol>
     *
     * <h1>When</h1>
     * <ol>
     *   <li>ItemAvailabilityResponse is received</li>
     * </ol>
     *
     * <h1>Then</h1>
     * <ol>
     *   <li>OrderPartsRouted is created</li>
     *   <li>All order lines are routed to DEFAULT WAREHOUSE</li>
     *   <li>All order lines are present per order part</li>
     * </ol>
     */
    @Test
    public void executeOrderRoutingRules_availabilityMissingForAllOrderLinesButInSfs_orderRoutedToDefaultWarehouse() {
        // arrange
        orderModel.getShippingAddress().setCountry("DE");

        var orderLineQuantityModels = orderModel.getOrderLineQuantities();

        var items = generateAvailabilityItems(orderLineQuantityModels, ean -> createItem(ean,
                createItemAvailability(FULFILLMENT_CENTER_EAST, ZERO_AVAILABILITY),
                createItemAvailability(FULFILLMENT_CENTER_WEST, ZERO_AVAILABILITY),
                createItemAvailability(SHIP_FROM_STORE_DE, FULL_AVAILABILITY)));

        itemAvailabilityResponse = new ItemAvailabilityResponse()
                .withCorrelationId(orderModel.getOrderId())
                .withItems(items);

        facts = getOrderRoutingRulesFacts(itemAvailabilityResponse, orderModel);

        // act
        rulesService.executeOrderRoutingRules(facts);

        // assert
        verify(orderPartsRoutedProducer).produce(orderPartsRoutedArgumentCaptor.capture());
        verify(orderPartsCreatedProducer).produce(orderPartsCreatedArgumentCaptor.capture());

        assertOrderPart(DEFAULT_WAREHOUSE, orderLineQuantityModels);
    }
}
