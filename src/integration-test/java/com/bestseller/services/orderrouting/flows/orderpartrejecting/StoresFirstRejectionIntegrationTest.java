package com.bestseller.services.orderrouting.flows.orderpartrejecting;

import com.bestseller.services.orderrouting.constants.CountryConstants;
import com.bestseller.services.orderrouting.feature.toggles.ORSFeatures;
import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.model.OrderLineQuantityStatus;
import com.bestseller.services.orderrouting.rules.facts.FactNames;
import com.bestseller.services.orderrouting.rules.orderpartrejecting.actions.RerouteOrderPartAction;
import com.bestseller.services.orderrouting.service.CancellationReason;
import com.bestseller.services.orderrouting.utils.generator.OrderModelGenerator;
import com.bestseller.services.orderrouting.utils.generator.OrderPartRejectedGenerator;
import org.jeasy.rules.api.Facts;
import org.junit.Test;
import org.junit.jupiter.api.AfterEach;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Collections;
import java.util.List;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.hasSize;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;

@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
    properties = "KAFKA_TOPIC_PREFIX=StoresFirstRejectionIntegrationTest")
public class StoresFirstRejectionIntegrationTest extends RejectionBase {

    @Value("${warehouse.fulfillment-center-east}")
    private String fce;

    @AfterEach
    public void tearDown() {
        ORSFeatures.PRIORITIZE_FCW_OVER_FCE.setActive(false);
    }

    /**
     * Scenario: Order lines from order (stores first) which are rejected are rerouted.
     *
     * <h1>Given</h1>
     * <ol>
     *   <li>An order was routed to SFS Stores First country</li>
     * </ol>
     *
     * <h1>When</h1>
     * <ol>
     *   <li>An order part rejected is received for this order</li>
     * </ol>
     *
     * <h1>Then</h1>
     * <ol>
     *   <li>Order Part is rerouted to primary warehouse</li>
     * </ol>
     */
    @Test
    public void executeOrderPartRejectedRules_givenSfsFirstRejection_orderPartRerouted() {
        // arrange
        arrangeBaseRejection(CountryConstants.NORWAY);
        List<OrderLineQuantityModel> orderLineQuantityModels = orderModel.getOrderLineQuantities();

        // act
        rulesService.executeOrderPartRejectedRules(facts);

        // assert
        verify(orderPartsRoutedProducer).produce(orderPartsRoutedArgumentCaptor.capture());
        verify(orderPartsCreatedProducer).produce(orderPartsCreatedArgumentCaptor.capture());

        assertOrderPart(fce, orderLineQuantityModels);
    }

    /**
     * Scenario: Order lines from jj order (stores first) which are rejected are rerouted.
     *
     * <h1>Given</h1>
     * <ol>
     *   <li>An order was routed to SFS Stores First country and checkout from JJ</li>
     * </ol>
     *
     * <h1>When</h1>
     * <ol>
     *   <li>An order part rejected is received for this order</li>
     *   <li>Fulfillment center west toggle is enabled</li>
     * </ol>
     *
     * <h1>Then</h1>
     * <ol>
     *   <li>Order Part is rerouted to fulfillment center west</li>
     * </ol>
     */
    @Test
    public void executeOrderPartRejectedRules_givenSfsFirstRejection_orderPartReroutedToFcw() {
        // arrange
        ORSFeatures.PRIORITIZE_FCW_OVER_FCE.setActive(true);
        arrangeBaseRejection(CountryConstants.NORWAY, "jj");
        List<OrderLineQuantityModel> orderLineQuantityModels = orderModel.getOrderLineQuantities();

        // act
        rulesService.executeOrderPartRejectedRules(facts);

        // assert
        verify(orderPartsRoutedProducer).produce(orderPartsRoutedArgumentCaptor.capture());
        verify(orderPartsCreatedProducer).produce(orderPartsCreatedArgumentCaptor.capture());

        assertOrderPart("INGRAM_MICRO_NL", orderLineQuantityModels);
    }

    /**
     * Scenario: Order lines from split order (stores first) which are rejected are rerouted.
     *
     * <h1>Given</h1>
     * <ol>
     *   <li>An order was routed to SFS Stores first country</li>
     *   <li>The order was split between FCE and SFS</li>
     * </ol>
     *
     * <h1>When</h1>
     * <ol>
     *   <li>An order part rejected is received for SFS part</li>
     * </ol>
     *
     * <h1>Then</h1>
     * <ol>
     *   <li>All order lines from SFS part are cancelled with the same reason received</li>
     * </ol>
     */
    @Test
    public void executeOrderPartRejectedRules_givenSplitSfsFirstRejection_orderLinesCancelledAndNotRerouted() {
        // arrange
        orderModel = OrderModelGenerator.createTestOrderModel();
        orderModel.getShippingAddress().setCountry(CountryConstants.NORWAY);
        orderModel.setOrderLineQuantities(OrderModelGenerator.createThreeTestOrderLines(orderModel));
        orderModel.getOrderLineQuantities().forEach(orderLine -> orderLine.setTotalOrderParts(SFS_EAN_LINE));
        orderModel.getOrderLineQuantities().get(SFS_EAN_LINE).setOrderPartNumber(2);

        orderService.saveOrderModel(orderModel);

        orderPartRejected =
                OrderPartRejectedGenerator.createFullOrderPartRejectedMessage(orderModel.getOrderId(),
                                                                              SFS_WAREHOUSE,
                                                                              Collections.singletonList(orderModel.getOrderLineQuantities()
                                                                                                                  .get(SFS_EAN_LINE).getEan()));

        facts = new Facts();
        facts.put(FactNames.ORDER_MODEL_FACT, orderModel);
        facts.put(FactNames.ORDER_PART_REJECTED_FACT, orderPartRejected);
        // act
        rulesService.executeOrderPartRejectedRules(facts);

        // assert
        verify(orderPartsRoutedProducer, never()).produce(any());
        verify(orderPartsCreatedProducer, never()).produce(any());

        verify(spiedOrderLineQuantityModelRepository).saveAll(orderLinesQuantityModelCaptor.capture());
        List<OrderLineQuantityModel> orderLinesCancelled = orderLinesQuantityModelCaptor.getValue();

        assertThat("Only one line should be cancelled", orderLinesCancelled, hasSize(1));
        assertThat("EAN from cancelled line should match SFS EAN", orderLinesCancelled.get(0).getEan(),
                   is(equalTo(orderModel.getOrderLineQuantities().get(SFS_EAN_LINE).getEan())));

        String cancelReason = orderPartRejected.getOrderLines().get(0).getCancelReason();
        orderLinesCancelled.forEach(orderLine -> {
            assertThat("Order status should be cancelled", orderLine.getStatus(),
                       is(equalTo(OrderLineQuantityStatus.CANCELLED)));
            assertThat("Cancel reason should be match", orderLine.getCancelReason(),
                       is(equalTo(CancellationReason.valueOf(cancelReason))));
        });
    }

}
