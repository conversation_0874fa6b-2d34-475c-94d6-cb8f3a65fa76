package com.bestseller.services.orderrouting.flows.orderplacing;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailabilityRequest;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsRouted.OrderPartsRouted;
import com.bestseller.services.orderrouting.utils.generator.OrderForFulfillmentGenerator;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.containsInAnyOrder;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.timeout;
import static org.mockito.Mockito.verify;

@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(
    webEnvironment = SpringBootTest.WebEnvironment.MOCK,
    properties = "KAFKA_TOPIC_PREFIX=OrderForFulfillmentIntegrationTest")
public class OrderForFulfillmentIntegrationTest extends OrderPlacingBase {

    @Captor
    protected ArgumentCaptor<OrderPartsRouted> orderPartsRoutedCaptor;

    /**
     * Scenario: Order comes in without predefined fulfillment node.
     *
     * <h1>Given</h1>
     * <ol>
     *   <li>An order with 5 order lines</li>
     *   <li>fulfillmentNode on the order is not set</li>
     * </ol>
     *
     * <h1>When</h1>
     * <ol>
     *   <li>OrderForFulfillment is received</li>
     * </ol>
     *
     * <h1>Then</h1>
     * <ol>
     *   <li>ItemAvailabilityRequest is created</li>
     *   <li>All order lines are present in item availability request</li>
     * </ol>
     *
     */
    @Test
    public void executeOrderPlacingRules_normalOrder_itemAvailabilityRequested() throws InterruptedException {
        // arrange
        orderForFulfillment = OrderForFulfillmentGenerator.createFullOrderForFulfillmentMessage();
        orderForFulfillment.setOrderLines(OrderForFulfillmentGenerator.createOrderLines(5));

        convertAndSaveToOrderModel(orderForFulfillment);

        facts = getPlacingRulesFacts(orderForFulfillment, orderModel);

        // act
        rulesService.executeOrderPlacingRules(facts);

        // assert
        assertItemAvailabilityRequest();
    }

    /**
     * Scenario: Order comes in with minimum amount of fields.
     *
     * <h1>Given</h1>
     * <ol>
     *   <li>An order with 5 order lines</li>
     *   <li>fulfillmentNode on the order is not set</li>
     *   <li>minimum amount of fields are set</li>
     * </ol>
     *
     * <h1>When</h1>
     * <ol>
     *   <li>OrderForFulfillment is received</li>
     * </ol>
     *
     * <h1>Then</h1>
     * <ol>
     *   <li>ItemAvailabilityRequest is created</li>
     *   <li>All order lines are present in item availability request</li>
     * </ol>
     *
     */
    @Test
    public void executeOrderPlacingRules_orderWithMinimalFields_itemAvailabilityRequested() throws InterruptedException {
        // arrange
        orderForFulfillment = OrderForFulfillmentGenerator.createMinValidOrderForFulfillmentMessage();
        orderForFulfillment.setOrderLines(OrderForFulfillmentGenerator.createOrderLines(5));

        convertAndSaveToOrderModel(orderForFulfillment);

        facts = getPlacingRulesFacts(orderForFulfillment, orderModel);

        // act
        rulesService.executeOrderPlacingRules(facts);

        // assert
        assertItemAvailabilityRequest();
    }

    private void assertItemAvailabilityRequest() {
        verify(itemAvailabilityRequestProducer).produce(itemAvailabilityArgumentCaptor.capture());

        verify(orderPartsRoutedProducer, never()).produce(any());
        verify(orderPartsCreatedProducer, never()).produce(any());

        ItemAvailabilityRequest itemAvailabilityRequest = itemAvailabilityArgumentCaptor.getValue();
        assertThat("Correlation id is the same as the order", itemAvailabilityRequest.getCorrelationId(),
                equalTo(orderForFulfillment.getOrderId()));
        assertThat("All eans are present in item availability request", itemAvailabilityRequest.getEans(),
                containsInAnyOrder(orderForFulfillment.getOrderLines().stream().map(OrderLine::getEan).toArray()));
    }


}
