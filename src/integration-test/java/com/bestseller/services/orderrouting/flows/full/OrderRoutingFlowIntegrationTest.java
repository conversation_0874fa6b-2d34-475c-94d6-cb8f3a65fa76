package com.bestseller.services.orderrouting.flows.full;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.Item;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailability;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailabilityResponse;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderForFulfillment.OrderForFulfillment;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsCreated.OrderPart;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsCreated.OrderPartsCreated;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsRouted.OrderPartsRouted;
import com.bestseller.services.orderrouting.utils.CacheSupervisor;
import com.bestseller.services.orderrouting.utils.TestConsumer;
import lombok.extern.slf4j.Slf4j;
import org.hamcrest.Matchers;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.bestseller.services.orderrouting.constants.FulfillmentNodeConstants.FULFILLMENT_CENTER_EAST;
import static com.bestseller.services.orderrouting.constants.FulfillmentNodeConstants.SHIP_FROM_STORE_NL;
import static com.bestseller.services.orderrouting.utils.EanUtils.eans;
import static org.awaitility.Awaitility.await;
import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.hasSize;

@RunWith(SpringRunner.class)
@SpringBootTest(
    webEnvironment = SpringBootTest.WebEnvironment.MOCK,
    properties = "KAFKA_TOPIC_PREFIX=OrderRoutingFlowIntegrationTest")
@ActiveProfiles("dev")
@Slf4j
public class OrderRoutingFlowIntegrationTest extends OrderFullFlowBase {

    private TestConsumer<OrderPartsRouted> orderPartsRouted;
    private TestConsumer<OrderPartsCreated> orderPartsCreated;

    @Before
    public void setUp() {
        cacheSupervisor = new CacheSupervisor(orderModelRepository, orderLineQuantityModelRepository);
        orderPartsRouted = new TestConsumer<>(OrderPartsRouted.class, orderPartsRoutedTopic);
        orderPartsCreated = new TestConsumer<>(OrderPartsCreated.class, orderPartsCreatedTopic);
    }

    /**
     * Scenario: Basic flow, no split.
     *
     * <h1>Given</h1>
     * <ol>
     *   <li>An order with 5 order lines</li>
     *   <li>All items available in warehouse</li>
     * </ol>
     *
     * <h1>When</h1>
     * <ol>
     *   <li>OrderForFulfillment is received</li>
     *   <li>ItemAvailabilityResponse is received</li>
     * </ol>
     *
     * <h1>Then</h1>
     * <ol>
     *   <li>OrderPartsRouted and OrderPartsCreated are generated</li>
     *   <li>FCE is fulfilling items</li>
     *   <li>All items present in order for fulfillment are in the message produced</li>
     *   <li>Order should be routed to FCE</li>
     * </ol>
     *
     * @throws Exception exception
     */
    @Test
    public void basicOrder_givenOrderWithOneWarehouse_producesOneMessage() throws Exception {
        // arrange
        OrderForFulfillment orderForFulfillment = createOrderForFulfillmentAndItemAvailabilityResponse(5, 0);

        // act
        var opcMessages = orderPartsCreated.consumeFromHere();
        var oprMessages = orderPartsRouted.consumeFromHere();
        publishOrderForFulfillmentAndItemAvailabilityResponse(orderForFulfillment);

        // assert
        OrderPartsCreated orderPartsCreated = await("order parts created")
                .until(opcMessages::next, message ->
                        orderForFulfillment.getOrderId().equals(message.getOrderId()));
        OrderPartsRouted orderPartsRouted = await("order parts created")
                .until(oprMessages::next, message ->
                        orderForFulfillment.getOrderId().equals(message.getOrderId()));
        assertThat("Node mismatch", orderPartsCreated.getFulfillmentNode(), is(equalTo(orderPartsRouted.getFulfillmentNode())));
        assertThat("Wrong node", orderPartsRouted.getFulfillmentNode(), is(equalTo(FULFILLMENT_CENTER_EAST)));

        assertThat("OPC should have one part", orderPartsCreated.getOrderParts(), hasSize(1));

        assertThat("OPC Order part should have same size than order for fulfillment",
                   orderPartsCreated.getOrderParts().get(0).getOrderLines(),
                   hasSize(orderForFulfillment.getOrderLines().size()));

        assertThat("OPR Order part should have same size than order for fulfillment",
                   orderPartsRouted.getOrderLines(), hasSize(orderForFulfillment.getOrderLines().size()));

        assertThat("Wrong EANs", eans(orderPartsCreated), Matchers.equalTo(eans(orderForFulfillment)));
        assertThat("Wrong EANs", eans(orderPartsRouted), Matchers.equalTo(eans(orderForFulfillment)));


        verifyLineFulfillmentNode(orderPartsCreated, itemAvailabilityResponse);
        verifyLineFulfillmentNode(orderPartsRouted, itemAvailabilityResponse);
    }

    /**
     * Scenario: Split order between two warehouses produces two messages.
     *
     * <h1>Given</h1>
     * <ol>
     *   <li>An order with 5 order lines</li>
     *   <li>2 Items not available in warehouse</li>
     *   <li>SFS enabled for the country of the order</li>
     * </ol>
     *
     * <h1>When</h1>
     * <ol>
     *   <li>OrderForFulfillment is received</li>
     *   <li>ItemAvailabilityResponse is received</li>
     * </ol>
     *
     * <h1>Then</h1>
     * <ol>
     *   <li>OrderPartsRouted and OrderPartsCreated are generated</li>
     *   <li>FCE is fulfilling items in Ingram micro</li>
     *   <li>SFS is fulfilling the rest of the order</li>
     *   <li>All items present in order for fulfillment are in the message produced</li>
     * </ol>
     *
     * @throws Exception exception
     */
    @Test
    public void splitOrder_givenSplitOrderWithTwoWarehouses_producesTwoMessages() throws Exception {
        // arrange
        OrderForFulfillment orderForFulfillment = createOrderForFulfillmentAndItemAvailabilityResponse(3, 2);

        // act
        var opcMessages = orderPartsCreated.consumeFromHere();
        var oprMessages = orderPartsRouted.consumeFromHere();
        publishOrderForFulfillmentAndItemAvailabilityResponse(orderForFulfillment);

        // assert
        OrderPartsCreated opcEastPart = await("order parts created for FCE")
                .until(opcMessages::next, message ->
                        orderForFulfillment.getOrderId().equals(message.getOrderId())
                        && FULFILLMENT_CENTER_EAST.equalsIgnoreCase(message.getFulfillmentNode()));

        OrderPartsRouted oprEastPart = await("order parts routed for FCE")
                .until(oprMessages::next, message ->
                        orderForFulfillment.getOrderId().equals(message.getOrderId())
                        && FULFILLMENT_CENTER_EAST.equalsIgnoreCase(message.getFulfillmentNode()));

        assertThat("FCE part should have 1 part", opcEastPart.getOrderParts(), hasSize(1));

        assertThat("Wrong # of lines", opcEastPart.getOrderParts().get(0).getOrderLines(), hasSize(3));
        assertThat("Wrong # of lines", oprEastPart.getOrderLines(), hasSize(3));

        OrderPartsCreated opcSfsPart = await("order parts created for SFS NL")
                .until(opcMessages::next, message ->
                        orderForFulfillment.getOrderId().equals(message.getOrderId())
                        && SHIP_FROM_STORE_NL.equalsIgnoreCase(message.getFulfillmentNode()));

        OrderPartsRouted oprSfsPart = await("order parts routed for SFS NL")
                .until(oprMessages::next, message ->
                        orderForFulfillment.getOrderId().equals(message.getOrderId())
                        && SHIP_FROM_STORE_NL.equalsIgnoreCase(message.getFulfillmentNode()));

        assertThat("SFS OrderPartsCreated should have 1 part", opcSfsPart.getOrderParts(), hasSize(1));

        assertThat("Wrong # of lines", opcSfsPart.getOrderParts().get(0).getOrderLines(), hasSize(2));
        assertThat("Wrong # of lines", oprSfsPart.getOrderLines(), hasSize(2));

        List<String> expectedEans = eans(orderForFulfillment);

        assertThat("Wrong EANs", eans(opcEastPart, opcSfsPart), Matchers.equalTo(expectedEans));

        assertThat("Wrong EANs", eans(oprEastPart, oprSfsPart), Matchers.equalTo(expectedEans));

        verifyLineFulfillmentNode(opcEastPart, itemAvailabilityResponse);
        verifyLineFulfillmentNode(opcSfsPart, itemAvailabilityResponse);

        verifyLineFulfillmentNode(oprEastPart, itemAvailabilityResponse);
        verifyLineFulfillmentNode(oprSfsPart, itemAvailabilityResponse);
    }

    /**
     * Scenario: When a big order is split in several parts with the same fulfillment warehouse, only one message is
     * produced, containing all parts and items of the order for fulfillment.
     *
     * <h1>Given</h1>
     * <ol>
     *   <li>A big (>10) order</li>
     *   <li>Items only available in SFS</li>
     *   <li>SFS enabled for the country of the order</li>
     * </ol>
     *
     * <h1>When</h1>
     * <ol>
     *   <li>OrderForFulfillment is received</li>
     *   <li>ItemAvailabilityResponse is received</li>
     * </ol>
     *
     * <h1>Then</h1>
     * <ol>
     *   <li>One OrderPartsCreated message is produced with SFS as fulfillment node</li>
     *   <li>OrderPartsCreated contains two order parts</li>
     *   <li>Two OrderPartsRouted messages are produced with SFS as fulfillment node</li>
     *   <li>Warehouse is SFS for both OrderPartsCreated and OrderPartsRouted</li>
     *   <li>All items present in order for fulfillment are in the message produced</li>
     * </ol>
     *
     * @throws Exception exception
     */
    @Test
    public void orderPartsCreated_givenSplitOrderSameWarehouse_producesOneMessage() throws Exception {
        // arrange
        OrderForFulfillment orderForFulfillment = createOrderForFulfillmentAndItemAvailabilityResponse(0, 11);

        // act
        var opcMessages = orderPartsCreated.consumeFromHere();
        var oprMessages = orderPartsRouted.consumeFromHere();
        publishOrderForFulfillmentAndItemAvailabilityResponse(orderForFulfillment);

        // assert
        OrderPartsCreated orderPartsCreated = await("order parts created for SFS NL")
                .until(opcMessages::next, message ->
                        orderForFulfillment.getOrderId().equals(message.getOrderId())
                        && SHIP_FROM_STORE_NL.equalsIgnoreCase(message.getFulfillmentNode()));

        assertThat("OrderPartsCreated has two parts", orderPartsCreated.getOrderParts(), hasSize(2));

        OrderPartsRouted oprFirstPart = await("order part #1 routed for SFS NL")
                .until(oprMessages::next, message ->
                        orderForFulfillment.getOrderId().equals(message.getOrderId())
                        && SHIP_FROM_STORE_NL.equalsIgnoreCase(message.getFulfillmentNode())
                        && message.getOrderPartNumber() == 1);


        OrderPartsRouted oprSecondPart = await("order part #2 routed for SFS NL")
                .until(oprMessages::next, message ->
                        orderForFulfillment.getOrderId().equals(message.getOrderId())
                        && SHIP_FROM_STORE_NL.equalsIgnoreCase(message.getFulfillmentNode())
                        && message.getOrderPartNumber() == 2);

        assertThat("Wrong node", orderPartsCreated.getFulfillmentNode(), is(equalTo(SHIP_FROM_STORE_NL)));
        assertThat("Wrong node", oprFirstPart.getFulfillmentNode(), is(equalTo(SHIP_FROM_STORE_NL)));
        assertThat("Wrong node", oprSecondPart.getFulfillmentNode(), is(equalTo(SHIP_FROM_STORE_NL)));

        List<OrderLine> opcOrderLines = getOrderLines(orderPartsCreated);
        assertThat("Wrong # of lines", orderPartsCreated.getOrderParts().get(0).getOrderLines(), hasSize(5));
        assertThat("Wrong # of lines", orderPartsCreated.getOrderParts().get(1).getOrderLines(), hasSize(6));
        assertThat("Wrong # of lines", opcOrderLines, hasSize(11));

        assertThat("Wrong # of lines", oprFirstPart.getOrderLines(), hasSize(5));
        assertThat("Wrong # of lines", oprSecondPart.getOrderLines(), hasSize(6));

        List<String> expectedEans = eans(orderForFulfillment);

        List<String> opcRoutedEans = eans(orderPartsCreated);
        List<String> oprRoutedEans = eans(oprFirstPart, oprSecondPart);

        assertThat("Wrong EANs", opcRoutedEans, Matchers.equalTo(expectedEans));
        assertThat("Wrong EANs", oprRoutedEans, Matchers.equalTo(expectedEans));

        verifyLineFulfillmentNode(orderPartsCreated, itemAvailabilityResponse);
        verifyLineFulfillmentNode(oprFirstPart, itemAvailabilityResponse);
        verifyLineFulfillmentNode(oprSecondPart, itemAvailabilityResponse);
    }

    private List<OrderLine> getOrderLines(OrderPartsCreated orderPartsCreated) {
        return orderPartsCreated.getOrderParts()
                                .stream()
                                .map(OrderPart::getOrderLines)
                                .flatMap(Collection::stream)
                                .collect(Collectors.toList());
    }

    private void verifyLineFulfillmentNode(OrderPartsRouted orderPartsRouted, ItemAvailabilityResponse iar) {
        verifyOrderLineFulfillmentNode(orderPartsRouted.getFulfillmentNode(),
                                       orderPartsRouted.getOrderLines().stream(),
                                       iar);
    }

    private void verifyLineFulfillmentNode(OrderPartsCreated orderPartsCreated, ItemAvailabilityResponse iar) {
        Stream<OrderLine> orderLineStream = orderPartsCreated.getOrderParts()
                                                             .stream()
                                                             .map(OrderPart::getOrderLines)
                                                             .flatMap(Collection::stream);
        verifyOrderLineFulfillmentNode(orderPartsCreated.getFulfillmentNode(), orderLineStream, iar);
    }

    public void verifyOrderLineFulfillmentNode(String assignedFulfillmentNode,
                                               Stream<OrderLine> orderLineStream,
                                               ItemAvailabilityResponse iar) {
        orderLineStream.forEach(orderLine -> assertThat("Order line was routed to the correct warehouse",
                                                        assignedFulfillmentNode,
                                                        is(equalTo(findWarehouseFromItemAvailabilityResponse(orderLine.getEan(), iar, assignedFulfillmentNode)))));
    }

    private String findWarehouseFromItemAvailabilityResponse(String ean,
                                                             ItemAvailabilityResponse response,
                                                             String assignedFulfillmentNode) {
        return response.getItems()
                       .stream()
                       .filter(it -> it.getEan().equals(ean) && it.getItemAvailability().stream().anyMatch(itemAvailability -> itemAvailability.getWarehouse().equals(assignedFulfillmentNode)))
                       .findFirst()
                       .map(Item::getItemAvailability)
                       .orElseThrow(() -> new RuntimeException("EAN " + ean + " not found in ItemAvailabilityResponse"))
                       .stream()
                       .filter(itemAvailability -> itemAvailability.getWarehouse().equals(assignedFulfillmentNode))
                       .findFirst()
                       .map(ItemAvailability::getWarehouse)
                       .orElseThrow(() -> new RuntimeException("Warehouse " + assignedFulfillmentNode + " not found "
                               + "for EAN " + ean + " ItemAvailabilityResponse"));

    }
}
