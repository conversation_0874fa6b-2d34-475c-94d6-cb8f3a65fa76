package com.bestseller.services.orderrouting.flows.orderpartrejecting;

import com.bestseller.services.orderrouting.constants.CountryConstants;
import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.model.OrderLineQuantityStatus;
import com.bestseller.services.orderrouting.rules.facts.FactNames;
import com.bestseller.services.orderrouting.service.CancellationReason;
import com.bestseller.services.orderrouting.utils.generator.OrderModelGenerator;
import com.bestseller.services.orderrouting.utils.generator.OrderPartRejectedGenerator;
import org.jeasy.rules.api.Facts;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Collections;
import java.util.List;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.hasSize;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;

@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
    properties = "KAFKA_TOPIC_PREFIX=SoldOutSituationRejectionIntegrationTest")
public class SoldOutSituationRejectionIntegrationTest extends RejectionBase {

    /**
     * Scenario: Order lines from order (Sold out situation) which are rejected are cancelled.
     *
     * <h1>Given</h1>
     * <ol>
     *   <li>An order was routed to SFS SOLD OUT country</li>
     * </ol>
     *
     * <h1>When</h1>
     * <ol>
     *   <li>An order part rejected is received for this order</li>
     * </ol>
     *
     * <h1>Then</h1>
     * <ol>
     *   <li>All order lines are cancelled with the same reason received</li>
     * </ol>
     */
    @Test
    public void executeOrderPartRejectedRules_givenSoldOutRejection_orderLinesCancelledAndNotRerouted() {
        // arrange
        arrangeBaseRejection(CountryConstants.NETHERLANDS);

        // act
        rulesService.executeOrderPartRejectedRules(facts);

        // assert
        verify(orderPartsRoutedProducer, never()).produce(orderPartsRoutedArgumentCaptor.capture());
        verify(orderPartsCreatedProducer, never()).produce(orderPartsCreatedArgumentCaptor.capture());

        verify(spiedOrderLineQuantityModelRepository).saveAll(orderLinesQuantityModelCaptor.capture());
        List<OrderLineQuantityModel> orderLinesCancelled = orderLinesQuantityModelCaptor.getValue();

        assertThat("Order lines cancelled size should be the same as the original order", orderLinesCancelled,
                   hasSize(orderModel.getOrderLineQuantities().size()));

        String cancelReason = orderPartRejected.getOrderLines().get(0).getCancelReason();
        orderLinesCancelled.forEach(orderLine -> {
            assertThat("Order status should be cancelled", orderLine.getStatus(),
                       is(equalTo(OrderLineQuantityStatus.CANCELLED)));
            assertThat("Cancel reason should be match", orderLine.getCancelReason(),
                       is(equalTo(CancellationReason.valueOf(cancelReason))));
        });
    }


    /**
     * Scenario: Order lines from split order (Sold out situation) which are rejected are cancelled.
     *
     * <h1>Given</h1>
     * <ol>
     *   <li>An order was routed to SFS SOLD OUT country</li>
     *   <li>The order was split between FCE and SFS</li>
     * </ol>
     *
     * <h1>When</h1>
     * <ol>
     *   <li>An order part rejected is received for SFS part</li>
     * </ol>
     *
     * <h1>Then</h1>
     * <ol>
     *   <li>All order lines from SFS part are cancelled with the same reason received</li>
     * </ol>
     */
    @Test
    public void executeOrderPartRejectedRules_givenSplitSoldOutRejection_orderLinesCancelledAndNotRerouted() {
        // arrange
        orderModel = OrderModelGenerator.createTestOrderModel();
        orderModel.setOrderLineQuantities(OrderModelGenerator.createThreeTestOrderLines(orderModel));
        orderModel.getOrderLineQuantities().forEach(orderLine -> orderLine.setTotalOrderParts(SFS_EAN_LINE));
        orderModel.getOrderLineQuantities().get(SFS_EAN_LINE).setOrderPartNumber(2);

        orderService.saveOrderModel(orderModel);

        orderPartRejected =
                OrderPartRejectedGenerator.createFullOrderPartRejectedMessage(orderModel.getOrderId(),
                                                                              SFS_WAREHOUSE,
                                                                              Collections.singletonList(orderModel.getOrderLineQuantities()
                                                                                                                  .get(SFS_EAN_LINE).getEan()));

        facts = new Facts();
        facts.put(FactNames.ORDER_MODEL_FACT, orderModel);
        facts.put(FactNames.ORDER_PART_REJECTED_FACT, orderPartRejected);
        // act
        rulesService.executeOrderPartRejectedRules(facts);

        // assert
        verify(orderPartsRoutedProducer, never()).produce(orderPartsRoutedArgumentCaptor.capture());
        verify(orderPartsCreatedProducer, never()).produce(orderPartsCreatedArgumentCaptor.capture());

        verify(spiedOrderLineQuantityModelRepository).saveAll(orderLinesQuantityModelCaptor.capture());
        List<OrderLineQuantityModel> orderLinesCancelled = orderLinesQuantityModelCaptor.getValue();

        assertThat("Only one line should be cancelled", orderLinesCancelled, hasSize(1));
        assertThat("EAN from cancelled line should match SFS EAN", orderLinesCancelled.get(0).getEan(),
                   is(equalTo(orderModel.getOrderLineQuantities().get(SFS_EAN_LINE).getEan())));

        String cancelReason = orderPartRejected.getOrderLines().get(0).getCancelReason();
        orderLinesCancelled.forEach(orderLine -> {
            assertThat("Order status should be cancelled", orderLine.getStatus(),
                       is(equalTo(OrderLineQuantityStatus.CANCELLED)));
            assertThat("Cancel reason should be match", orderLine.getCancelReason(),
                       is(equalTo(CancellationReason.valueOf(cancelReason))));
        });
    }


}
