package com.bestseller.services.orderrouting.flows.full;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.Item;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailabilityResponse;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderForFulfillment.OrderForFulfillment;
import com.bestseller.services.orderrouting.repository.OrderLineQuantityModelRepository;
import com.bestseller.services.orderrouting.repository.OrderModelRepository;
import com.bestseller.services.orderrouting.utils.CacheSupervisor;
import com.bestseller.services.orderrouting.utils.TestMessageProducer;
import com.bestseller.services.orderrouting.utils.generator.OrderForFulfillmentGenerator;
import org.junit.Rule;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailability.Type.STORE;
import static com.bestseller.services.orderrouting.constants.CountryConstants.NETHERLANDS;
import static com.bestseller.services.orderrouting.constants.CountryConstants.NORWAY;
import static com.bestseller.services.orderrouting.constants.FulfillmentNodeConstants.FULFILLMENT_CENTER_WEST;
import static com.bestseller.services.orderrouting.constants.FulfillmentNodeConstants.FULFILLMENT_CENTER_EAST;
import static com.bestseller.services.orderrouting.constants.FulfillmentNodeConstants.SHIP_FROM_STORE_NL;
import static com.bestseller.services.orderrouting.constants.FulfillmentNodeConstants.SHIP_FROM_STORE_NO;
import static com.bestseller.services.orderrouting.utils.generator.ItemAvailabilityGenerator.FULL_AVAILABILITY;
import static com.bestseller.services.orderrouting.utils.generator.ItemAvailabilityGenerator.ZERO_AVAILABILITY;
import static com.bestseller.services.orderrouting.utils.generator.ItemAvailabilityGenerator.createItem;
import static com.bestseller.services.orderrouting.utils.generator.ItemAvailabilityGenerator.createItemAvailability;

public abstract class OrderFullFlowBase {
    @Rule
    public TestMessageProducer messageProducer = new TestMessageProducer();

    @Autowired
    protected OrderModelRepository orderModelRepository;

    @Autowired
    protected OrderLineQuantityModelRepository orderLineQuantityModelRepository;

    @Value("${spring.cloud.stream.bindings.orderForFulfillment.destination}")
    protected String orderForFulfillmentTopic;

    @Value("${spring.cloud.stream.bindings.itemAvailabilityResponse.destination}")
    protected String itemAvailabilityResponseTopic;

    @Value("${spring.cloud.stream.bindings.orderPartsRouted.destination}")
    protected String orderPartsRoutedTopic;

    @Value("${spring.cloud.stream.bindings.orderPartsCreated.destination}")
    protected String orderPartsCreatedTopic;

    protected CacheSupervisor cacheSupervisor;

    protected ItemAvailabilityResponse itemAvailabilityResponse;


    protected OrderForFulfillment createOrderForFulfillmentAndItemAvailabilityResponse(int eastLines,
                                                                                       int shipFromStoreLines) {
        int totalLines = eastLines + shipFromStoreLines;

        List<OrderLine> orderLines = OrderForFulfillmentGenerator.createOrderLines(totalLines);

        OrderForFulfillment orderForFulfillment = OrderForFulfillmentGenerator.createFullOrderForFulfillmentMessage();
        orderForFulfillment.setOrderLines(orderLines);
        orderForFulfillment.getShippingInformation().getShippingAddress().setCountry(NETHERLANDS);
        orderForFulfillment.getOrderDetails().setShippingMethod("STANDARD");

        Stream<String> orderLinesInEast = orderLines.subList(0, eastLines).stream().map(OrderLine::getEan);
        Stream<String> orderLinesInShipFromStore = orderLines.subList(eastLines, totalLines).stream().map(OrderLine::getEan);

        List<Item> availability = generateAvailabilityItems(orderLinesInEast, ean -> createItem(ean,
                createItemAvailability(FULFILLMENT_CENTER_EAST, FULL_AVAILABILITY),
                createItemAvailability(FULFILLMENT_CENTER_WEST, ZERO_AVAILABILITY),
                createItemAvailability(SHIP_FROM_STORE_NL, ZERO_AVAILABILITY),
                createItemAvailability(SHIP_FROM_STORE_NO, ZERO_AVAILABILITY, STORE, NORWAY)));

        List<Item> shipFromStoreAvailability = generateAvailabilityItems(orderLinesInShipFromStore, ean -> createItem(ean,
                createItemAvailability(FULFILLMENT_CENTER_EAST, ZERO_AVAILABILITY),
                createItemAvailability(FULFILLMENT_CENTER_WEST, ZERO_AVAILABILITY),
                createItemAvailability(SHIP_FROM_STORE_NL, FULL_AVAILABILITY),
                createItemAvailability(SHIP_FROM_STORE_NO, FULL_AVAILABILITY, STORE, NORWAY)));

        availability.addAll(shipFromStoreAvailability);

        itemAvailabilityResponse = new ItemAvailabilityResponse()
                .withCorrelationId(orderForFulfillment.getOrderId())
                .withItems(availability);

        return orderForFulfillment;
    }

    protected List<Item> generateAvailabilityItems(Stream<String> orderLineQuantityModels, Function<String, Item> mapper) {
        return orderLineQuantityModels
                .map(mapper)
                .collect(Collectors.toList());
    }

    protected void publishOrderForFulfillmentAndItemAvailabilityResponse(OrderForFulfillment orderForFulfillment) throws Exception {
        messageProducer.publishMessage(orderForFulfillmentTopic, orderForFulfillment);
        cacheSupervisor.waitUntilOrderFound(orderForFulfillment.getOrderId());

        messageProducer.publishMessage(itemAvailabilityResponseTopic, itemAvailabilityResponse);
    }
}
