package com.bestseller.services.orderrouting.flows.orderrouting.normalrouting;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.Item;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailabilityResponse;
import com.bestseller.services.orderrouting.feature.toggles.ORSFeatures;
import com.bestseller.services.orderrouting.flows.orderrouting.RoutingBase;
import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.utils.generator.OrderModelGenerator;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.List;

import static com.bestseller.services.orderrouting.constants.FulfillmentNodeConstants.FULFILLMENT_CENTER_WEST;
import static com.bestseller.services.orderrouting.constants.FulfillmentNodeConstants.FULFILLMENT_CENTER_EAST;
import static com.bestseller.services.orderrouting.constants.FulfillmentNodeConstants.SHIP_FROM_STORE_NL;
import static com.bestseller.services.orderrouting.utils.generator.ItemAvailabilityGenerator.FULL_AVAILABILITY;
import static com.bestseller.services.orderrouting.utils.generator.ItemAvailabilityGenerator.ZERO_AVAILABILITY;
import static com.bestseller.services.orderrouting.utils.generator.ItemAvailabilityGenerator.createItem;
import static com.bestseller.services.orderrouting.utils.generator.ItemAvailabilityGenerator.createItemAvailability;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(
    webEnvironment = SpringBootTest.WebEnvironment.MOCK,
    properties = "KAFKA_TOPIC_PREFIX=SplitWarehouseIntegrationTest")
public class SplitWarehouseIntegrationTest  extends RoutingBase {
    @Before
    public void setUp() {
        orderModel = OrderModelGenerator.createTestOrderModel();
        orderModel.setOrderLineQuantities(OrderModelGenerator.createThreeTestOrderLines(orderModel));

        orderService.saveOrderModel(orderModel);

        ORSFeatures.PRIORITIZE_FCW_OVER_FCE.setActive(true);
    }

    @After
    public void tearDown() {
        ORSFeatures.PRIORITIZE_FCW_OVER_FCE.setActive(false);
    }

    /**
     * Scenario: Order is split between warehouses.
     *
     * <h1>Given</h1>
     * <ol>
     *   <li>An order with 3 order lines</li>
     *   <li>Availability is in FCE and in FCW (having no overlap)</li>
     * </ol>
     *
     * <h1>When</h1>
     * <ol>
     *   <li>ItemAvailabilityResponse is received</li>
     * </ol>
     *
     * <h1>Then</h1>
     * <ol>
     *   <li>OrderPartsRouted is created</li>
     *   <li>Split into FCE and FCW parts</li>
     *   <li>All order lines are present per order part</li>
     *   <li>Order only has two order parts</li>
     * </ol>
     *
     */
    @Test
    public void executeOrderRoutingRules_availabilityInTwoWarehousesWithoutOverlap_orderRoutedToBothWarehouses() {
        // arrange
        List<OrderLineQuantityModel> orderLineQuantityModels = orderModel.getOrderLineQuantities();

        List<Item> items = generateAvailabilityItems(orderLineQuantityModels, ean -> createItem(ean,
                createItemAvailability(FULFILLMENT_CENTER_EAST, FULL_AVAILABILITY),
                createItemAvailability(FULFILLMENT_CENTER_WEST, ZERO_AVAILABILITY),
                createItemAvailability(SHIP_FROM_STORE_NL, ZERO_AVAILABILITY)));
        items.get(2).getItemAvailability().get(0).setAvailableQuantity(ZERO_AVAILABILITY);
        items.get(2).getItemAvailability().get(1).setAvailableQuantity(FULL_AVAILABILITY);

        itemAvailabilityResponse = new ItemAvailabilityResponse()
                .withCorrelationId(orderModel.getOrderId())
                .withItems(items);

        facts = getOrderRoutingRulesFacts(itemAvailabilityResponse, orderModel);

        // act
        rulesService.executeOrderRoutingRules(facts);

        // assert
        verify(orderPartsRoutedProducer, times(TWO_ORDER_PARTS)).produce(orderPartsRoutedArgumentCaptor.capture());
        verify(orderPartsCreatedProducer, times(TWO_ORDER_PARTS)).produce(orderPartsCreatedArgumentCaptor.capture());

        assertOrderPart(FULFILLMENT_CENTER_EAST, orderLineQuantityModels.get(0), orderLineQuantityModels.get(1));
        assertOrderPart(FULFILLMENT_CENTER_WEST, orderLineQuantityModels.get(2));
    }

    /**
     * Scenario: Order is split between warehouses, with overlapping availability.
     *
     * <h1>Given</h1>
     * <ol>
     *   <li>An order with 3 order lines</li>
     *   <li>Availability is in FCE and in FCW (having overlapping availability for eans)</li>
     * </ol>
     *
     * <h1>When</h1>
     * <ol>
     *   <li>ItemAvailabilityResponse is received</li>
     * </ol>
     *
     * <h1>Then</h1>
     * <ol>
     *   <li>OrderPartsRouted is created</li>
     *   <li>Split into FCE and FCW parts</li>
     *   <li>Warehouse is selected to with the most availability over another</li>
     *   <li>All order lines are present per order part</li>
     *   <li>Order only has two order parts</li>
     * </ol>
     *
     */
    @Test
    public void executeOrderRoutingRules_availabilityInTwoWarehousesWithOverlap_orderRoutedToWarehouseWithMostAvailability() {
        // arrange
        List<OrderLineQuantityModel> orderLineQuantityModels = orderModel.getOrderLineQuantities();

        itemAvailabilityResponse = new ItemAvailabilityResponse().withCorrelationId(orderModel.getOrderId());
        itemAvailabilityResponse.withItems(Arrays.asList(
                createItem(orderLineQuantityModels.get(0).getEan(),
                        createItemAvailability(FULFILLMENT_CENTER_EAST, 999),
                        createItemAvailability(FULFILLMENT_CENTER_WEST, 1000),
                        createItemAvailability(SHIP_FROM_STORE_NL, ZERO_AVAILABILITY)),
                createItem(orderLineQuantityModels.get(1).getEan(),
                        createItemAvailability(FULFILLMENT_CENTER_EAST, FULL_AVAILABILITY),
                        createItemAvailability(FULFILLMENT_CENTER_WEST, ZERO_AVAILABILITY),
                        createItemAvailability(SHIP_FROM_STORE_NL, ZERO_AVAILABILITY)),
                createItem(orderLineQuantityModels.get(2).getEan(),
                        createItemAvailability(FULFILLMENT_CENTER_EAST, ZERO_AVAILABILITY),
                        createItemAvailability(FULFILLMENT_CENTER_WEST, FULL_AVAILABILITY),
                        createItemAvailability(SHIP_FROM_STORE_NL, ZERO_AVAILABILITY))
        ));

        facts = getOrderRoutingRulesFacts(itemAvailabilityResponse, orderModel);

        // act
        rulesService.executeOrderRoutingRules(facts);

        // assert
        verify(orderPartsRoutedProducer, times(TWO_ORDER_PARTS)).produce(orderPartsRoutedArgumentCaptor.capture());
        verify(orderPartsCreatedProducer, times(TWO_ORDER_PARTS)).produce(orderPartsCreatedArgumentCaptor.capture());

        assertOrderPart(FULFILLMENT_CENTER_WEST, orderLineQuantityModels.get(0), orderLineQuantityModels.get(2));
        assertOrderPart(FULFILLMENT_CENTER_EAST, orderLineQuantityModels.get(1));
    }

    /**
     * Scenario: Order is split between warehouses, with insufficient and overlapping availability.
     *
     * <h1>Given</h1>
     * <ol>
     *   <li>An order with 3 order lines</li>
     *   <li>Availability is in FCE and in FCW (having overlapping availability for eans
     *   and not sufficient quantities)</li>
     * </ol>
     *
     * <h1>When</h1>
     * <ol>
     *   <li>ItemAvailabilityResponse is received</li>
     * </ol>
     *
     * <h1>Then</h1>
     * <ol>
     *   <li>OrderPartsRouted is created</li>
     *   <li>Split into FCE and FCW parts</li>
     *   <li>Warehouse is selected to with the most availability over another</li>
     *   <li>All order lines are present per order part</li>
     *   <li>Order only has two order parts</li>
     * </ol>
     *
     */
    @Test
    public void executeOrderRoutingRules_insufficientAvailabilityInTwoWarehousesWithOverlap_orderRoutedToWarehouseWithMostAvailability() {
        // arrange
        List<OrderLineQuantityModel> orderLineQuantityModels = orderModel.getOrderLineQuantities();

        itemAvailabilityResponse = new ItemAvailabilityResponse().withCorrelationId(orderModel.getOrderId());
        itemAvailabilityResponse.withItems(Arrays.asList(
                createItem(orderLineQuantityModels.get(0).getEan(),
                        createItemAvailability(FULFILLMENT_CENTER_EAST, 1),
                        createItemAvailability(FULFILLMENT_CENTER_WEST, 2),
                        createItemAvailability(SHIP_FROM_STORE_NL, ZERO_AVAILABILITY)),
                createItem(orderLineQuantityModels.get(1).getEan(),
                        createItemAvailability(FULFILLMENT_CENTER_EAST, 2),
                        createItemAvailability(FULFILLMENT_CENTER_WEST, 1),
                        createItemAvailability(SHIP_FROM_STORE_NL, ZERO_AVAILABILITY)),
                createItem(orderLineQuantityModels.get(2).getEan(),
                        createItemAvailability(FULFILLMENT_CENTER_EAST, ZERO_AVAILABILITY),
                        createItemAvailability(FULFILLMENT_CENTER_WEST, ZERO_AVAILABILITY),
                        createItemAvailability(SHIP_FROM_STORE_NL, ZERO_AVAILABILITY))
        ));

        facts = getOrderRoutingRulesFacts(itemAvailabilityResponse, orderModel);

        // act
        rulesService.executeOrderRoutingRules(facts);

        // assert
        verify(orderPartsRoutedProducer, times(TWO_ORDER_PARTS)).produce(orderPartsRoutedArgumentCaptor.capture());
        verify(orderPartsCreatedProducer, times(TWO_ORDER_PARTS)).produce(orderPartsCreatedArgumentCaptor.capture());

        assertOrderPart(FULFILLMENT_CENTER_WEST, orderLineQuantityModels.get(0), orderLineQuantityModels.get(2));
        assertOrderPart(FULFILLMENT_CENTER_EAST, orderLineQuantityModels.get(1));
    }
}
