package com.bestseller.services.orderrouting.flows.orderplacing;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderForFulfillment.FulfillmentAdvice;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsCreated.OrderPartsCreated;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsRouted.OrderPartsRouted;
import com.bestseller.services.orderrouting.utils.generator.OrderForFulfillmentGenerator;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import static com.bestseller.services.orderrouting.constants.FulfillmentNodeConstants.FULFILLMENT_CENTER_WEST;
import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;

@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(
    webEnvironment = SpringBootTest.WebEnvironment.MOCK,
    properties = "KAFKA_TOPIC_PREFIX=PredefinedFulfillmentNodeIntegrationTest")
public class PredefinedFulfillmentNodeIntegrationTest extends OrderPlacingBase {

    /**
     * Scenario: Order comes in with predefined fulfillment node, order routes to that node.
     *
     * <h1>Given</h1>
     * <ol>
     *   <li>An order with 5 order lines</li>
     *   <li>fulfillmentNode on the order is set to FULFILLMENT_CENTER_WEST</li>
     * </ol>
     *
     * <h1>When</h1>
     * <ol>
     *   <li>OrderForFulfillment is received</li>
     *   <li>ItemAvailabilityResponse is received</li>
     * </ol>
     *
     * <h1>Then</h1>
     * <ol>
     *   <li>Order should be routed to FULFILLMENT_CENTER_WEST</li>
     * </ol>
     */
    @Test
    public void executeOrderPlacingRules_orderWithPredefinedFulfillmentNode_routesToFulfillmentNode() {
        // arrange
        orderForFulfillment = OrderForFulfillmentGenerator.createFullOrderForFulfillmentMessage();
        orderForFulfillment.setOrderLines(OrderForFulfillmentGenerator.createOrderLines(5));
        orderForFulfillment.setFulfillmentAdvice(new FulfillmentAdvice()
                .withFulfillmentNode(FULFILLMENT_CENTER_WEST)
                .withHoldFromRouting(false));
        convertAndSaveToOrderModel(orderForFulfillment);

        facts = getPlacingRulesFacts(orderForFulfillment, orderModel);

        // act
        rulesService.executeOrderPlacingRules(facts);

        // assert
        verify(itemAvailabilityRequestProducer, never()).produce(any());
        verify(orderPartsRoutedProducer).produce(orderPartsRoutedArgumentCaptor.capture());
        verify(orderPartsCreatedProducer).produce(orderPartsCreatedArgumentCaptor.capture());

        OrderPartsRouted orderPartsRouted = orderPartsRoutedArgumentCaptor.getValue();
        OrderPartsCreated orderPartsCreated = orderPartsCreatedArgumentCaptor.getValue();
        assertThat("Warehouse differ", orderPartsCreated.getFulfillmentNode(), equalTo(orderPartsRouted.getFulfillmentNode()));
        assertThat("Routed despite advice", orderPartsRouted.getFulfillmentNode(), equalTo(FULFILLMENT_CENTER_WEST));
    }
}

