package com.bestseller.services.orderrouting.flows.orderplacing;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailabilityRequest;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderForFulfillment.OrderForFulfillment;
import com.bestseller.services.orderrouting.converter.OrderConverter;
import com.bestseller.services.orderrouting.flows.FlowRuleBase;
import com.bestseller.services.orderrouting.model.OrderModel;
import org.jeasy.rules.api.Facts;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;

import static com.bestseller.services.orderrouting.rules.facts.FactNames.ORDER_FOR_FULFILLMENT_FACT;
import static com.bestseller.services.orderrouting.rules.facts.FactNames.ORDER_MODEL_FACT;

public class OrderPlacingBase extends FlowRuleBase {

    @Captor
    protected ArgumentCaptor<ItemAvailabilityRequest> itemAvailabilityArgumentCaptor;

    protected Facts getPlacingRulesFacts(OrderForFulfillment orderForFulfillment, OrderModel orderModel) {
        final Facts facts = new Facts();
        facts.put(ORDER_MODEL_FACT, orderModel);
        facts.put(ORDER_FOR_FULFILLMENT_FACT, orderForFulfillment);

        return facts;
    }

    protected void convertAndSaveToOrderModel(OrderForFulfillment orderForFulfillment) {
        orderConverter = new OrderConverter();
        orderModel = orderConverter.convert(orderForFulfillment);

        orderService.saveOrderModel(orderModel);
    }

}
