package com.bestseller.services.orderrouting.flows.orderpartrejecting;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartRejected.OrderPartRejected;
import com.bestseller.services.orderrouting.flows.FlowRuleBase;
import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.repository.OrderLineQuantityModelRepository;
import com.bestseller.services.orderrouting.rules.facts.FactNames;
import com.bestseller.services.orderrouting.rules.orderpartrejecting.actions.RerouteOrderPartAction;
import com.bestseller.services.orderrouting.service.OrderService;
import com.bestseller.services.orderrouting.service.RulesService;
import com.bestseller.services.orderrouting.utils.generator.OrderModelGenerator;
import com.bestseller.services.orderrouting.utils.generator.OrderPartRejectedGenerator;
import org.jeasy.rules.api.Facts;
import org.junit.Before;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.List;
import java.util.stream.Collectors;

public abstract class RejectionBase extends FlowRuleBase {
    protected static final int SFS_EAN_LINE = 2;
    protected static final String SFS_WAREHOUSE = "SHIP_FROM_STORE_NL";

    @Captor
    protected ArgumentCaptor<List<OrderLineQuantityModel>> orderLinesQuantityModelCaptor;

    @Autowired
    protected RulesService rulesService;

    @Autowired
    protected OrderService orderService;

    @Autowired
    protected RerouteOrderPartAction rerouteOrderPartAction;

    protected OrderLineQuantityModelRepository spiedOrderLineQuantityModelRepository;

    protected OrderPartRejected orderPartRejected;

    @Before
    public void setUp() {
        // JPA Repositories can't be instantiated as @SpyBean, we need to create them and set into the service
        spiedOrderLineQuantityModelRepository = Mockito.spy(orderLineQuantityModelRepository);
        ReflectionTestUtils.setField(orderService, "orderLineQuantityModelRepository",
                                     spiedOrderLineQuantityModelRepository);
    }

    protected void arrangeBaseRejection(String countryCode) {
        arrangeBaseRejection(countryCode, OrderModelGenerator.OrderProperties.CHECKOUT);
    }

    protected void arrangeBaseRejection(String countryCode, String checkout) {
        orderModel = OrderModelGenerator.createTestOrderModel();
        orderModel.setCheckout(checkout);
        orderModel.getShippingAddress().setCountry(countryCode);
        orderModel.setOrderLineQuantities(OrderModelGenerator.createThreeTestOrderLines(orderModel));

        orderService.saveOrderModel(orderModel);

        orderPartRejected =
                OrderPartRejectedGenerator.createFullOrderPartRejectedMessage(orderModel.getOrderId(),
                                                                              SFS_WAREHOUSE,
                                                                              orderModel.getOrderLineQuantities()
                                                                                        .stream()
                                                                                        .map(OrderLineQuantityModel::getEan)
                                                                                        .collect(Collectors.toList()));

        facts = new Facts();
        facts.put(FactNames.ORDER_MODEL_FACT, orderModel);
        facts.put(FactNames.ORDER_PART_REJECTED_FACT, orderPartRejected);
    }
}
