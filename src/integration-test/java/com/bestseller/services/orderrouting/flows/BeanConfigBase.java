package com.bestseller.services.orderrouting.flows;

import com.bestseller.services.orderrouting.messaging.producer.ItemAvailabilityRequestProducer;
import com.bestseller.services.orderrouting.messaging.producer.OrderPartsCreatedProducer;
import com.bestseller.services.orderrouting.messaging.producer.OrderPartsRoutedProducer;
import com.bestseller.services.orderrouting.repository.OrderLineQuantityModelRepository;
import org.springframework.boot.test.mock.mockito.SpyBean;

public abstract class BeanConfigBase {
    @SpyBean
    protected OrderPartsRoutedProducer orderPartsRoutedProducer;

    @SpyBean
    protected OrderPartsCreatedProducer orderPartsCreatedProducer;

    @SpyBean
    protected ItemAvailabilityRequestProducer itemAvailabilityRequestProducer;

    @SpyBean
    protected OrderLineQuantityModelRepository orderLineQuantityModelRepository;
}
