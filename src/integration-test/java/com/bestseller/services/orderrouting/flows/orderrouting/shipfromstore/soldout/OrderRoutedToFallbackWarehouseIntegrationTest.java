package com.bestseller.services.orderrouting.flows.orderrouting.shipfromstore.soldout;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.Item;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailabilityResponse;
import com.bestseller.services.orderrouting.constants.ShippingConstants;
import com.bestseller.services.orderrouting.flows.orderrouting.RoutingBase;
import com.bestseller.services.orderrouting.strategy.StockPerNodeFulfillment;
import com.bestseller.services.orderrouting.utils.generator.OrderModelGenerator;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

import static com.bestseller.services.orderrouting.constants.FulfillmentNodeConstants.FULFILLMENT_CENTER_WEST;
import static com.bestseller.services.orderrouting.constants.FulfillmentNodeConstants.FULFILLMENT_CENTER_EAST;
import static com.bestseller.services.orderrouting.constants.FulfillmentNodeConstants.SHIP_FROM_STORE_NL;
import static com.bestseller.services.orderrouting.utils.generator.ItemAvailabilityGenerator.FULL_AVAILABILITY;
import static com.bestseller.services.orderrouting.utils.generator.ItemAvailabilityGenerator.ZERO_AVAILABILITY;
import static com.bestseller.services.orderrouting.utils.generator.ItemAvailabilityGenerator.createItem;
import static com.bestseller.services.orderrouting.utils.generator.ItemAvailabilityGenerator.createItemAvailability;
import static org.mockito.Mockito.verify;

@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(
    webEnvironment = SpringBootTest.WebEnvironment.MOCK,
    properties = "KAFKA_TOPIC_PREFIX=OrderRoutedToFallbackWarehouseIntegrationTest")
public class OrderRoutedToFallbackWarehouseIntegrationTest extends RoutingBase {

    @Autowired
    private StockPerNodeFulfillment shipFromStoreWarehousePriorityFulfillment;

    /**
     * Scenario: Not standard shipping method order with only SFS availability routes to Fallback warehouse.
     *
     * <h1>Given</h1>
     * <ol>
     *   <li>An order with 3 order lines and not standard shipping method</li>
     *   <li>Items are not available in any warehouse</li>
     * </ol>
     *
     * <h1>When</h1>
     * <ol>
     *   <li>ItemAvailabilityResponse is received</li>
     * </ol>
     *
     * <h1>Then</h1>
     * <ol>
     *   <li>OrderPartsRouted is created</li>
     *   <li>All order lines are routed to FALLBACK warehouse</li>
     *   <li>All order lines are present per order part</li>
     * </ol>
     */
    @Test
    public void executeOrderRoutingRules_givenSameAvailabilityInWarehouseAndSfs_orderRoutedToWarehouseWithQuantity() {
        // arrange
        // arrange order model
        orderModel = OrderModelGenerator.createTestOrderModel();
        orderModel.setOrderLineQuantities(OrderModelGenerator.createThreeTestOrderLines(orderModel));
        orderModel.setShippingMethod(ShippingConstants.EXPRESS);
        orderService.saveOrderModel(orderModel);

        // arrange ItemAvailabilityResponse
        List<Item> items = generateAvailabilityItems(orderModel.getOrderLineQuantities(), ean -> createItem(ean,
                createItemAvailability(FULFILLMENT_CENTER_EAST, ZERO_AVAILABILITY),
                createItemAvailability(FULFILLMENT_CENTER_WEST, ZERO_AVAILABILITY),
                createItemAvailability(SHIP_FROM_STORE_NL, FULL_AVAILABILITY)));

        itemAvailabilityResponse = new ItemAvailabilityResponse()
                .withCorrelationId(orderModel.getOrderId())
                .withItems(items);

        facts = getOrderRoutingRulesFacts(itemAvailabilityResponse, orderModel);

        // act
        rulesService.executeOrderRoutingRules(facts);

        // assert
        verify(orderPartsRoutedProducer).produce(orderPartsRoutedArgumentCaptor.capture());
        verify(orderPartsCreatedProducer).produce(orderPartsCreatedArgumentCaptor.capture());

        assertOrderPart(FULFILLMENT_CENTER_EAST, orderModel.getOrderLineQuantities());
    }

}
