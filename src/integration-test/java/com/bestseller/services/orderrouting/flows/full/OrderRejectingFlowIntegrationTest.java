package com.bestseller.services.orderrouting.flows.full;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderForFulfillment.OrderForFulfillment;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsRouted.OrderPartsRouted;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartRejected.OrderPartRejected;
import com.bestseller.services.orderrouting.utils.CacheSupervisor;
import com.bestseller.services.orderrouting.utils.TestConsumer;
import com.bestseller.services.orderrouting.utils.generator.OrderPartRejectedGenerator;
import org.awaitility.core.ConditionTimeoutException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.Duration;
import java.util.List;
import java.util.stream.Collectors;

import static com.bestseller.services.orderrouting.constants.CountryConstants.NORWAY;
import static com.bestseller.services.orderrouting.constants.FulfillmentNodeConstants.SHIP_FROM_STORE_NL;
import static com.bestseller.services.orderrouting.constants.FulfillmentNodeConstants.SHIP_FROM_STORE_NO;
import static com.bestseller.services.orderrouting.utils.EanUtils.eans;
import static org.awaitility.Awaitility.await;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.allOf;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.hasProperty;
import static org.junit.jupiter.api.Assertions.assertThrows;

@RunWith(SpringRunner.class)
@SpringBootTest(
    webEnvironment = SpringBootTest.WebEnvironment.MOCK,
    properties = "KAFKA_TOPIC_PREFIX=OrderRejectingFlowIntegrationTest")
@ActiveProfiles("dev")
public class OrderRejectingFlowIntegrationTest extends OrderFullFlowBase {
    private static final String JACK_JONES = "jack-jones";

    @Value("${warehouse.fulfillment-center-east}")
    private String fulfillmentCenterEast;

    @Value("${spring.cloud.stream.bindings.orderPartRejected.destination}")
    private String orderPartRejectedTopic;

    private TestConsumer<OrderPartsRouted> consumer;

    @Before
    public void setUp() {
        cacheSupervisor = new CacheSupervisor(orderModelRepository, orderLineQuantityModelRepository);
        consumer = new TestConsumer<>(OrderPartsRouted.class, orderPartsRoutedTopic);
    }

    /**
     * Scenario: Split order Warehouse/SFS, no rerouting takes place after cancellation of SFS Part.
     *
     * <h1>Given</h1>
     * <ol>
     *   <li>An order with 5 order lines</li>
     *   <li>3 items available in FCE</li>
     *   <li>2 items available in SFS_NL</li>
     * </ol>
     *
     * <h1>When</h1>
     * <ol>
     *   <li>OrderForFulfillment is received</li>
     *   <li>ItemAvailabilityResponse is received</li>
     *   <li>2 OrderPartsRouted are generated</li>
     *   <li>OrderPartRejected is received for SFS part</li>
     * </ol>
     *
     * <h1>Then</h1>
     * <ol>
     *   <li>SFS Order part is not rerouted to default warehouse</li>
     * </ol>
     *
     * @throws Exception exception
     */
    @Test
    public void orderPartsRouted_givenOrderWithOneWarehouse_producesOneMessage() throws Exception {
        // arrange
        OrderForFulfillment orderForFulfillment = createOrderForFulfillmentAndItemAvailabilityResponse(3, 2);

        // act
        var messages = consumer.consumeFromHere();
        publishOrderForFulfillmentAndItemAvailabilityResponse(orderForFulfillment);

        OrderPartsRouted sfsPart = await("part routed to SFS NL").until(messages::next, message ->
                orderForFulfillment.getOrderId().equals(message.getOrderId())
                && SHIP_FROM_STORE_NL.equals(message.getFulfillmentNode()));

        OrderPartRejected orderPartRejected = createOrderPartRejected(sfsPart.getOrderId(), sfsPart.getOrderLines(), SHIP_FROM_STORE_NL);

        messageProducer.publishMessage(orderPartRejectedTopic, orderPartRejected);

        // assert
        assertThrows(ConditionTimeoutException.class, () ->
            await("extra message")
                    .atMost(Duration.ofSeconds(10))
                    .until(messages::next, message ->
                            orderForFulfillment.getOrderId().equals(message.getOrderId())
                            && sfsPart.getOrderPartNumber().equals(message.getOrderPartNumber())
                            && fulfillmentCenterEast.equals(message.getFulfillmentNode())));
    }

    /**
     * Scenario: Order with only SFS part, rerouting to predefined warehouse after receiving rejection.
     *
     * <h1>Given</h1>
     * <ol>
     *   <li>An order with 3 order lines</li>
     *   <li>Items available only in SFS</li>
     * </ol>
     *
     * <h1>When</h1>
     * <ol>
     *   <li>OrderForFulfillment is received</li>
     *   <li>ItemAvailabilityResponse is received</li>
     *   <li>OrderPartsRouted is generated</li>
     *   <li>OrderPartRejected is received</li>
     * </ol>
     *
     * <h1>Then</h1>
     * <ol>
     *   <li>Order is rerouted to default warehouse with all items on the original OrderForFulfillment</li>
     * </ol>
     *
     * @throws Exception exception
     */
    @Test
    public void orderPartRejected_givenOrderWithSfsWarehouse_rerouteToDefaultWarehouse() throws Exception {
        // arrange
        OrderForFulfillment orderForFulfillment = createOrderForFulfillmentAndItemAvailabilityResponse(0, 3);
        orderForFulfillment.getOrderDetails().setShippingMethod("STANDARD");
        orderForFulfillment.getShippingInformation().getShippingAddress().setCountry(NORWAY);
        orderForFulfillment.getOrderLines().forEach(ol -> ol.setBrand(JACK_JONES));

        // act
        var messages = consumer.consumeFromHere();
        publishOrderForFulfillmentAndItemAvailabilityResponse(orderForFulfillment);

        await("part routed to SFS NO").until(messages::next, message ->
                orderForFulfillment.getOrderId().equals(message.getOrderId())
                && SHIP_FROM_STORE_NO.equals(message.getFulfillmentNode()));

        OrderPartRejected orderPartRejected = createOrderPartRejected(orderForFulfillment.getOrderId(),
                                                                      orderForFulfillment.getOrderLines(), SHIP_FROM_STORE_NO);

        messageProducer.publishMessage(orderPartRejectedTopic, orderPartRejected);

        // assert
        OrderPartsRouted orderPartsRouted = await("part routed to FCE")
                .until(messages::next, allOf(
                        hasProperty("orderId", equalTo(orderForFulfillment.getOrderId())),
                        hasProperty("fulfillmentNode", equalTo(fulfillmentCenterEast))));

        assertThat("Wrong EANs", eans(orderPartsRouted), equalTo(eans(orderForFulfillment)));
    }

    /**
     * Scenario: Split order to multiple order parts to SFS, no rerouting takes place after rejection of SFS Part.
     *
     * <h1>Given</h1>
     * <ol>
     *   <li>A (big) order with 11 order lines</li>
     *   <li>All items available only in SFS</li>
     * </ol>
     *
     * <h1>When</h1>
     * <ol>
     *   <li>OrderForFulfillment is received</li>
     *   <li>ItemAvailabilityResponse is received</li>
     *   <li>2 OrderPartsRouted are generated</li>
     *   <li>OrderPartRejected is received for one part</li>
     * </ol>
     *
     * <h1>Then</h1>
     * <ol>
     *   <li>Order part is not rerouted to default warehouse</li>
     * </ol>
     *
     * @throws Exception exception
     */
    @Test
    public void orderPartRejected_givenSplitBigSfsOrder_orderNotRerouted() throws Exception {
        // arrange
        OrderForFulfillment orderForFulfillment = createOrderForFulfillmentAndItemAvailabilityResponse(0, 11);

        // act
        var messages = consumer.consumeFromHere();
        publishOrderForFulfillmentAndItemAvailabilityResponse(orderForFulfillment);

        OrderPartsRouted partToCancel = await("part routed to SFS NL")
                .until(messages::next, message ->
                        orderForFulfillment.getOrderId().equals(message.getOrderId())
                        && SHIP_FROM_STORE_NL.equals(message.getFulfillmentNode()));

        OrderPartRejected orderPartRejected = createOrderPartRejected(partToCancel.getOrderId(), partToCancel.getOrderLines(), SHIP_FROM_STORE_NL);

        messageProducer.publishMessage(orderPartRejectedTopic, orderPartRejected);

        // assert
        assertThrows(ConditionTimeoutException.class, () ->
                await("extra message")
                        .atMost(Duration.ofSeconds(10))
                        .until(messages::next, message ->
                                orderForFulfillment.getOrderId().equals(message.getOrderId())
                                && partToCancel.getOrderPartNumber().equals(message.getOrderPartNumber())
                                && fulfillmentCenterEast.equals(message.getFulfillmentNode())));
    }

    private OrderPartRejected createOrderPartRejected(String orderId, List<OrderLine> orderLines, String warehouse) {
        List<String> eans = orderLines.stream()
                .map(OrderLine::getEan)
                .collect(Collectors.toList());

        return OrderPartRejectedGenerator.createFullOrderPartRejectedMessage(orderId, warehouse, eans);
    }
}
