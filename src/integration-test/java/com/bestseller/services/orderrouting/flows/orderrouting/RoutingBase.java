package com.bestseller.services.orderrouting.flows.orderrouting;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.Item;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailabilityResponse;
import com.bestseller.services.orderrouting.flows.FlowRuleBase;
import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.model.OrderModel;
import org.jeasy.rules.api.Facts;
import org.springframework.test.annotation.DirtiesContext;

import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.bestseller.services.orderrouting.rules.facts.FactNames.ITEM_AVAILABILITY_RESPONSE_FACT;
import static com.bestseller.services.orderrouting.rules.facts.FactNames.ORDER_MODEL_FACT;

@DirtiesContext(classMode = DirtiesContext.ClassMode.BEFORE_CLASS)
public abstract class RoutingBase extends FlowRuleBase {
    protected static final int ONE_ORDER_PART = 1;
    protected static final int TWO_ORDER_PARTS = 2;
    protected static final int THREE_ORDER_PARTS = 3;

    protected Facts getOrderRoutingRulesFacts(ItemAvailabilityResponse itemAvailabilityResponse, OrderModel orderModel) {
        final Facts facts = new Facts();
        facts.put(ORDER_MODEL_FACT, orderModel);
        facts.put(ITEM_AVAILABILITY_RESPONSE_FACT, itemAvailabilityResponse);

        return facts;
    }

    protected List<Item> generateAvailabilityItems(List<OrderLineQuantityModel> orderLineQuantityModels, Function<String, Item> mapper) {
        return orderLineQuantityModels.stream()
                                      .map(OrderLineQuantityModel::getEan)
                                      .map(mapper)
                                      .collect(Collectors.toList());
    }
}
