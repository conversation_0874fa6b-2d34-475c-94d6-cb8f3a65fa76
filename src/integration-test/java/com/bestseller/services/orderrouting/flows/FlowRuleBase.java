package com.bestseller.services.orderrouting.flows;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailabilityResponse;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderForFulfillment.OrderForFulfillment;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsCreated.OrderPartsCreated;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsRouted.OrderPartsRouted;
import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.model.OrderModel;
import com.bestseller.services.orderrouting.service.OrderService;
import com.bestseller.services.orderrouting.service.RulesService;
import org.jeasy.rules.api.Facts;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.convert.converter.Converter;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.bestseller.services.orderrouting.utils.EanUtils.eans;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.hasKey;
import static org.hamcrest.Matchers.hasSize;

public abstract class FlowRuleBase extends BeanConfigBase {

    @Captor
    protected ArgumentCaptor<OrderPartsRouted> orderPartsRoutedArgumentCaptor;

    @Captor
    protected ArgumentCaptor<OrderPartsCreated> orderPartsCreatedArgumentCaptor;

    @Autowired
    protected RulesService rulesService;

    @Autowired
    protected OrderService orderService;

    protected Converter<OrderForFulfillment, OrderModel> orderConverter;

    protected OrderForFulfillment orderForFulfillment;
    protected ItemAvailabilityResponse itemAvailabilityResponse;

    protected OrderModel orderModel;

    protected Facts facts;

    protected void assertOrderPart(String expectedWarehouse, List<OrderLineQuantityModel> orderLineQuantityModels) {
        // assert
        Map<String, List<OrderPartsRouted>> orderPartsRoutedFound = orderPartsRoutedArgumentCaptor.getAllValues()
                .stream()
                .collect(Collectors.groupingBy(OrderPartsRouted::getFulfillmentNode));
        Map<Object, List<OrderPartsCreated>> orderPartCreatedFound = orderPartsCreatedArgumentCaptor.getAllValues()
                .stream()
                .collect(Collectors.groupingBy(OrderPartsCreated::getFulfillmentNode));

        assertThat("Misrouted order part", orderPartsRoutedFound, hasKey(expectedWarehouse));
        assertThat("Mismatched messages", orderPartCreatedFound.keySet(), equalTo(orderPartsRoutedFound.keySet()));

        OrderPartsRouted orderPartsRouted = orderPartsRoutedFound.get(expectedWarehouse).get(0);
        OrderPartsCreated orderPartsCreated = orderPartCreatedFound.get(expectedWarehouse).get(0);
        assertThat("Wrong # of lines", orderPartsRouted.getOrderLines(), hasSize(orderLineQuantityModels.size()));

        assertThat(eans(orderPartsRouted), equalTo(eans(orderLineQuantityModels)));
        assertThat(eans(orderPartsCreated), equalTo(eans(orderLineQuantityModels)));
    }

    protected void assertOrderPart(String expectedWarehouse, OrderLineQuantityModel... orderLineQuantityModels) {
        assertOrderPart(expectedWarehouse, Arrays.asList(orderLineQuantityModels));
    }
}
