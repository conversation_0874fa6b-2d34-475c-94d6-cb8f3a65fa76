package com.bestseller.services.orderrouting.flows.orderrouting.normalrouting;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.Item;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailabilityResponse;
import com.bestseller.services.orderrouting.feature.toggles.ORSFeatures;
import com.bestseller.services.orderrouting.flows.orderrouting.RoutingBase;
import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.utils.generator.OrderModelGenerator;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

import static com.bestseller.services.orderrouting.constants.FulfillmentNodeConstants.FULFILLMENT_CENTER_EAST;
import static com.bestseller.services.orderrouting.constants.FulfillmentNodeConstants.FULFILLMENT_CENTER_WEST;
import static com.bestseller.services.orderrouting.constants.FulfillmentNodeConstants.SHIP_FROM_STORE_NO;
import static com.bestseller.services.orderrouting.utils.generator.ItemAvailabilityGenerator.FULL_AVAILABILITY;
import static com.bestseller.services.orderrouting.utils.generator.ItemAvailabilityGenerator.ZERO_AVAILABILITY;
import static com.bestseller.services.orderrouting.utils.generator.ItemAvailabilityGenerator.createItem;
import static com.bestseller.services.orderrouting.utils.generator.ItemAvailabilityGenerator.createItemAvailability;
import static org.mockito.Mockito.verify;

@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(
    webEnvironment = SpringBootTest.WebEnvironment.MOCK,
    properties = "KAFKA_TOPIC_PREFIX=InStoreOrderingSingleWarehouseIntegrationTest")
public class InStoreOrderingSingleWarehouseIntegrationTest extends RoutingBase {
    @Before
    public void setUp() {
        orderModel = OrderModelGenerator.createTestOrderModel();
        orderModel.setIsoStoreId("IsoStoreId");
        orderModel.getShippingAddress().setCountry("NO");
        orderModel.setOrderLineQuantities(OrderModelGenerator.createThreeTestOrderLines(orderModel));

        orderService.saveOrderModel(orderModel);
    }

    @After
    public void tearDown() {
        ORSFeatures.PRIORITIZE_FCW_OVER_FCE.setActive(false);
    }

    /**
     * Scenario: Order is available in one warehouse.
     *
     * <h1>Given</h1>
     * <ol>
     *   <li>An order with 3 order lines</li>
     *   <li>Availability is in FCE warehouse</li>
     * </ol>
     *
     * <h1>When</h1>
     * <ol>
     *   <li>ItemAvailabilityResponse is received</li>
     * </ol>
     *
     * <h1>Then</h1>
     * <ol>
     *   <li>OrderPartsRouted is created</li>
     *   <li>Order is routed to FCE</li>
     *   <li>All order lines are present in order</li>
     *   <li>Order only has one order part</li>
     * </ol>
     *
     */
    @Test
    public void executeOrderRoutingRules_availabilityOnlyInFce_orderRoutedToFce() {
        // arrange
        List<OrderLineQuantityModel> orderLineQuantityModels = orderModel.getOrderLineQuantities();

        List<Item> items = generateAvailabilityItems(orderLineQuantityModels, ean -> createItem(ean,
                createItemAvailability(FULFILLMENT_CENTER_EAST, FULL_AVAILABILITY),
                createItemAvailability(FULFILLMENT_CENTER_WEST, ZERO_AVAILABILITY),
                createItemAvailability(SHIP_FROM_STORE_NO, ZERO_AVAILABILITY)));

        itemAvailabilityResponse = new ItemAvailabilityResponse()
                .withCorrelationId(orderModel.getOrderId())
                .withItems(items);

        facts = getOrderRoutingRulesFacts(itemAvailabilityResponse, orderModel);

        // act
        rulesService.executeOrderRoutingRules(facts);

        // assert
        verify(orderPartsRoutedProducer).produce(orderPartsRoutedArgumentCaptor.capture());
        verify(orderPartsCreatedProducer).produce(orderPartsCreatedArgumentCaptor.capture());

        assertOrderPart(FULFILLMENT_CENTER_EAST, orderLineQuantityModels);
    }

    /**
     * Scenario: Order is available in one warehouse, but it is not the default warehouse.
     *
     * <h1>Given</h1>
     * <ol>
     *   <li>An order with 3 order lines</li>
     *   <li>Availability is not in FCE, only in Fulfillment Center West warehouse</li>
     * </ol>
     *
     * <h1>When</h1>
     * <ol>
     *   <li>ItemAvailabilityResponse is received</li>
     * </ol>
     *
     * <h1>Then</h1>
     * <ol>
     *   <li>OrderPartsRouted is created</li>
     *   <li>Order is routed to SECONDARY_FULFILLMENT_NODE</li>
     *   <li>All order lines are present in order</li>
     *   <li>Order only has one order part</li>
     * </ol>
     *
     */
    @Test
    public void executeOrderRoutingRules_availabilityNotInDefaultWarehouse_orderRoutedToSecondaryFulfillmentNode() {
        // arrange
        List<OrderLineQuantityModel> orderLineQuantityModels = orderModel.getOrderLineQuantities();

        List<Item> items = generateAvailabilityItems(orderLineQuantityModels, ean -> createItem(ean,
                createItemAvailability(FULFILLMENT_CENTER_EAST, ZERO_AVAILABILITY),
                createItemAvailability(FULFILLMENT_CENTER_WEST, FULL_AVAILABILITY),
                createItemAvailability(SHIP_FROM_STORE_NO, ZERO_AVAILABILITY)));

        itemAvailabilityResponse = new ItemAvailabilityResponse()
                .withCorrelationId(orderModel.getOrderId())
                .withItems(items);

        facts = getOrderRoutingRulesFacts(itemAvailabilityResponse, orderModel);

        // act
        rulesService.executeOrderRoutingRules(facts);

        // assert
        verify(orderPartsRoutedProducer).produce(orderPartsRoutedArgumentCaptor.capture());
        verify(orderPartsCreatedProducer).produce(orderPartsCreatedArgumentCaptor.capture());

        assertOrderPart(FULFILLMENT_CENTER_WEST, orderLineQuantityModels);
    }

    /**
     * Scenario: Order is available in both warehouses.
     *
     * <h1>Given</h1>
     * <ol>
     *   <li>An order with 3 order lines</li>
     *   <li>Availability in both FCE and FCW</li>
     * </ol>
     *
     * <h1>When</h1>
     * <ol>
     *   <li>ItemAvailabilityResponse is received</li>
     * </ol>
     *
     * <h1>Then</h1>
     * <ol>
     *   <li>OrderPartsRouted is created</li>
     *   <li>Order is routed to FULFILLMENT_CENTER_WEST</li>
     *   <li>All order lines are present in order</li>
     *   <li>Order only has one order part</li>
     * </ol>
     *
     */
    @Test
    public void executeOrderRoutingRules_availabilityBothWarehouses_orderRoutedToFulfillmentCenterWest() {
        // arrange
        ORSFeatures.PRIORITIZE_FCW_OVER_FCE.setActive(true);
        var orderLineQuantityModels = orderModel.getOrderLineQuantities();

        var items = generateAvailabilityItems(orderLineQuantityModels, ean -> createItem(ean,
                createItemAvailability(FULFILLMENT_CENTER_EAST, FULL_AVAILABILITY),
                createItemAvailability(FULFILLMENT_CENTER_WEST, FULL_AVAILABILITY),
                createItemAvailability(SHIP_FROM_STORE_NO, ZERO_AVAILABILITY)));

        itemAvailabilityResponse = new ItemAvailabilityResponse()
                .withCorrelationId(orderModel.getOrderId())
                .withItems(items);

        facts = getOrderRoutingRulesFacts(itemAvailabilityResponse, orderModel);

        // act
        rulesService.executeOrderRoutingRules(facts);

        // assert
        verify(orderPartsRoutedProducer).produce(orderPartsRoutedArgumentCaptor.capture());
        verify(orderPartsCreatedProducer).produce(orderPartsCreatedArgumentCaptor.capture());

        assertOrderPart(FULFILLMENT_CENTER_WEST, orderLineQuantityModels);
    }

    /**
     * Scenario: Order is available only in SFS warehouse
     *
     * <h1>Given</h1>
     * <ol>
     *   <li>An order with 3 order lines</li>
     *   <li>Availability only in SFS NO warehouse</li>
     * </ol>
     *
     * <h1>When</h1>
     * <ol>
     *   <li>ItemAvailabilityResponse is received</li>
     * </ol>
     *
     * <h1>Then</h1>
     * <ol>
     *   <li>OrderPartsRouted is created</li>
     *   <li>Order is routed to FULFILLMENT_CENTER_WEST</li>
     *   <li>All order lines are present in order</li>
     *   <li>Order only has one order part</li>
     * </ol>
     *
     */
    @Test
    public void executeOrderRoutingRules_availabilitySFS_orderRoutedToFulfillmentCenterWest() {
        // arrange
        ORSFeatures.PRIORITIZE_FCW_OVER_FCE.setActive(true);
        var orderLineQuantityModels = orderModel.getOrderLineQuantities();

        var items = generateAvailabilityItems(orderLineQuantityModels, ean -> createItem(ean,
            createItemAvailability(FULFILLMENT_CENTER_EAST, ZERO_AVAILABILITY),
            createItemAvailability(FULFILLMENT_CENTER_WEST, ZERO_AVAILABILITY),
            createItemAvailability(SHIP_FROM_STORE_NO, FULL_AVAILABILITY)));

        itemAvailabilityResponse = new ItemAvailabilityResponse()
            .withCorrelationId(orderModel.getOrderId())
            .withItems(items);

        facts = getOrderRoutingRulesFacts(itemAvailabilityResponse, orderModel);

        // act
        rulesService.executeOrderRoutingRules(facts);

        // assert
        verify(orderPartsRoutedProducer).produce(orderPartsRoutedArgumentCaptor.capture());
        verify(orderPartsCreatedProducer).produce(orderPartsCreatedArgumentCaptor.capture());

        assertOrderPart(FULFILLMENT_CENTER_WEST, orderLineQuantityModels);
    }
}
