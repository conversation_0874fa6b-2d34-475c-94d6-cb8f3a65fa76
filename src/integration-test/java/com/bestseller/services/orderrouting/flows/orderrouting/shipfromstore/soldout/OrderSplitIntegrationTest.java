package com.bestseller.services.orderrouting.flows.orderrouting.shipfromstore.soldout;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.Item;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailabilityResponse;
import com.bestseller.services.orderrouting.constants.ShippingConstants;
import com.bestseller.services.orderrouting.flows.orderrouting.RoutingBase;
import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.utils.generator.OrderModelGenerator;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

import static com.bestseller.services.orderrouting.constants.FulfillmentNodeConstants.FULFILLMENT_CENTER_EAST;
import static com.bestseller.services.orderrouting.constants.FulfillmentNodeConstants.FULFILLMENT_CENTER_WEST;
import static com.bestseller.services.orderrouting.constants.FulfillmentNodeConstants.SHIP_FROM_STORE_NL;
import static com.bestseller.services.orderrouting.utils.generator.ItemAvailabilityGenerator.FULL_AVAILABILITY;
import static com.bestseller.services.orderrouting.utils.generator.ItemAvailabilityGenerator.ZERO_AVAILABILITY;
import static com.bestseller.services.orderrouting.utils.generator.ItemAvailabilityGenerator.createItem;
import static com.bestseller.services.orderrouting.utils.generator.ItemAvailabilityGenerator.createItemAvailability;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;


@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(
    webEnvironment = SpringBootTest.WebEnvironment.MOCK,
    properties = "KAFKA_TOPIC_PREFIX=OrderSplitIntegrationTest")
public class OrderSplitIntegrationTest extends RoutingBase {

    /**
     * Scenario: Order lines are only available in two different warehouses and SFS.
     *
     * <h1>Given</h1>
     * <ol>
     *   <li>An order with 3 order lines from different brands</li>
     *   <li>Some order lines available in FCE, some in SFS NL</li>
     * </ol>
     *
     * <h1>When</h1>
     * <ol>
     *   <li>ItemAvailabilityResponse is received</li>
     * </ol>
     *
     * <h1>Then</h1>
     * <ol>
     *   <li>Two OrderPartsRouted are produced</li>
     *   <li>Two OrderPartsCreated are produced</li>
     *   <li>All order lines are routed</li>
     * </ol>
     */
    @Test
    public void executeOrderRoutingRules_givenMixedAvailability_splitOrderRouted() {
        // arrange
        orderModel = OrderModelGenerator.createTestOrderModel();
        orderModel.setCarrierVariant(ShippingConstants.PICKUP);
        orderModel.setOrderLineQuantities(OrderModelGenerator.createThreeTestOrderLines(orderModel));
        orderService.saveOrderModel(orderModel);

        List<OrderLineQuantityModel> orderLineQuantityModels = orderModel.getOrderLineQuantities();

        List<Item> items = generateAvailabilityItems(orderLineQuantityModels, ean -> createItem(ean,
                createItemAvailability(FULFILLMENT_CENTER_EAST, ZERO_AVAILABILITY),
                createItemAvailability(FULFILLMENT_CENTER_WEST, ZERO_AVAILABILITY),
                createItemAvailability(SHIP_FROM_STORE_NL, FULL_AVAILABILITY)));
        items.get(0).getItemAvailability().get(0).setAvailableQuantity(FULL_AVAILABILITY);
        items.get(0).getItemAvailability().get(1).setAvailableQuantity(ZERO_AVAILABILITY);

        itemAvailabilityResponse = new ItemAvailabilityResponse()
                .withCorrelationId(orderModel.getOrderId())
                .withItems(items);

        facts = getOrderRoutingRulesFacts(itemAvailabilityResponse, orderModel);

        // act
        rulesService.executeOrderRoutingRules(facts);

        // assert
        verify(orderPartsRoutedProducer, times(TWO_ORDER_PARTS)).produce(orderPartsRoutedArgumentCaptor.capture());
        verify(orderPartsCreatedProducer, times(TWO_ORDER_PARTS)).produce(orderPartsCreatedArgumentCaptor.capture());

        assertOrderPart(FULFILLMENT_CENTER_EAST, orderLineQuantityModels.get(0));
        assertOrderPart(SHIP_FROM_STORE_NL, orderLineQuantityModels.get(1), orderLineQuantityModels.get(2));

    }
}
