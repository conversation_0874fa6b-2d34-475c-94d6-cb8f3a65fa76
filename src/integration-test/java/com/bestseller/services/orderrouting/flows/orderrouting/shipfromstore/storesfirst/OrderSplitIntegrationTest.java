package com.bestseller.services.orderrouting.flows.orderrouting.shipfromstore.storesfirst;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.Item;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailabilityResponse;
import com.bestseller.services.orderrouting.flows.orderrouting.RoutingBase;
import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.utils.generator.OrderModelGenerator;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

import static com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailability.Type.STORE;
import static com.bestseller.services.orderrouting.constants.CountryConstants.NORWAY;
import static com.bestseller.services.orderrouting.constants.FulfillmentNodeConstants.FULFILLMENT_CENTER_EAST;
import static com.bestseller.services.orderrouting.constants.FulfillmentNodeConstants.FULFILLMENT_CENTER_WEST;
import static com.bestseller.services.orderrouting.constants.FulfillmentNodeConstants.SHIP_FROM_STORE_NO;
import static com.bestseller.services.orderrouting.utils.generator.ItemAvailabilityGenerator.FULL_AVAILABILITY;
import static com.bestseller.services.orderrouting.utils.generator.ItemAvailabilityGenerator.ZERO_AVAILABILITY;
import static com.bestseller.services.orderrouting.utils.generator.ItemAvailabilityGenerator.createItem;
import static com.bestseller.services.orderrouting.utils.generator.ItemAvailabilityGenerator.createItemAvailability;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(
    webEnvironment = SpringBootTest.WebEnvironment.MOCK,
    properties = "KAFKA_TOPIC_PREFIX=OrderSplitIntegrationTest")
public class OrderSplitIntegrationTest extends RoutingBase {
    @Before
    public void setUp() {
        orderModel = OrderModelGenerator.createTestOrderModel();
        orderModel.getShippingAddress().setCountry(NORWAY);

        orderModel.setOrderLineQuantities(OrderModelGenerator.createThreeTestOrderLines(orderModel));
        orderModel.getOrderLineQuantities().forEach(orderLine -> orderLine.setBrand("jack-jones"));
        orderModel.getOrderLineQuantities().forEach(orderLine -> orderLine.setQuantity(10));

        orderModel = orderService.saveOrderModel(orderModel);
    }

    /**
     * Scenario: Order lines are available in Ship From Store but not enough quantity, order is sent to the warehouse.
     *
     * <h1>Given</h1>
     * <ol>
     *   <li>An order with 3 order lines</li>
     *   <li>Order goes to NO</li>
     *   <li>Single brand order: Jack & Jones</li>
     *   <li>Order quantity set to 10</li>
     *   <li>Availability in both FCE and SHIP_FROM_STORE_NO, but not enough quantity in SHIP_FROM_STORE_NO for all items</li>
     * </ol>
     *
     * <h1>When</h1>
     * <ol>
     *   <li>ItemAvailabilityResponse is received</li>
     * </ol>
     *
     * <h1>Then</h1>
     * <ol>
     *   <li>OrderPartsRouted is created</li>
     *   <li>Order lines are split between FCE and SHIP_FROM_STORE_NO</li>
     *   <li>All order lines are present per order part</li>
     * </ol>
     */
    @Test
    public void executeOrderRoutingRules_singleBrandOrder_orderRoutedToShipFromStore() {
        // arrange
        orderService.saveOrderModel(orderModel);

        List<OrderLineQuantityModel> orderLineQuantityModels = orderModel.getOrderLineQuantities();

        List<Item> items = generateAvailabilityItems(orderLineQuantityModels, ean -> createItem(ean,
                createItemAvailability(FULFILLMENT_CENTER_EAST, FULL_AVAILABILITY),
                createItemAvailability(FULFILLMENT_CENTER_WEST, ZERO_AVAILABILITY),
                createItemAvailability(SHIP_FROM_STORE_NO, FULL_AVAILABILITY, STORE, NORWAY)));
        items.get(0).getItemAvailability().get(2).setAvailableQuantity(9);

        itemAvailabilityResponse = new ItemAvailabilityResponse()
                .withCorrelationId(orderModel.getOrderId())
                .withItems(items);

        facts = getOrderRoutingRulesFacts(itemAvailabilityResponse, orderModel);

        // act
        rulesService.executeOrderRoutingRules(facts);

        // assert
        verify(orderPartsRoutedProducer, times(TWO_ORDER_PARTS)).produce(orderPartsRoutedArgumentCaptor.capture());
        verify(orderPartsCreatedProducer, times(TWO_ORDER_PARTS)).produce(orderPartsCreatedArgumentCaptor.capture());

        assertOrderPart(FULFILLMENT_CENTER_EAST, orderLineQuantityModels.get(0));
        assertOrderPart(SHIP_FROM_STORE_NO, orderLineQuantityModels.get(1), orderLineQuantityModels.get(2));
    }
}
