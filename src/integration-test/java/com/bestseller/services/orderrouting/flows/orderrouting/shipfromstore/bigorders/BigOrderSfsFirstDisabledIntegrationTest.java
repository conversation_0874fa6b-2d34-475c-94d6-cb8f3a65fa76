package com.bestseller.services.orderrouting.flows.orderrouting.shipfromstore.bigorders;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.Item;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inventory.ItemAvailabilityResponse;
import com.bestseller.services.orderrouting.constants.CountryConstants;
import com.bestseller.services.orderrouting.feature.toggles.AuroraStateRepository;
import com.bestseller.services.orderrouting.feature.toggles.ORSFeatures;
import com.bestseller.services.orderrouting.flows.orderrouting.RoutingBase;
import com.bestseller.services.orderrouting.model.OrderLineQuantityModel;
import com.bestseller.services.orderrouting.utils.generator.OrderModelGenerator;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.togglz.core.repository.FeatureState;

import java.util.List;

import static com.bestseller.services.orderrouting.constants.FulfillmentNodeConstants.FULFILLMENT_CENTER_EAST;
import static com.bestseller.services.orderrouting.constants.FulfillmentNodeConstants.FULFILLMENT_CENTER_WEST;
import static com.bestseller.services.orderrouting.constants.FulfillmentNodeConstants.SHIP_FROM_STORE_NL;
import static com.bestseller.services.orderrouting.utils.generator.ItemAvailabilityGenerator.FULL_AVAILABILITY;
import static com.bestseller.services.orderrouting.utils.generator.ItemAvailabilityGenerator.ZERO_AVAILABILITY;
import static com.bestseller.services.orderrouting.utils.generator.ItemAvailabilityGenerator.createItem;
import static com.bestseller.services.orderrouting.utils.generator.ItemAvailabilityGenerator.createItemAvailability;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@SpringBootTest(
    webEnvironment = SpringBootTest.WebEnvironment.MOCK,
    properties = "KAFKA_TOPIC_PREFIX=BigOrderSfsFirstDisabledIntegrationTest")
public class BigOrderSfsFirstDisabledIntegrationTest extends RoutingBase {

    @Autowired
    private AuroraStateRepository stateRepository;

    @Before
    public void setUp() {
        orderModel = OrderModelGenerator.createTestOrderModel();

        orderModel.setOrderLineQuantities(OrderModelGenerator.createNOrderLines(orderModel.getOrderId(), 14));
        orderModel.getOrderLineQuantities().forEach(orderLine -> orderLine.setBrand("jack-jones"));
        orderModel.getShippingAddress().setCountry(CountryConstants.NORWAY);

        orderService.saveOrderModel(orderModel);

        stateRepository.setFeatureState(new FeatureState(ORSFeatures.SFS_STORE_FIRST_BIG_ORDERS_ENABLED, false));
    }

    /**
     * Scenario: A SFS First country order with more 14 items has both availability in NL and FCE.
     *
     * <h1>Given</h1>
     * <ol>
     *   <li>An order with 14 order lines</li>
     *   <li>Order goes to NO</li>
     *   <li>Availability is spread: some availability in FCE, some of items have availability in SFS NL</li>
     * </ol>
     *
     * <h1>When</h1>
     * <ol>
     *   <li>ItemAvailabilityResponse is received</li>
     * </ol>
     *
     * <h1>Then</h1>
     * <ol>
     *   <li>OrderPartsRouted is created</li>
     *   <li>Order is not split because is SFS Store First country and Big Order Rule feature toggle is disabled</li>
     *   <li>All order lines are routed to FCE</li>
     *   <li>All order lines are present in one order part</li>
     * </ol>
     */
    @Test
    public void executeOrderRoutingRules_availabilitySpreadBetweenWarehouses_orderNotSplit() {
        // arrange
        List<OrderLineQuantityModel> availabilityInSfsWarehouse = orderModel.getOrderLineQuantities().subList(0, 3);
        List<OrderLineQuantityModel> availabilityInFce = orderModel.getOrderLineQuantities().subList(3, 14);

        List<Item> availability = generateAvailabilityItems(availabilityInFce, ean -> createItem(ean,
                createItemAvailability(FULFILLMENT_CENTER_EAST, FULL_AVAILABILITY),
                createItemAvailability(FULFILLMENT_CENTER_WEST, ZERO_AVAILABILITY),
                createItemAvailability(SHIP_FROM_STORE_NL, ZERO_AVAILABILITY)));

        // three items are only in the SHIP_FROM_STORE_NL
        List<Item> shipFromStoreAvailability = generateAvailabilityItems(availabilityInSfsWarehouse, ean -> createItem(ean,
                createItemAvailability(FULFILLMENT_CENTER_EAST, ZERO_AVAILABILITY),
                createItemAvailability(FULFILLMENT_CENTER_WEST, ZERO_AVAILABILITY),
                createItemAvailability(SHIP_FROM_STORE_NL, FULL_AVAILABILITY)));

        availability.addAll(shipFromStoreAvailability);

        itemAvailabilityResponse = new ItemAvailabilityResponse()
                .withCorrelationId(orderModel.getOrderId())
                .withItems(availability);

        facts = getOrderRoutingRulesFacts(itemAvailabilityResponse, orderModel);

        // act
        rulesService.executeOrderRoutingRules(facts);

        // assert
        verify(orderPartsRoutedProducer, times(ONE_ORDER_PART)).produce(orderPartsRoutedArgumentCaptor.capture());
        verify(orderPartsCreatedProducer, times(ONE_ORDER_PART)).produce(orderPartsCreatedArgumentCaptor.capture());

        assertOrderPart(FULFILLMENT_CENTER_EAST, orderModel.getOrderLineQuantities());
    }

    /**
     * Scenario: A SFS Sold out country order with more 14 items has both availability in NL and FCE.
     *
     * <h1>Given</h1>
     * <ol>
     *   <li>An order with 14 order lines</li>
     *   <li>Order goes to NL</li>
     *   <li>Availability is spread: some availability in FCE, some of items have availability in SFS NL</li>
     * </ol>
     *
     * <h1>When</h1>
     * <ol>
     *   <li>ItemAvailabilityResponse is received</li>
     * </ol>
     *
     * <h1>Then</h1>
     * <ol>
     *   <li>OrderPartsRouted is created</li>
     *   <li>Order is split because is SFS Store First country feature toggle should not affect Sold out countries</li>
     *   <li>Some order lines are routed to FCE, some to SHIP_FROM_STORE_NL</li>
     *   <li>All order lines are present per order part</li>
     * </ol>
     */
    @Test
    public void executeOrderRoutingRules_availabilitySpreadBetweenWarehouses_orderSplit() {
        // arrange
        orderModel.getShippingAddress().setCountry(CountryConstants.NETHERLANDS);
        List<OrderLineQuantityModel> expectedOrderLinesQuantitiesInShipFromStore = orderModel.getOrderLineQuantities().subList(0, 3);
        List<OrderLineQuantityModel> expectedOrderLinesQuantitiesInFce = orderModel.getOrderLineQuantities().subList(3, 14);

        List<Item> availability = generateAvailabilityItems(expectedOrderLinesQuantitiesInFce, ean -> createItem(ean,
                                                                                                                         createItemAvailability(FULFILLMENT_CENTER_EAST, FULL_AVAILABILITY),
                                                                                                                         createItemAvailability(FULFILLMENT_CENTER_WEST, ZERO_AVAILABILITY),
                                                                                                                         createItemAvailability(SHIP_FROM_STORE_NL, ZERO_AVAILABILITY)));

        // three items are only in the SHIP_FROM_STORE_NL
        List<Item> shipFromStoreAvailability = generateAvailabilityItems(expectedOrderLinesQuantitiesInShipFromStore, ean -> createItem(ean,
                                                                                                                                        createItemAvailability(FULFILLMENT_CENTER_EAST, ZERO_AVAILABILITY),
                                                                                                                                        createItemAvailability(FULFILLMENT_CENTER_WEST, ZERO_AVAILABILITY),
                                                                                                                                        createItemAvailability(SHIP_FROM_STORE_NL, FULL_AVAILABILITY)));

        availability.addAll(shipFromStoreAvailability);

        itemAvailabilityResponse = new ItemAvailabilityResponse()
                .withCorrelationId(orderModel.getOrderId())
                .withItems(availability);

        facts = getOrderRoutingRulesFacts(itemAvailabilityResponse, orderModel);

        // act
        rulesService.executeOrderRoutingRules(facts);

        // assert
        verify(orderPartsRoutedProducer, times(TWO_ORDER_PARTS)).produce(orderPartsRoutedArgumentCaptor.capture());
        verify(orderPartsCreatedProducer, times(TWO_ORDER_PARTS)).produce(orderPartsCreatedArgumentCaptor.capture());

        assertOrderPart(FULFILLMENT_CENTER_EAST, expectedOrderLinesQuantitiesInFce);
        assertOrderPart(SHIP_FROM_STORE_NL, expectedOrderLinesQuantitiesInShipFromStore);
    }

}
