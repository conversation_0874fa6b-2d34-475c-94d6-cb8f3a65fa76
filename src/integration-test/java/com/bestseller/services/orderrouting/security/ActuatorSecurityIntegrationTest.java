package com.bestseller.services.orderrouting.security;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.web.context.WebApplicationContext;

import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.httpBasic;
import static org.springframework.security.test.web.servlet.setup.SecurityMockMvcConfigurers.springSecurity;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import static org.springframework.test.web.servlet.setup.MockMvcBuilders.webAppContextSetup;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("dev")
public class ActuatorSecurityIntegrationTest {
    private static final String TEST_INVALID_USER = "testInvalidUser";
    private static final String TEST_INVALID_PASS = "testInvalidPass";
    private static final String ACTUATOR_ENDPOINT = "/actuator/";
    private static final String ACTUATOR_HEALTH_ENDPOINT = "/actuator/health";

    @Value("${user.monitor.name}")
    private String monitorUser;

    @Value("${user.monitor.password}")
    private String monitorPass;

    @Autowired
    private WebApplicationContext wac;

    private MockMvc mockMvc;

    @Before
    public void setUp() {
        mockMvc = webAppContextSetup(wac).apply(springSecurity()).build();
    }

    /**
     * Scenario: Actuator health check is accessible without authentication, needed for ECS.
     *
     * <h1>Given</h1>
     * <ol>
     *   <li>A healthy running application</li>
     * </ol>
     *
     * <h1>When</h1>
     * <ol>
     *   <li>A request to the health actuator is made</li>
     * </ol>
     *
     * <h1>Then</h1>
     * <ol>
     *   <li>A 200 response is received</li>
     * </ol>
     */
    @Test
    public void pingActuatorActuatorEndpoint_withoutAuthentication_returnsOkHttpStatus() throws Exception {
        //act
        mockMvc.perform(get(ACTUATOR_HEALTH_ENDPOINT)).andExpect(status().isOk());
    }

    /**
     * Scenario: Any call to actuator with the right credentials has to be successful.
     *
     * <h1>Given</h1>
     * <ol>
     *   <li>A running application</li>
     * </ol>
     *
     * <h1>When</h1>
     * <ol>
     *   <li>A request to the actuator with valid credentials is sent</li>
     * </ol>
     *
     * <h1>Then</h1>
     * <ol>
     *   <li>A 200 response is received</li>
     * </ol>
     */
    @Test
    public void pingActuatorInfoEndpoint_validAuthentication_returnsOkHttpStatus() throws Exception {
        //act
        mockMvc.perform(get(ACTUATOR_ENDPOINT).with(httpBasic(monitorUser, monitorPass)))
               .andExpect(status().isOk());
    }

    /**
     * Scenario: Any call to actuator without the right credentials has to fail.
     *
     * <h1>Given</h1>
     * <ol>
     *   <li>A running application</li>
     * </ol>
     *
     * <h1>When</h1>
     * <ol>
     *   <li>A request to the actuator with invalid credentials is sent</li>
     * </ol>
     *
     * <h1>Then</h1>
     * <ol>
     *   <li>A 401 (unauthorized) message is received</li>
     * </ol>
     */
    @Test
    public void pingActuatorInfoEndpoint_invalidAuthentication_returnsUnauthorizedHttpStatus() throws Exception {
        //act
        mockMvc.perform(get(ACTUATOR_ENDPOINT).with(httpBasic(TEST_INVALID_USER, TEST_INVALID_PASS)))
               .andExpect(status().isUnauthorized());
    }
}
