This file contains guidelines that should be followed when making any
changes to the repository. All text before the first level 1 (L1) header
will be ignored by the Pull request guidelines add-on.

Please keep this document lean and point to Confluence for detailed explanations.

# Basics

## Do not use abbreviations

Instead of mainAddr, use mainAddress.

## Use proper English for code symbols and comments

Pay attention to typos, grammar, syntax, spelling, etc.

## All code must be unit tested

We strive for predictability. As a rule of thumb, you can see in the Pull
request the list of modified files. If a code file is modified but there is no
matching unit test, that PR is not good.

## Sensitive information should not be committed

Do not commit passwords, private keys, etc. Provide a way for the application to read them on each environment.

#Static Code Analysis
_Add additional information for checkstyle rules here_

# Unit Tests

## Use the Triple A (AAA)

- Add an ``// arrange`` comment for setting up a test
- Add an ``// act`` comment to run the actual test
- Add an ``// assert`` comment to mar the section of code where results are checked

## Make sure the tests can turn red

A test that is always green has no value. When developing, start with a red test.

## Test behavior, not implementation

Do not peek into the internals of a class, only the public API.

## Test expected functionality and default behavior

If you have more than one possible outcome, test at least two of them.

## Naming Conventions

Unit test methods follow the convention `methodName_condition_outcome`.
You can read it in more details in [Confluence](https://bestseller.jira.com/wiki/display/PORTAL/Test+Naming+Convention).

## Line endings

Unified line endings are enforced using a .gitattributes setting. Unix line endings (LF) are used due to smaller size.  

# Integration tests

## Use the dedicated folder

Add integration test classes to `src/integration-test/java` and resources to `src/integration-test/resources`

## Naming conventions

- Names of test classes must end with `IntegrationTest`
- Names of test methods follow the convention `condition_outcome`.

## Always use the cache supervisor when asserting Redis cache updates

Redis operations happen asynchronously and it will take time between saving/updating/deleting an entry
and this actual operation to be applied at the server. This means that when you update a cache entry and
immediately fetch it from the cache you may not get the latest change. To mitigate such situations it is
advisable to use a repeatable mechanism when you assert that an entry has been updated.
[Awaitility](https://github.com/awaitility/awaitility/wiki/Usage) provides the semantics to assert asynchronous
operations. Please follow these rules when you want to assert that Redis cache has been updated:

- Always use the provided methods in the CacheSupervisor class
- When you want to add a new assert, please implement it as a method in CacheSupervisor

## Modifications of the functionality

When you need to modify an existing service or component in order to mimic some behaviour you must annotate your test class with
[@DirtiesContext](https://docs.spring.io/spring/docs/current/javadoc-api/org/springframework/test/annotation/DirtiesContext.html)!
This is needed because you are modifying the current application logic and it would break other tests that will use the same
Spring context. DirtiesContext would instruct JUnit to create a separate Spring context for your test class.

Examples of modified functionality:

- When you change the behaviour of a component using MockBean
- When you observe the behaviour of a component using SpyBean

## Notes on consumers

Two classes are provided in order to send a message in a Kafka topic and to verify that a message has been sent to a Kafka topic:

- Use TestMessageProducer to send a message
- Use TestMessageProducer to verify that a message has been sent or not

# JIRA comment

Does the script have the ticket number mentioned in the first line as a comment?

# Logging and Exceptions

## Log on the correct level

- Is the code properly logged?
- Are the log levels (INFO, DEBUG, ERROR, TRACE ...) set correctly?

## Avoid string concatenation

Use the formatting overloads of the logger API instead of manually concatenating strings.

## Do not catch Exception

Catch specific exception types.

## Avoid NullPointerException

Use checks to avoid null pointer exception.

## Do not isolate exception classes in their own package

Custom exception classes should live side by side with the regular code. Package
structure should be domain driven, e.g. java.io and not java.io.exception.