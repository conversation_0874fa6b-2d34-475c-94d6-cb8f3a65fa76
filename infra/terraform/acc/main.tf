# Setup remote state
terraform {
  backend "remote" {
    hostname     = "tfe.mng.bseint.io"
    organization = "bestseller-ecom"

    workspaces {
      name = "ors-acc"
    }
  }
}

# Configure AWS provider
provider "aws" {
  region              = local.aws_region
  allowed_account_ids = [local.aws_account_id]

  assume_role {
    session_name = "terraform"
    role_arn     = "arn:aws:iam::${local.aws_account_id}:role/terraform-admin-role"
  }
}

# Define variables
locals {
  # Setup - DO NOT CHANGE!
  aws_account_id = "************" # BSE-AWS-ACC
  aws_region     = "eu-west-1"
  env            = "acc"

  # Project Information
  project_information = module.metadata.projects["ors"]["metadata"]
  project             = local.project_information["project"]
  project_name        = local.project_information["project_name"]
  owner               = local.project_information["owner"]
  vcs                 = local.project_information["github_vcs"]

  # Kafka properties
  consumer_group = "OrderRoutingService"

  default_tags = {
    env         = local.env
    project     = local.project
    owner       = local.owner
    Terraformed = "true"
    Ansible     = "false"
  }
  database_name = "${local.project}${local.env}"

  # Monitoring Datadog
  web_hook_P1                = "@webhook-EIDTS_P1"
  web_hook_P2                = "@webhook-EIDTS_P2"
  web_hook_P3                = "@webhook-EIDTS_P3"
  slack_notification_channel = "sfs-monitoring"

  # Datadog required environment variables
  datadog_env_vars = [
    {
      name  = "DD_JMXFETCH_ENABLED"
      value = "true"
    },
    {
      # DD APM tries to add a header to Kafka produced messages, disable while we are using old kafka version
      name  = "DD_KAFKA_CLIENT_PROPAGATION_ENABLED"
      value = "false"
    },
    {
      name  = "DD_SERVICE_NAME"
      value = local.project
    },
    {
      name  = "DD_ENABLED"
      value = "true"
    }
  ]

  # Application specific environment variables
  app_env_vars = [
    {
      name  = "JAVA_TOOL_OPTIONS"
      value = "-Xmx768m -Xms768m"
    },
    {
      name  = "LOG_LEVEL"
      value = "INFO"
    },
    {
      name  = "KAFKA_BROKER_LIST"
      value = join(",", module.ec2-kafka-data.kafka_full_names)
    },
    {
      name  = "KAFKA_BROKER_SECURITY_PROTOCOL"
      value = "PLAINTEXT"
    },
    {
      name  = "DELAY_INVOKE_ITEMAVAILABILITY_PRODUCER"
      value = "1500"
    },
    {
      name  = "ADMINISTRATOR_ENDPOINT_USER"
      value = "ors_rest_admin_acc"
    },
    {
      name  = "MONITOR_ENDPOINT_USER"
      value = "ors_rest_mon_acc"
    },
    {
      name  = "SPRING_PROFILES_ACTIVE"
      value = local.env
    },
    {
      name  = "CONSUMER_GROUP"
      value = local.consumer_group
    }
  ]

  app_env_secrets = [
    {
      name      = "ADMINISTRATOR_ENDPOINT_PASSWORD"
      valueFrom = module.secretsmanager-secret-administrator.secret_arn
    },
    {
      name      = "MONITOR_ENDPOINT_PASSWORD"
      valueFrom = module.secretsmanager-secret-monitoring.secret_arn
    },
    {
      name      = "DD_API_KEY"
      valueFrom = data.aws_secretsmanager_secret_version.datadog_api_key.arn
    },
    {
      name      = "DD_APPLICATION_KEY"
      valueFrom = module.secretsmanager-secret-dd-application-key.secret_arn
    }
  ]

  # Database specific variables
  db_env_secrets = [
    {
      name      = "FLYWAY_PASSWORD"
      valueFrom = module.rds_db.secret_version_arns["db_app_migrations_user"]
    },
    {
      name      = "SPRING_DATASOURCE_PASSWORD"
      valueFrom = module.rds_db.secret_version_arns["db_app_user"]
    }
  ]

  db_env_vars = [
    {
      name  = "FLYWAY_USERNAME"
      value = module.rds_db.db_usernames["db_app_migrations_user"]
    },
    {
      name  = "SPRING_DATASOURCE_USERNAME"
      value = module.rds_db.db_usernames["db_app_user"]
    },
    {
      name  = "SPRING_DATASOURCE_URL"
      value = "jdbc:mysql://db-${local.project}.${local.env}.bseint.io:3306/${local.database_name}?useLegacyDatetimeCode=false&serverTimezone=UTC&characterEncoding=UTF-8"
    }
  ]
}

module "budget" {
  source  = "tfe.mng.bseint.io/bestseller-ecom/budget/aws"
  version = "~> 2.0.0"

  env           = local.env
  project       = local.project
  owner         = local.owner
  vcs           = local.vcs
  cost_estimate = "150"
}

module "rds_db" {
  source         = "tfe.mng.bseint.io/bestseller-ecom/rds-aurora-mysql/aws"
  version        = "~> 2.0.0"
  env            = local.env
  project        = local.project
  owner          = local.owner
  vcs            = local.vcs
  instance_class = "db.t4g.medium"

  database_name                  = local.database_name
  engine_version                 = "8.0.mysql_aurora.3.08.2"
  cluster_instance_count         = 1
  cluster_availability_zones     = ["eu-west-1a", "eu-west-1b", "eu-west-1c"]
  backup_retention_period        = 7
  preferred_backup_window        = "01:00-09:00"
  preferred_maintenance_window   = "wed:23:00-wed:23:30"
  additional_app_user_privileges = ["ALTER ROUTINE", "CREATE ROUTINE", "REFERENCES"]
  apply_immediately              = true
  allowed_secgp_names            = ["secgp-rds-default", "${module.aws_ecs.aws_security_group["name"]}"]
}

# Project Meta Data
module "metadata" {
  source  = "tfe.mng.bseint.io/bestseller-ecom/metadata/bestseller"
  version = "~> 2.0.0"
}

# Kafka channels
module "ec2-kafka-data" {
  source  = "tfe.mng.bseint.io/bestseller-ecom/ec2-kafka-data/aws"
  version = "~> 1.3.0"
  env     = local.env
}

module "ecr_management" {
  source  = "tfe.mng.bseint.io/bestseller-ecom/ecr/aws"
  version = "~> 1.2.0"

  env     = local.env
  project = local.project
  owner   = local.owner
  vcs     = local.vcs
}

module "aws_ecs" {
  source  = "tfe.mng.bseint.io/bestseller-ecom/ecs/aws"
  version = "~> 1.2.0"

  env          = local.env
  project      = local.project
  project_name = local.project_name
  owner        = local.owner
  vcs          = local.vcs

  desired_count = 1

  alb_listener_port             = 443
  alb_listener_rule_path        = "/${local.project}/*"
  cluster_name                  = "logistics-${local.env}"
  container_port                = 8080
  container_image               = "${local.aws_account_id}.dkr.ecr.eu-west-1.amazonaws.com/${local.project}:master"
  container_cpu_allocation      = 128
  container_memory_allocation   = 840 # a bit higher than XMX settings
  container_health_check_path   = "/actuator/health"
  container_environment_vars    = concat(local.datadog_env_vars, local.app_env_vars, local.db_env_vars)
  container_environment_secrets = concat(local.db_env_secrets, local.app_env_secrets)

}

module "secretsmanager-secret-administrator" {
  source  = "tfe.mng.bseint.io/bestseller-ecom/secretsmanager-secret/aws"
  version = "~> 1.1.0"

  env     = local.env
  owner   = local.owner
  project = local.project
  vcs     = local.vcs

  secret_name        = "ADMINISTRATOR_ENDPOINT_PASSWORD"
  secret_description = "Password used to access resource protected with Admin role."
  secret_string      = "AQECAHiJQdttwbIfqPJh8ZRJZlr7Pkf2H2IjkJ9tZViR1ZJDzAAAAHYwdAYJKoZIhvcNAQcGoGcwZQIBADBgBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDGvzjDBkYpN5demHMgIBEIAzbCMwbcQ38YCpQFYW3sFH4IUQ/uAWidK7q0N0m9RtnGmemxYfAEEAwWqWY4e9gkLIkFpG"

}

module "secretsmanager-secret-monitoring" {
  source  = "tfe.mng.bseint.io/bestseller-ecom/secretsmanager-secret/aws"
  version = "~> 1.1.0"

  env     = local.env
  owner   = local.owner
  project = local.project
  vcs     = local.vcs

  secret_name        = "MONITOR_ENDPOINT_PASSWORD"
  secret_description = "Password used to monitor."
  secret_string      = "AQECAHiJQdttwbIfqPJh8ZRJZlr7Pkf2H2IjkJ9tZViR1ZJDzAAAAHYwdAYJKoZIhvcNAQcGoGcwZQIBADBgBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDKlr83RhVqEIr64+ewIBEIAznll/TR0RDmkhiVwpZAvxvy1T8J6fvAATbImU8o/jvct31chH4ApfGnSazqaWWlixjNoa"

}

module "secretsmanager-secret-dd-application-key" {
  source  = "tfe.mng.bseint.io/bestseller-ecom/secretsmanager-secret/aws"
  version = "~> 1.1.0"

  env     = local.env
  owner   = local.owner
  project = local.project
  vcs     = local.vcs

  secret_name        = "dd_application_key"
  secret_description = "DD Application Key."
  secret_string      = "AQECAHiJQdttwbIfqPJh8ZRJZlr7Pkf2H2IjkJ9tZViR1ZJDzAAAAIcwgYQGCSqGSIb3DQEHBqB3MHUCAQAwcAYJKoZIhvcNAQcBMB4GCWCGSAFlAwQBLjARBAwgywqI9OTAHGXD0WUCARCAQ1o+BETwki9uXRsfV4dkmrQvcWCrkNqRCKehfu/MWqutF1xFKRhyue9irGOMBjKt/x4PwZhcXD9EIxrMOrS3oq7IUPc="
}

data "aws_secretsmanager_secret_version" "datadog_api_key" {
  secret_id = "DATADOG_API_KEY"
}
