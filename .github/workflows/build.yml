name: Build
on:
  workflow_dispatch:
  push:
    branches:
      - master
    paths-ignore:
      - 'infra/**'
      - 'README.md'
      - '**/README.md'
      
  pull_request:
    branches:
      - master
    paths-ignore:
      - 'infra/**'
      - 'README.md'
      - '**/README.md'

jobs:
  start-runner:
    secrets: inherit
    uses: bestseller-ecom/bestseller-pipelines/.github/workflows/github-runner.yml@v2
    with:
      owner: fulfilment 
      project-name: oms
      mode: start
      ec2-instance-type: c7i.xlarge

  build:
    needs: ["start-runner"]
    secrets: inherit
    uses: bestseller-ecom/bestseller-pipelines/.github/workflows/build-ecs-java.yml@v2
    with:
      project-name: ors
      environment: acc
      java-version: 11
      build-tool: gradle
      runs-on: ${{ needs.start-runner.outputs.label }}

  stop-runner:
    if: always()
    needs: ["start-runner", "build"]
    secrets: inherit
    uses: bestseller-ecom/bestseller-pipelines/.github/workflows/github-runner.yml@v2
    with:
      mode: stop
      label: ${{ needs.start-runner.outputs.label }}
      ec2-instance-id: ${{ needs.start-runner.outputs.ec2-instance-id }}
     
