name: Deploy-ors-service
on:
  workflow_dispatch:
    inputs:
      environment:
        type: choice
        description: Select Environment to deploy in
        options:
          - OrsAcc
          - OrsProd
        required: true
jobs:
  DeployAcc:
    secrets: inherit
    if: github.event.inputs.environment == 'OrsAcc'
    uses: bestseller-ecom/bestseller-pipelines/.github/workflows/deploy-ecs.yml@v2
    with:
      owner: fulfilment
      project-name: ors
      cluster-name: logistics-acc
      environment: acc
    
  PushProdImage:
    secrets: inherit
    if: github.event.inputs.environment == 'OrsProd' && github.ref == 'refs/heads/master'
    uses: bestseller-ecom/bestseller-pipelines/.github/workflows/push-ecs.yml@v2
    with:
      project-name: ors

  DeployProd:
    secrets: inherit
    if: github.event.inputs.environment == 'OrsProd' && github.ref == 'refs/heads/master'
    needs: [PushProdImage]
    uses: bestseller-ecom/bestseller-pipelines/.github/workflows/deploy-ecs.yml@v2
    with:
      owner: fulfilment
      project-name: ors
      cluster-name: logistics-prod
      environment: prod