<?xml version="1.0"?>
<ruleset name="Custom ruleset for PMD"
         xmlns="http://pmd.sourceforge.net/ruleset/2.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://pmd.sourceforge.net/ruleset/2.0.0
	http://pmd.sourceforge.net/ruleset_2_0_0.xsd">

    <description>
        This custom ruleset checks the code for problems.
    </description>

    <rule ref="category/java/design.xml/UseUtilityClass">
        <properties>
            <property name="violationSuppressXPath"
                      value="//ClassOrInterfaceDeclaration/preceding-sibling::Annotation/MarkerAnnotation/Name[@Image='SpringBootApplication']"/>
        </properties>
    </rule>


    <rule ref="category/java/bestpractices.xml">
        <!-- Rule disabled - We are using slf4j with parametrized logging, so log guarding has very little benefit.-->
        <exclude name="GuardLogStatement"/>
        <exclude name="JUnitTestContainsTooManyAsserts"/>
        <exclude name="JUnitTestsShouldIncludeAssert"/>
    </rule>
    <rule ref="category/java/bestpractices.xml/UnusedPrivateField">
        <properties>
            <property name="violationSuppressXPath"
                      value="//ClassOrInterfaceBodyDeclaration[Annotation/MarkerAnnotation/Name[@Image = 'Mock']]" />
        </properties>
    </rule>

    <rule ref="category/java/codestyle.xml">
        <exclude name="OnlyOneReturn"/>
        <exclude name="ShortVariable"/>
        <exclude name="AbstractNaming"/>
        <exclude name="LocalVariableCouldBeFinal"/>
        <exclude name="MethodArgumentCouldBeFinal"/>
        <exclude name="AtLeastOneConstructor"/>
        <exclude name="UnnecessaryAnnotationValueElement"/>
    </rule>
    <rule ref="category/java/codestyle.xml/LongVariable">
        <properties>
            <property name="minimum" value="63"/>
        </properties>
    </rule>
    <rule ref="category/java/codestyle.xml/MethodNamingConventions">
        <properties>
            <property name="junit4TestPattern" value="([a-z][a-zA-Z0-9]+_?){3}"/>
        </properties>
    </rule>
    <rule ref="category/java/codestyle.xml/ClassNamingConventions">
        <properties>
            <property name="utilityClassPattern" value="[A-Z][a-zA-Z0-9]+(Utils?|Helper|Constants|Generator)" />
        </properties>
    </rule>
    <rule ref="category/java/codestyle.xml/TooManyStaticImports">
        <properties>
            <property name="maximumStaticImports" value="20" />
        </properties>
    </rule>
    <rule ref="category/java/codestyle.xml/LinguisticNaming">
        <properties>
            <property name="ignoredAnnotations" value="java.lang.Override|org.junit.Test" />
        </properties>
    </rule>

    <rule ref="category/java/design.xml">
        <exclude name="LoosePackageCoupling"/>
        <exclude name="LawOfDemeter"/>
        <exclude name="ImmutableField"/>
        <exclude name="SignatureDeclareThrowsException"/>
        <exclude name="AvoidCatchingGenericException"/>
        <exclude name="ExcessiveImports"/>
    </rule>
    <rule ref="category/java/design.xml/NcssCount">
        <properties>
            <property name="methodReportLevel" value="30"/>
        </properties>
    </rule>
    <rule ref="category/java/design.xml/TooManyMethods">
        <properties>
            <property name="maxmethods" value="30" />
        </properties>
    </rule>

    <rule ref="category/java/documentation.xml/CommentRequired">
        <properties>
            <property name="fieldCommentRequirement" value="Ignored"/>
            <property name="violationSuppressXPath"
                      value="//MethodDeclaration/../Annotation/MarkerAnnotation/Name[@Image='Test']"/>
        </properties>
    </rule>
    <rule ref="category/java/documentation.xml/CommentSize">
        <properties>
            <property name="maxLines" value="20"/>
            <property name="maxLineLength" value="150"/>
        </properties>
    </rule>

    <rule ref="category/java/errorprone.xml">
        <exclude name="BeanMembersShouldSerialize"/>
        <exclude name="LoggerIsNotStaticFinal"/>
        <exclude name="DataflowAnomalyAnalysis"/>
        <exclude name="MissingSerialVersionUID"/>
        <exclude name="InvalidLogMessageFormat"/>
    </rule>

    <rule ref="category/java/multithreading.xml">
        <exclude name="UseConcurrentHashMap"/>
    </rule>

    <rule ref="category/java/performance.xml">
        <exclude name="AvoidInstantiatingObjectsInLoops"/>
    </rule>
</ruleset>
