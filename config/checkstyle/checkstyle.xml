<?xml version="1.0"?>
<!DOCTYPE module PUBLIC
        "-//Puppy Crawl//DTD Check Configuration 1.3//EN"
        "http://www.puppycrawl.com/dtds/configuration_1_3.dtd">

<!--

  Checkstyle configuration that checks the sun coding conventions from:

    - the Java Language Specification at
      http://java.sun.com/docs/books/jls/second_edition/html/index.html

    - the Sun Code Conventions at http://java.sun.com/docs/codeconv/

    - the Javadoc guidelines at
      http://java.sun.com/j2se/javadoc/writingdoccomments/index.html

    - the JDK Api documentation http://java.sun.com/j2se/docs/api/index.html

    - some best practices

  Checkstyle is very configurable. Be sure to read the documentation at
  http://checkstyle.sf.net (or in your downloaded distribution).

  Most Checks are configurable, be sure to consult the documentation.

  To completely disable a check, just comment it out or delete it from the file.

  Finally, it is worth reading the documentation.

-->

<!--
This file is based on sun_checks.xml with customizations marked with ***
-->

<module name="Checker">
    <!--
        If you set the basedir property below, then all reported file
        names will be relative to the specified directory. See
        http://checkstyle.sourceforge.net/5.x/config.html#Checker

        <property name="basedir" value="${basedir}"/>
    -->

    <property name="fileExtensions" value="java, properties, xml, json"/>

    <!--
        Filter SuppressionFilter rejects audit events for Check errors according to a suppressions XML document in a
        file. If there is no configured suppressions file or the optional is set to true and suppressions file was not
        found the Filter accepts all audit events.
    -->
    <module name="SuppressionFilter">
        <property name="file" value="${config_loc}/suppressions.xml"/>
    </module>

    <!-- Checks that a package-info.java file exists for each package.     -->
    <!-- See http://checkstyle.sf.net/config_javadoc.html#JavadocPackage -->
    <!--
    *** Disabled JavadocPackage
    -->
    <!--<module name="JavadocPackage"/>-->

    <!-- Checks whether files end with a new line.                        -->
    <!-- See http://checkstyle.sf.net/config_misc.html#NewlineAtEndOfFile -->
    <module name="NewlineAtEndOfFile">
        <property name="lineSeparator" value="lf_cr_crlf"/>
    </module>
    <!-- Checks that property files contain the same keys.         -->
    <!-- See http://checkstyle.sf.net/config_misc.html#Translation -->
    <module name="Translation"/>

    <!-- Checks for Size Violations.                    -->
    <!-- See http://checkstyle.sf.net/config_sizes.html -->
    <module name="FileLength">
        <property name="max" value="500"/>
    </module>

    <!-- Checks for whitespace                               -->
    <!-- See http://checkstyle.sf.net/config_whitespace.html -->
    <module name="FileTabCharacter"/>

    <!-- Miscellaneous other checks.                   -->
    <!-- See http://checkstyle.sf.net/config_misc.html -->
    <module name="RegexpSingleline">
        <property name="format" value="\s+$"/>
        <property name="minimum" value="0"/>
        <property name="maximum" value="0"/>
        <property name="message" value="Line has trailing spaces."/>
    </module>

    <!-- Checks for Headers                                -->
    <!-- See http://checkstyle.sf.net/config_header.html   -->
    <!-- <module name="Header"> -->
    <!--   <property name="headerFile" value="${checkstyle.header.file}"/> -->
    <!--   <property name="fileExtensions" value="java"/> -->
    <!-- </module> -->


    <!-- Checks for Size Violations.                    -->
    <!-- See http://checkstyle.sf.net/config_sizes.html -->
    <module name="LineLength">
        <property name="max" value="150"/>
    </module>

    <module name="TreeWalker">

        <!-- Checks for Javadoc comments.                     -->
        <!-- See http://checkstyle.sf.net/config_javadoc.html -->
        <module name="MissingJavadocMethod">
            <property name="allowedAnnotations"
                      value="Override,Before,Test"/> <!-- *** no comment needed for tests and override methods -->
            <property name="allowMissingPropertyJavadoc" value="true"/>
            <property name="minLineCount" value="2"/>
        </module>
        <module name="JavadocMethod">
            <property name="allowThrowsTagsForSubclasses" value="true"/>
            <property name="scope" value="package"/> <!-- *** don't document private methods -->
            <property name="allowMissingParamTags" value="true"/>
            <property name="allowMissingThrowsTags" value="true"/>
            <property name="allowMissingReturnTag" value="true"/>
        </module>
        <module name="JavadocStyle"/>

        <!-- Checks for Naming Conventions.                  -->
        <!-- See http://checkstyle.sf.net/config_naming.html -->
        <module name="ConstantName"/>
        <module name="LocalFinalVariableName"/>
        <module name="LocalVariableName"/>
        <module name="MemberName"/>
        <module name="MethodName"> <!-- *** using Google flavor to permit underscore in test method names -->
            <property name="format" value="^[a-z][a-z0-9][a-zA-Z0-9_]*$"/>
            <message key="name.invalidPattern"
                     value="Method name ''{0}'' must match pattern ''{1}''."/>
        </module>
        <module name="PackageName"/>
        <module name="ParameterName"/>
        <module name="StaticVariableName"/>
        <module name="TypeName"/>

        <module name="RegexpSinglelineJava">
            <!-- Two or more whitespaces except those in the beginning of the line
             and those followed by a " -->
            <!-- It allows for whitespaces inside string literals -->
            <property name="format" value="(?&lt;!^\s*)\s\s+(?&lt;=&quot;})"/>
            <property name="message" value="Superfluous whitespace"/>
            <property name="ignoreComments" value="true"/>
        </module>

        <!-- Checks for imports                              -->
        <!-- See http://checkstyle.sf.net/config_imports.html -->
        <module name="AvoidStarImport">
            <property
                    name="excludes"
                    value="java.awt,java.io,java.util,javax.swing,java.lang.Math,org.junit.Assert,org.mockito.Mockito"/> <!-- *** allowing star import for some packages -->
        </module>
        <module name="IllegalImport"/> <!-- defaults to sun.* packages -->
        <module name="RedundantImport"/>
        <module name="UnusedImports"/>
        <module name="MethodLength"/>
        <module name="ParameterNumber">
            <property name="max" value="4"/>
            <property name="tokens" value="METHOD_DEF"/>
        </module>

        <!-- Checks for whitespace                               -->
        <!-- See http://checkstyle.sf.net/config_whitespace.html -->
        <module name="EmptyForIteratorPad"/>
        <module name="GenericWhitespace"/>
        <module name="MethodParamPad"/>
        <module name="NoWhitespaceAfter"/>
        <module name="NoWhitespaceBefore"/>
        <module name="OperatorWrap"/>
        <module name="ParenPad"/>
        <module name="TypecastParenPad"/>
        <module name="WhitespaceAfter"/>
        <module name="WhitespaceAround"/>

        <!-- Modifier Checks                                    -->
        <!-- See http://checkstyle.sf.net/config_modifiers.html -->
        <module name="ModifierOrder"/>
        <module name="RedundantModifier"/>

        <!-- Checks for blocks. You know, those {}'s         -->
        <!-- See http://checkstyle.sf.net/config_blocks.html -->
        <module name="AvoidNestedBlocks"/>
        <module name="EmptyBlock"/>
        <module name="LeftCurly"/>
        <module name="NeedBraces"/>
        <module name="RightCurly"/>

        <!-- Checks for common coding problems               -->
        <!-- See http://checkstyle.sf.net/config_coding.html -->
        <!-- *** permiting inline conditionals
        <module name="AvoidInlineConditionals"/>
        -->
        <module name="EmptyStatement"/>
        <module name="EqualsHashCode"/>
        <module name="HiddenField">
            <property name="ignoreConstructorParameter"
                      value="true"/> <!-- *** allow same name for constructor parameters -->
            <property name="ignoreSetter" value="true"/> <!-- *** allow same name for setter parameter -->
        </module>
        <module name="IllegalInstantiation"/>
        <module name="InnerAssignment"/>
        <module name="MagicNumber">
            <property name="ignoreFieldDeclaration" value="true"/>
            <property name="ignoreAnnotation" value="true"/>
        </module>
        <module name="MissingSwitchDefault"/>
        <module name="NestedIfDepth"/> <!-- *** added if-depth check -->
        <module name="SimplifyBooleanExpression"/>
        <module name="SimplifyBooleanReturn"/>
        <module name="CovariantEquals"/> <!-- *** added module -->
        <module name="DeclarationOrder"/> <!-- *** added module -->
        <module name="DefaultComesLast"/> <!-- *** added module -->
        <module name="EqualsAvoidNull"/> <!-- *** added module -->
        <module name="ExplicitInitialization"/> <!-- *** added module -->
        <module name="FallThrough"/> <!-- *** added module -->
        <module name="IllegalThrows"/> <!-- *** added module -->
        <module name="IllegalTokenText"> <!-- *** added module -->
            <property name="tokens" value="STRING_LITERAL"/>
            <property name="format" value="teh"/>
        </module>
        <module name="IllegalType"/> <!-- *** added module -->
        <module name="MultipleVariableDeclarations"/> <!-- *** added module -->
        <module name="MultipleStringLiterals" />  <!-- *** added module -->
        <module name="NestedForDepth"/> <!-- *** added module -->
        <module name="NestedTryDepth"/> <!-- *** added module -->
        <module name="NoClone"/> <!-- *** added module -->
        <module name="OneStatementPerLine"/> <!-- *** added module -->
        <module name="OverloadMethodsDeclarationOrder"/> <!-- *** added module -->
        <module name="PackageDeclaration"/> <!-- *** added module -->
        <module name="ParameterAssignment"/> <!-- *** added module -->
        <module name="StringLiteralEquality"/> <!-- *** added module -->
        <module name="UnnecessaryParentheses"/> <!-- *** added module -->

        <!-- Checks for class design                         -->
        <!-- See http://checkstyle.sf.net/config_design.html -->
        <!--<module name="DesignForExtension"/>--> <!-- *** disabled rule -->
        <module name="FinalClass"/>
        <module name="InterfaceIsType"/>
        <module name="VisibilityModifier"/>

        <!-- Miscellaneous other checks.                   -->
        <!-- See http://checkstyle.sf.net/config_misc.html -->
        <module name="ArrayTypeStyle"/>
        <!--<module name="FinalParameters"/> *** disabled module -->
        <module name="TodoComment">
            <property name="format" value="(TODO)|(FIXME)"/>
        </module>
        <module name="UpperEll"/>

        <module name="CommentsIndentation"/> <!-- *** added module -->
        <module name="EmptyLineSeparator"> <!-- *** added module -->
            <property name="allowNoEmptyLineBetweenFields" value="true"/>
            <property name="allowMultipleEmptyLines" value="false"/>
        </module>

        <module name="AvoidStarImport"> <!-- *** added module -->
            <property name="allowClassImports" value="false"/>
            <property name="allowStaticMemberImports" value="false"/>
        </module>

        <module name="AbbreviationAsWordInName"/> <!-- *** added module -->
        <module name="AnnotationUseStyle"/> <!-- *** added module -->
        <module name="AvoidNestedBlocks"/> <!-- *** added module -->
        <module name="BooleanExpressionComplexity"/> <!-- *** added module -->
        <module name="CatchParameterName"/> <!-- *** added module -->
        <module name="ClassTypeParameterName"/> <!-- *** added module -->
        <module name="EmptyCatchBlock"/> <!-- *** added module -->
        <module name="EmptyForInitializerPad"/> <!-- *** added module -->
        <module name="IllegalCatch">
            <property name="illegalClassNames" value="java.lang.Throwable, java.lang.RuntimeException, java.lang.NullPointerException"/>
        </module> <!-- *** added module -->
        <module name="InterfaceTypeParameterName" /><!-- *** added module -->
        <module name="MissingOverride" /><!-- *** added module -->
        <module name="ModifiedControlVariable">
            <property name="skipEnhancedForLoopVariable" value="true"/>
        </module>
        <module name="MutableException" /><!-- *** added module -->
        <module name="OneTopLevelClass" /><!-- *** added module -->
        <module name="OuterTypeFilename" /><!-- *** added module -->
        <module name="OuterTypeNumber" /><!-- *** added module -->
        <module name="SingleLineJavadoc" /><!-- *** added module -->
        <module name="SummaryJavadoc" /><!-- *** added module -->
        <module name="ThrowsCount">
            <property name="max" value="2"/>
            <property name="ignorePrivateMethods" value="false"/>
        </module>

        <!--<module name="JavaNCSS" /> *** added module -->
    </module>

</module>
