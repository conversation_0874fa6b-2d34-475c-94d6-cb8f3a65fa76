#! /bin/bash -e

echo Bash version: $BASH_VERSION


GRADLE_OPTIONS='--console=plain --no-daemon --full-stacktrace --info'
export GRADLE_COMMAND="gradle $GRADLE_OPTIONS clean build jacocoTestCoverageVerification"

# Pick either local or BSE credentials whichever are available, print error otherwise
export ORG_GRADLE_PROJECT_gitbubUsername="${GITHUB_USER=${ORG_GRADLE_PROJECT_githubUsername?}}"
export ORG_GRADLE_PROJECT_githubPassword="${GITHUB_TOKEN=${ORG_GRADLE_PROJECT_githubPassword?}}"

export KAFKA_HOST=kafka

export USERID=$(id -u)
export GID=$(id -g)
export COMPOSE_FILE="$(dirname $0)/docker-compose.yml"
export COMPOSE_PROJECT_NAME=ors
COMPOSE_OPTIONS=(--abort-on-container-exit --exit-code-from gradle --no-build)

trap send_service_messages EXIT

function send_service_messages {
  if [ $? -ne 0 ]
  then
    echo "##teamcity[buildProblem description='Gradle build failed. See build artifacts']"
  fi

  echo "##teamcity[publishArtifacts 'build/reports/']"
}

docker-compose down --volumes
# keeping `app` down or else it would cry out for the missing Jar file
docker-compose up $COMPOSE_OPTIONS zookeeper kafka mysql gradle
