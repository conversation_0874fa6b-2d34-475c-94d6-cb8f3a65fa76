#! /bin/bash -e

echo Bash version: $BASH_VERSION


MAVEN_OPTIONS='-B -V'
export MAVEN_COMMAND="mvn $MAVEN_OPTIONS clean verify"

# Pick either local or BSE credentials whichever are available, print error otherwise
export GITHUB_USERNAME="${GITHUB_USER=${GITHUB_USERNAME?}}"
export GITHUB_PASSWORD="${GITHUB_TOKEN=${GITHUB_PASSWORD?}}"

export KAFKA_HOST=kafka

export USERID=$(id -u)
export GID=$(id -g)
export COMPOSE_FILE="$(dirname $0)/docker-compose.yml"
export COMPOSE_PROJECT_NAME=ors
COMPOSE_OPTIONS=(--abort-on-container-exit --exit-code-from maven --no-build)

trap send_service_messages EXIT

function send_service_messages {
  if [ $? -ne 0 ]
  then
    echo "##teamcity[buildProblem description='Maven build failed. See build artifacts']"
  fi

  echo "##teamcity[publishArtifacts 'target/site/']"
}

docker-compose down --volumes
# keeping `app` down or else it would cry out for the missing Jar file
docker-compose up $COMPOSE_OPTIONS zookeeper kafka mysql maven
