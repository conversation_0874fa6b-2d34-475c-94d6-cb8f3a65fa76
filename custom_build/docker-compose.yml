---
version: "3.1"

services:
  zookeeper:
    image: 381841683508.dkr.ecr.eu-west-1.amazonaws.com/bse-zookeeper:1
    ports:
      - "2181:2181"

  kafka:
    image: 381841683508.dkr.ecr.eu-west-1.amazonaws.com/bse-kafka:2.6.0
    depends_on:
      - zookeeper
    ports:
      - 9092:9092
    environment:
      KAFKA_BROKER_ID: "1"
      KAFKA_ADVERTISED_HOST_NAME: localhost
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://${KAFKA_HOST-localhost}:9092
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181

  mysql:
    image: mysql/mysql-server:8.0.32
    command: [ "--max_connections=1000" ]
    ports:
      - 3307:3306
    environment:
      - MYSQL_ROOT_PASSWORD=WvkE5wkwNQUX4nnj
      - MYSQL_DATABASE=ors
      - MYSQL_USER=orsuser
      - MYSQL_PASSWORD=S3D5EjZb25MS6ZYM
  gradle:
    image: gradle:6.3.0-jdk13
    user: $USERID:$GID
    depends_on:
      - kafka
      - mysql
    volumes:
      - ..:/home/<USER>/project/
    working_dir: /home/<USER>/project/
    command: ${GRADLE_COMMAND-sleep infinity}
    environment:
      ORG_GRADLE_PROJECT_githubUsername: $ORG_GRADLE_PROJECT_githubUsername
      ORG_GRADLE_PROJECT_githubPassword: $ORG_GRADLE_PROJECT_githubPassword
      KAFKA_BROKER_LIST: kafka
      SPRING_DATASOURCE_URL: ***************************