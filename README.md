# Order Routing Service

This service is responsible for routing the orders to the respective fulfillment nodes (warehouses) based on different business rules.

The main responsibilities of the service are:
- Group order line quantities into order parts
- Submit order parts for fulfillment
- Notify OMS with order parts status updates

[Documentation](https://bestseller.jira.com/wiki/x/tK4yDQ)

## Prerequisites

1. Personal account in [BSE repository manager](https://nexus01.bs-ecommerce.com)
   (exposed via `ORG_GRADLE_PROJECT_nexusUsername` and `ORG_GRADLE_PROJECT_nexusPassword` environment variables)
2. Java 13, Gradle

       $ gradle -v
       Gradle 6.2
       JVM:          13.0.2 (Oracle Corporation 13.0.2+8)

3. Docker Compose

## Running the project

### Locally

 1. Start the components
 
        docker-compose up -d
 
 3. Run the app

        gradle bootRun 


## Running unit tests

### Locally

    gradle build jacocoTestCoverageVerification -x integrationTest

## Running integration tests

### Locally

    docker-compose up -d
    gradle integrationTest

### In Docker

    GRADLE_COMMAND='gradle integrationTest' KAFKA_HOST=kafka docker-compose up --abort-on-container-exit --exit-code-from gradle
